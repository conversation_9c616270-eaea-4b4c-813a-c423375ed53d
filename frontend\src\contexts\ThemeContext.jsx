import { createContext, useContext, useState, useEffect } from 'react';

// Tạo context cho theme
const ThemeContext = createContext(null);

// Provider component
export const ThemeProvider = ({ children }) => {
  // Kiểm tra xem người dùng đã chọn dark mode trước đó chưa
  const [darkMode, setDarkMode] = useState(() => {
    // Kiểm tra localStorage
    const savedTheme = localStorage.getItem('theme');
    
    // Kiểm tra cài đặt hệ thống
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    return savedTheme === 'dark' || (!savedTheme && prefersDark);
  });

  // Cập nhật class cho thẻ html khi darkMode thay đổi
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  }, [darkMode]);

  // Hàm chuyển đổi giữa light mode và dark mode
  const toggleDarkMode = () => {
    setDarkMode(prevMode => !prevMode);
  };

  return (
    <ThemeContext.Provider value={{ darkMode, toggleDarkMode }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Hook để sử dụng ThemeContext
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme phải được sử dụng trong ThemeProvider');
  }
  return context;
};
