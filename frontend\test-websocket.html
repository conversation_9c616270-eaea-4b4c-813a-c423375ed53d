<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <script src="https://cdn.socket.io/4.8.1/socket.io.min.js"></script>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="logs"></div>

    <script>
        const statusDiv = document.getElementById('status');
        const logsDiv = document.getElementById('logs');

        function log(message) {
            console.log(message);
            const p = document.createElement('p');
            p.textContent = new Date().toLocaleTimeString() + ': ' + message;
            logsDiv.appendChild(p);
        }

        // Test WebSocket connection
        log('Attempting to connect to WebSocket server...');
        
        const socket = io('http://localhost:5000', {
            transports: ['websocket', 'polling'],
            timeout: 20000,
            forceNew: true,
            reconnection: true,
            reconnectionAttempts: 5,
            reconnectionDelay: 1000,
            reconnectionDelayMax: 5000
        });

        socket.on('connect', () => {
            statusDiv.textContent = 'Connected ✅';
            statusDiv.style.color = 'green';
            log('✅ Connected to WebSocket server');
            log('Socket ID: ' + socket.id);
        });

        socket.on('disconnect', (reason) => {
            statusDiv.textContent = 'Disconnected ❌';
            statusDiv.style.color = 'red';
            log('❌ Disconnected: ' + reason);
        });

        socket.on('connect_error', (error) => {
            statusDiv.textContent = 'Connection Error ❌';
            statusDiv.style.color = 'red';
            log('❌ Connection error: ' + error.message);
        });

        socket.on('reconnect', (attemptNumber) => {
            log('🔄 Reconnected after ' + attemptNumber + ' attempts');
        });

        socket.on('reconnect_attempt', (attemptNumber) => {
            log('🔄 Reconnection attempt ' + attemptNumber);
        });

        socket.on('reconnect_error', (error) => {
            log('❌ Reconnection error: ' + error.message);
        });

        socket.on('reconnect_failed', () => {
            log('❌ Reconnection failed');
        });

        // Test authentication
        setTimeout(() => {
            log('Testing authentication...');
            socket.emit('authenticate', { token: 'test-token' });
        }, 2000);

        socket.on('authenticated', (data) => {
            log('✅ Authenticated: ' + JSON.stringify(data));
        });

        socket.on('authentication_error', (error) => {
            log('❌ Authentication error: ' + error.message);
        });

        // Test highlight expiry events
        socket.on('highlight_expired', (data) => {
            log('📢 Highlight expired event: ' + JSON.stringify(data));
        });

        socket.on('room_highlight_updated', (data) => {
            log('📢 Room highlight updated event: ' + JSON.stringify(data));
        });

        socket.on('highlight_activated', (data) => {
            log('📢 Highlight activated event: ' + JSON.stringify(data));
        });
    </script>
</body>
</html>
