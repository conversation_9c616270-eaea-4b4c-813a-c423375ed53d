import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import Card from '../components/Card'
import Button from '../components/Button'
import useRooms from '../hooks/useRooms'

const HomePageSimple = () => {
  const { rooms, isLoading, error, fetchRooms } = useRooms();
  const [highlightedRooms, setHighlightedRooms] = useState([]);
  const [regularRooms, setRegularRooms] = useState([]);

  // State cho image slider
  const [currentSlide, setCurrentSlide] = useState(0);

  // Slider images
  const sliderImages = [
    'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2080&q=80',
    'https://images.unsplash.com/photo-1484154218962-a197022b5858?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80'
  ];

  // Load rooms khi component mount
  useEffect(() => {
    const loadRooms = async () => {
      try {
        console.log('[HomePageSimple] Loading rooms...');
        
        // Load highlighted rooms
        const highlightedResponse = await fetchRooms({
          status: 'available',
          isHighlighted: true,
          limit: 100,
          sort: '-createdAt'
        });
        
        if (highlightedResponse.success) {
          setHighlightedRooms(highlightedResponse.data.rooms);
          console.log('[HomePageSimple] Loaded highlighted rooms:', highlightedResponse.data.rooms.length);
        }

        // Load regular rooms
        const regularResponse = await fetchRooms({
          status: 'available',
          isHighlighted: false,
          limit: 15,
          sort: '-createdAt'
        });
        
        if (regularResponse.success) {
          setRegularRooms(regularResponse.data.rooms.slice(0, 6));
          console.log('[HomePageSimple] Loaded regular rooms:', regularResponse.data.rooms.length);
        }
      } catch (error) {
        console.error('[HomePageSimple] Error loading rooms:', error);
      }
    };

    loadRooms();
  }, [fetchRooms]);

  // Auto slider
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % sliderImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [sliderImages.length]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 text-xl">Có lỗi xảy ra: {error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Hero Slider Section */}
      <section className="relative h-96 md:h-[500px] overflow-hidden">
        {sliderImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={image}
              alt={`Slide ${index + 1}`}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          </div>
        ))}
        
        <div className="absolute inset-0 flex items-center justify-center text-white text-center">
          <div className="max-w-4xl mx-auto px-4">
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              Tìm Phòng Trọ Lý Tưởng
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Hàng ngàn phòng trọ chất lượng, giá cả phải chăng đang chờ bạn
            </p>
            <Link to="/search">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg">
                Bắt Đầu Tìm Kiếm
              </Button>
            </Link>
          </div>
        </div>

        {/* Slider indicators */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {sliderImages.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentSlide ? 'bg-white' : 'bg-white bg-opacity-50'
              }`}
            />
          ))}
        </div>
      </section>

      {/* Highlighted Rooms Section */}
      {highlightedRooms.length > 0 && (
        <section className="py-16 bg-gradient-to-r from-yellow-50 to-orange-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                🌟 Phòng Trọ Nổi Bật
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Những phòng trọ được đánh giá cao và có nhiều tiện ích
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {highlightedRooms.slice(0, 6).map((room) => (
                <Card key={room._id} room={room} />
              ))}
            </div>
            
            {highlightedRooms.length > 6 && (
              <div className="text-center mt-8">
                <Link to="/search?highlighted=true">
                  <Button variant="outline" size="lg">
                    Xem Tất Cả Phòng Nổi Bật
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </section>
      )}

      {/* Regular Rooms Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Phòng Trọ Mới Nhất
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Cập nhật liên tục những phòng trọ mới nhất
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {regularRooms.map((room) => (
              <Card key={room._id} room={room} />
            ))}
          </div>
          
          <div className="text-center mt-8">
            <Link to="/search">
              <Button variant="outline" size="lg">
                Xem Tất Cả Phòng Trọ
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Bạn có phòng trọ cần cho thuê?</h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Đăng tin ngay hôm nay để tiếp cận hàng ngàn người đang tìm kiếm phòng trọ mỗi ngày.
          </p>
          <Link to="/post-room">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 text-lg">
              Đăng Tin Miễn Phí
            </Button>
          </Link>
        </div>
      </section>
    </div>
  )
}

export default HomePageSimple
