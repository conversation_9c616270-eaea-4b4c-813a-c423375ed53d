import apiClient from './apiClient';
import mockPackages from '../data/mockPackages';

/**
 * Service xử lý các thao tác liên quan đến gói tin nổi bật
 *
 * Lưu ý: Hiện tại đang sử dụng dữ liệu mẫu cho các gói tin nổi bật
 * vì API endpoint chưa được triển khai trên backend.
 */
const packageService = {
  /**
   * L<PERSON>y danh sách gói tin nổi bật
   * @returns {Promise} Promise chứa kết quả trả về
   */
  getPackages: async () => {
    try {
      // Sử dụng dữ liệu mẫu thay vì gọi API
      // const response = await apiClient.get('/packages');
      // return response.data;

      // Giả lập độ trễ của API (500ms)
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        data: mockPackages,
        message: '<PERSON><PERSON><PERSON> danh sách gói tin nổi bật thành công'
      };
    } catch (error) {
      console.error('Error fetching packages:', error);
      throw error;
    }
  },

  /**
   * L<PERSON>y thông tin chi tiết gói tin nổi bật
   * @param {string} id ID của gói tin nổi bật
   * @returns {Promise} Promise chứa kết quả trả về
   */
  getPackageById: async (id) => {
    try {
      // Sử dụng dữ liệu mẫu thay vì gọi API
      // const response = await apiClient.get(`/packages/${id}`);
      // return response.data;

      // Giả lập độ trễ của API (300ms)
      await new Promise(resolve => setTimeout(resolve, 300));

      const packageData = mockPackages.find(pkg => pkg._id === id);

      if (packageData) {
        return {
          success: true,
          data: packageData,
          message: 'Lấy thông tin gói tin nổi bật thành công'
        };
      } else {
        throw new Error('Không tìm thấy gói tin nổi bật');
      }
    } catch (error) {
      console.error(`Error fetching package with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Tạo yêu cầu thanh toán VNPay
   * @param {Object} data Dữ liệu thanh toán
   * @returns {Promise} Promise chứa kết quả trả về
   */
  createPayment: async (data) => {
    try {
      // Sử dụng dữ liệu mẫu thay vì gọi API
      // const response = await apiClient.post('/payments/vnpay/create', data);
      // return response.data;

      // Giả lập độ trễ của API (800ms)
      await new Promise(resolve => setTimeout(resolve, 800));

      // Tìm thông tin gói tin nổi bật
      const packageData = mockPackages.find(pkg => pkg._id === data.packageId);

      if (!packageData) {
        throw new Error('Không tìm thấy gói tin nổi bật');
      }

      // Tạo URL thanh toán giả lập
      const paymentUrl = `https://sandbox.vnpayment.vn/paymentv2/vpcpay.html?amount=${packageData.price}&orderId=${Date.now()}&roomId=${data.roomId}&packageId=${data.packageId}&returnUrl=${encodeURIComponent(data.returnUrl)}`;

      return {
        success: true,
        data: {
          paymentUrl,
          orderId: Date.now().toString(),
          amount: packageData.price,
          packageId: data.packageId,
          roomId: data.roomId
        },
        message: 'Tạo yêu cầu thanh toán thành công'
      };
    } catch (error) {
      console.error('Error creating payment:', error);
      throw error;
    }
  },

  /**
   * Xác nhận kết quả thanh toán VNPay
   * @param {Object} data Dữ liệu xác nhận thanh toán
   * @returns {Promise} Promise chứa kết quả trả về
   */
  verifyPayment: async (data) => {
    try {
      // Sử dụng dữ liệu mẫu thay vì gọi API
      // const response = await apiClient.post('/payments/vnpay/verify', data);
      // return response.data;

      // Giả lập độ trễ của API (1000ms)
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Giả lập kết quả thanh toán thành công
      const isSuccess = Math.random() > 0.2; // 80% khả năng thành công

      if (isSuccess) {
        return {
          success: true,
          data: {
            transactionId: `VNP${Date.now()}`,
            orderId: data.orderId || Date.now().toString(),
            amount: data.amount || 50000,
            paymentDate: new Date().toISOString(),
            roomId: data.roomId || 'room_123',
            packageId: data.packageId || 'pkg_vip'
          },
          message: 'Thanh toán thành công'
        };
      } else {
        return {
          success: false,
          data: null,
          message: 'Thanh toán thất bại hoặc bị hủy bởi người dùng'
        };
      }
    } catch (error) {
      console.error('Error verifying payment:', error);
      throw error;
    }
  },

  /**
   * Nâng cấp tin đăng lên gói tin nổi bật
   * @param {string} roomId ID của tin đăng
   * @param {string} packageId ID của gói tin nổi bật
   * @returns {Promise} Promise chứa kết quả trả về
   */
  upgradeRoom: async (roomId, packageId) => {
    try {
      // Sử dụng dữ liệu mẫu thay vì gọi API
      // const response = await apiClient.post(`/rooms/${roomId}/upgrade`, { packageId });
      // return response.data;

      // Giả lập độ trễ của API (600ms)
      await new Promise(resolve => setTimeout(resolve, 600));

      // Tìm thông tin gói tin nổi bật
      const packageData = mockPackages.find(pkg => pkg._id === packageId);

      if (!packageData) {
        throw new Error('Không tìm thấy gói tin nổi bật');
      }

      // Tính ngày hết hạn
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + packageData.duration);

      return {
        success: true,
        data: {
          roomId,
          packageId,
          packageType: packageData.type,
          startDate: new Date().toISOString(),
          expiryDate: expiryDate.toISOString()
        },
        message: 'Nâng cấp tin đăng thành công'
      };
    } catch (error) {
      console.error(`Error upgrading room ${roomId}:`, error);
      throw error;
    }
  },

  /**
   * Lấy lịch sử gói tin nổi bật của tin đăng
   * @param {string} roomId ID của tin đăng
   * @returns {Promise} Promise chứa kết quả trả về
   */
  getRoomPackageHistory: async (roomId) => {
    try {
      // Sử dụng dữ liệu mẫu thay vì gọi API
      // const response = await apiClient.get(`/rooms/${roomId}/packages`);
      // return response.data;

      // Giả lập độ trễ của API (400ms)
      await new Promise(resolve => setTimeout(resolve, 400));

      // Tạo lịch sử gói tin nổi bật giả lập
      const now = new Date();
      const history = [
        {
          _id: `hist_${Date.now()}_1`,
          roomId,
          packageId: 'pkg_vip',
          packageName: 'Tin VIP',
          packageType: 'vip',
          startDate: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 ngày trước
          expiryDate: new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 ngày trước
          status: 'expired',
          createdAt: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          _id: `hist_${Date.now()}_2`,
          roomId,
          packageId: 'pkg_super_vip',
          packageName: 'Tin Super VIP',
          packageType: 'super_vip',
          startDate: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 ngày trước
          expiryDate: new Date(now.getTime() + 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 ngày sau
          status: 'active',
          createdAt: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];

      return {
        success: true,
        data: history,
        message: 'Lấy lịch sử gói tin nổi bật thành công'
      };
    } catch (error) {
      console.error(`Error fetching package history for room ${roomId}:`, error);
      throw error;
    }
  },

  /**
   * Cập nhật trạng thái tin nổi bật của tin đăng
   * @param {string} roomId ID của tin đăng
   * @param {boolean} isHighlighted Trạng thái tin nổi bật
   * @param {string} packageType Loại gói tin nổi bật (vip, super_vip, ...)
   * @returns {Promise} Promise chứa kết quả trả về
   */
  updateRoomHighlightStatus: async (roomId, isHighlighted, packageType) => {
    try {
      // Sử dụng dữ liệu mẫu thay vì gọi API
      // const response = await apiClient.put(`/rooms/${roomId}/highlight`, {
      //   isHighlighted,
      //   packageType
      // });
      // return response.data;

      // Giả lập độ trễ của API (500ms)
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        data: {
          roomId,
          isHighlighted,
          packageType,
          updatedAt: new Date().toISOString()
        },
        message: 'Cập nhật trạng thái tin nổi bật thành công'
      };
    } catch (error) {
      console.error(`Error updating highlight status for room ${roomId}:`, error);
      throw error;
    }
  }
};

export default packageService;
