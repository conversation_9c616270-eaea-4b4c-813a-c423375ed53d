/**
 * Test WebSocket integration với real-time highlight expiry
 */

const mongoose = require('mongoose');
const Room = require('./src/models/Room');
const User = require('./src/models/User');
const Category = require('./src/models/Category');
const jwt = require('jsonwebtoken');
const io = require('socket.io-client');
require('dotenv').config();

async function testWebSocketIntegration() {
  try {
    console.log('🔌 Test WebSocket Integration với Real-time Highlight Expiry\n');

    // Kết nối database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✓ Đã kết nối MongoDB');

    // Tìm user test
    let testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      console.log('❌ Không tìm thấy test user');
      return;
    }

    // Tạo JWT token hợp lệ
    const token = jwt.sign(
      { 
        userId: testUser._id,
        email: testUser.email 
      },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );

    console.log('✓ Đã tạo JWT token hợp lệ');
    console.log(`   User: ${testUser.fullName} (${testUser.email})`);

    // Tạo WebSocket client
    const socket = io('http://localhost:5000', {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    // Setup event listeners
    socket.on('connect', () => {
      console.log('✓ WebSocket connected');
      console.log(`   Socket ID: ${socket.id}`);
      
      // Authenticate
      socket.emit('authenticate', { token });
    });

    socket.on('authenticated', (data) => {
      console.log('✅ Authentication successful');
      console.log(`   User ID: ${data.userId}`);
      console.log(`   Joined room: user_${data.userId}`);
    });

    socket.on('authentication_error', (error) => {
      console.log('❌ Authentication failed:', error.message);
    });

    socket.on('highlight_expired', (data) => {
      console.log('📢 Received highlight_expired event:');
      console.log(`   Room ID: ${data.roomId}`);
      console.log(`   Room Title: ${data.roomTitle}`);
      console.log(`   Expired At: ${data.expiredAt}`);
    });

    socket.on('room_highlight_updated', (data) => {
      console.log('📢 Received room_highlight_updated event:');
      console.log(`   Room ID: ${data.roomId}`);
      console.log(`   Is Highlighted: ${data.isHighlighted}`);
    });

    socket.on('highlight_activated', (data) => {
      console.log('📢 Received highlight_activated event:');
      console.log(`   Room ID: ${data.roomId}`);
      console.log(`   Package: ${data.package}`);
      console.log(`   Expires At: ${data.expiresAt}`);
    });

    socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket disconnected:', reason);
    });

    socket.on('connect_error', (error) => {
      console.log('❌ WebSocket connection error:', error.message);
    });

    // Tạo tin test với thời gian hết hạn ngắn
    const category = await Category.findOne();
    if (!category) {
      console.log('❌ Không tìm thấy category');
      return;
    }

    const testRoom = new Room({
      title: 'Test WebSocket Integration',
      description: 'Test real-time highlight expiry notifications',
      price: 3000000,
      area: 25,
      category: category._id,
      address: {
        street: '123 WebSocket Test Street',
        ward: 'Test Ward',
        district: 'Test District',
        city: 'Test City'
      },
      images: ['test-image.jpg'],
      user: testUser._id,
      status: 'available',
      isHighlighted: true,
      highlightExpiry: new Date(Date.now() + 30000) // Hết hạn sau 30 giây
    });

    await testRoom.save();
    console.log('\n✓ Đã tạo tin test với highlight expiry');
    console.log(`   Room ID: ${testRoom._id}`);
    console.log(`   Hết hạn lúc: ${testRoom.highlightExpiry.toISOString()}`);
    console.log(`   Hiện tại: ${new Date().toISOString()}`);

    // Đợi 35 giây để tin hết hạn và cron job chạy
    console.log('\n⏰ Đợi 35 giây để tin hết hạn và cron job chạy...');
    
    let countdown = 35;
    const countdownInterval = setInterval(() => {
      process.stdout.write(`\r   Còn lại: ${countdown} giây`);
      countdown--;
      
      if (countdown < 0) {
        clearInterval(countdownInterval);
        console.log('\n');
      }
    }, 1000);

    // Cleanup sau 40 giây
    setTimeout(async () => {
      console.log('\n🧹 Cleanup...');
      
      // Xóa tin test
      await Room.findByIdAndDelete(testRoom._id);
      console.log('✓ Đã xóa tin test');
      
      // Đóng WebSocket connection
      socket.disconnect();
      console.log('✓ Đã đóng WebSocket connection');
      
      // Đóng database connection
      await mongoose.connection.close();
      console.log('✓ Đã đóng kết nối MongoDB');
      
      console.log('\n🎉 Test WebSocket integration hoàn thành!');
      process.exit(0);
    }, 40000);

  } catch (error) {
    console.error('❌ Test thất bại:', error);
    process.exit(1);
  }
}

// Chạy test
if (require.main === module) {
  testWebSocketIntegration();
}

module.exports = testWebSocketIntegration;
