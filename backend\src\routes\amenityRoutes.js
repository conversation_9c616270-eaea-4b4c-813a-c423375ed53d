const express = require('express');
const router = express.Router();
const {
  getAmenities,
  getAmenityById,
  createAmenity,
  updateAmenity,
  deleteAmenity
} = require('../controllers/amenityController');
const { protect, admin } = require('../middlewares/authMiddleware');

/**
 * @swagger
 * tags:
 *   name: Amenities
 *   description: API quản lý tiện nghi
 */

/**
 * @swagger
 * /amenities:
 *   get:
 *     summary: L<PERSON>y danh sách tiện nghi
 *     tags: [Amenities]
 *     responses:
 *       200:
 *         description: Danh sách tiện nghi
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Amenity'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/', getAmenities);

/**
 * @swagger
 * /amenities/{id}:
 *   get:
 *     summary: <PERSON><PERSON><PERSON> chi tiết tiện nghi
 *     tags: [Amenities]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: ID của tiện nghi
 *     responses:
 *       200:
 *         description: Chi tiết tiện nghi
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Amenity'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/:id', getAmenityById);

/**
 * @swagger
 * /amenities:
 *   post:
 *     summary: Tạo tiện nghi mới
 *     tags: [Amenities]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Tên tiện nghi
 *               icon:
 *                 type: string
 *                 description: Icon của tiện nghi
 *     responses:
 *       201:
 *         description: Tạo tiện nghi thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Amenity'
 *       400:
 *         description: Dữ liệu không hợp lệ
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post('/', protect, admin, createAmenity);

/**
 * @swagger
 * /amenities/{id}:
 *   put:
 *     summary: Cập nhật tiện nghi
 *     tags: [Amenities]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: ID của tiện nghi
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Tên tiện nghi
 *               icon:
 *                 type: string
 *                 description: Icon của tiện nghi
 *     responses:
 *       200:
 *         description: Cập nhật tiện nghi thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Amenity'
 *       400:
 *         description: Dữ liệu không hợp lệ
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.put('/:id', protect, admin, updateAmenity);

/**
 * @swagger
 * /amenities/{id}:
 *   delete:
 *     summary: Xóa tiện nghi
 *     tags: [Amenities]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: ID của tiện nghi
 *     responses:
 *       200:
 *         description: Xóa tiện nghi thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Xóa tiện nghi thành công
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.delete('/:id', protect, admin, deleteAmenity);

module.exports = router;
