const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'API Hệ thống tìm phòng trọ',
      version: '1.0.0',
      description: 'API cho hệ thống tìm phòng trọ - Tài liệu này mô tả đầy đủ các API endpoints của hệ thống',
      contact: {
        name: '<PERSON><PERSON> thống tìm phòng trọ',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: '/api',
        description: 'API Server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Sử dụng JWT Token để xác thực. Thêm Bearer token vào header Authorization'
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            message: {
              type: 'string',
              example: 'Lỗi server'
            },
            error: {
              type: 'string',
              example: 'Chi tiết lỗi (chỉ hiển thị trong môi trường phát triển)'
            }
          }
        },
        ValidationError: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            message: {
              type: 'string',
              example: 'Dữ liệu không hợp lệ'
            },
            errors: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  field: {
                    type: 'string',
                    example: 'email'
                  },
                  message: {
                    type: 'string',
                    example: 'Email không hợp lệ'
                  }
                }
              }
            }
          }
        }
      },
      responses: {
        UnauthorizedError: {
          description: 'Không có quyền truy cập',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: {
                    type: 'boolean',
                    example: false
                  },
                  message: {
                    type: 'string',
                    example: 'Không được phép, token không hợp lệ'
                  }
                }
              }
            }
          }
        },
        ForbiddenError: {
          description: 'Không có quyền thực hiện hành động này',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: {
                    type: 'boolean',
                    example: false
                  },
                  message: {
                    type: 'string',
                    example: 'Không được phép, yêu cầu quyền admin'
                  }
                }
              }
            }
          }
        },
        NotFoundError: {
          description: 'Không tìm thấy tài nguyên',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: {
                    type: 'boolean',
                    example: false
                  },
                  message: {
                    type: 'string',
                    example: 'Không tìm thấy tài nguyên'
                  }
                }
              }
            }
          }
        },
        ServerError: {
          description: 'Lỗi server',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ],
    tags: [
      {
        name: 'Auth',
        description: 'API xác thực người dùng'
      },
      {
        name: 'Rooms',
        description: 'API quản lý phòng trọ'
      },
      {
        name: 'Transactions',
        description: 'API quản lý giao dịch thanh toán'
      },
      {
        name: 'Amenities',
        description: 'API quản lý tiện nghi'
      },
      {
        name: 'Categories',
        description: 'API quản lý loại phòng'
      },
      {
        name: 'Admin',
        description: 'API dành cho quản trị viên'
      }
    ]
  },
  apis: ['./src/routes/*.js', './src/models/*.js']
};

const specs = swaggerJsdoc(options);

module.exports = specs;
