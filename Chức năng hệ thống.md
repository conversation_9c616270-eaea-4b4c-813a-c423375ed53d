
# TÀI LIỆU HỆ THỐNG TÌM PHÒNG TRỌ

## 1. Tổng quan về hệ thống

Hệ thống tìm phòng trọ là một nền tảng trực tuyến cho phép người dùng tìm kiếm, đăng tin và quản lý thông tin về phòng trọ. <PERSON>ệ thống được xây dựng với:
- **Frontend**: ReactJS
- **Backend**: ExpressJS
- **Database**: MongoDB
- **Payment**: VNPay Sandbox

## 2. <PERSON><PERSON><PERSON> chức năng chính của hệ thống

### 2.1. <PERSON><PERSON><PERSON> năng dành cho khách (không cần đăng nhập)
- **Tìm kiếm phòng trọ**: Kh<PERSON>ch có thể tìm kiếm phòng trọ theo nhiều tiêu chí như:
  - <PERSON>hu vực/địa điểm
  - <PERSON><PERSON><PERSON><PERSON> (khoảng giá)
  - <PERSON><PERSON><PERSON> tích 
  - <PERSON><PERSON><PERSON><PERSON> ích (đ<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, wifi, v.v.)
  - <PERSON><PERSON><PERSON>h<PERSON> (ph<PERSON>ng tr<PERSON>, chung c<PERSON> mini, nhà nguyên căn, v.v.)
- **Xem chi tiết phòng trọ**: Khách có thể xem thông tin chi tiết về một phòng trọ, bao gồm:
  - Hình ảnh
  - Mô tả
  - Giá cả
  - Diện tích
  - Địa chỉ 
  - Tiện ích 
  - Thông tin liên hệ của chủ trọ
  
- **Đăng ký tài khoản**: Khách có thể đăng ký tài khoản mới với email và mật khẩu.
- **Đăng nhập**: Khách có thể đăng nhập vào hệ thống bằng email và mật khẩu.

### 2.2. Chức năng dành cho người dùng (đã đăng nhập)
- **Quản lý tài khoản cá nhân**:
  - Cập nhật thông tin cá nhân(email, số điện thoại, v.v.)
  - Thay đổi mật khẩu
  
  

- **Quản lý danh sách phòng trọ**:
  - Đăng tin cho thuê phòng trọ: Nhập thông tin chi tiết về phòng trọ,Tải lên hình ảnh,Đặt giá ,chọn tiện ích và các điều kiện thuê, chon đăng tin thường (miễn phí) hoặc đăng tin nổi bật (10k) (yêu cầu thanh toán trước khi tin được đăng lên hệ thống)
  - sửa, xóa thông tin phòng trọ
  - Cập nhật trạng thái phòng (còn trống, đã cho thuê)
  

### 2.3. Chức năng dành cho quản trị viên
- **Quản lý tài khoản**:
	- Đăng nhập vào hệ thống (không có tính năng đăng ký)
  - Đổi mật khẩu
  - Quản lý thông tin liên hệ

- **Quản lý người dùng**:
  - Tìm kiếm người dùng theo tên hoặc email
  - Xem danh sách người dùng
  - Khóa/mở khóa tài khoản người dùng, xóa thông tin người dùng
- **Quản lý danh sach phong tro**:
  - Tìm kiếm theo tiêu đề hoặc địa điểm
  - xem chi tiết tin đăng
  - Xóa tin đăng vi phạm
  **Quản lý tien nghi**:
   -  CRUD tiện ích
- **Thống kê hệ thống**: Xem các báo cáo và thống kê tổng số phòng trọ, tổng số người dùng, tổng số tin đăng mới, tổng số người dùng , doanh thu

    

