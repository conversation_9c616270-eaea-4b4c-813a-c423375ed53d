import { useState, useEffect } from 'react';
import { Outlet, useNavigate, Link, useLocation } from 'react-router-dom';
import {
  FaUsers,
  FaHome,
  FaChartBar,
  FaMoneyBillWave,
  FaSignOutAlt,
  FaBars,
  FaTimes,
  FaUserShield,
  FaTags,
  FaList,
  FaDoorOpen
} from 'react-icons/fa';
import { useAuthContext } from '../../contexts';
import { toast } from 'react-toastify';

const AdminLayout = () => {
  const { user, logout } = useAuthContext();
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Kiểm tra quyền admin
  useEffect(() => {
    if (!user || user.role !== 'admin') {
      toast.error('<PERSON><PERSON><PERSON> không c<PERSON> quyền truy cập trang quản trị');
      navigate('/login');
    }
  }, [user, navigate]);

  // Xử lý responsive
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (mobile) {
        setSidebarOpen(false);
      } else {
        setSidebarOpen(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Xử lý đăng xuất
  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Danh sách menu
  const menuItems = [
    { path: '/admin', icon: <FaChartBar />, label: 'Thống kê' },
    { path: '/admin/users', icon: <FaUsers />, label: 'Quản lý chủ trọ' },
    { path: '/admin/rooms', icon: <FaHome />, label: 'Quản lý tin đăng' },
    { path: '/admin/transactions', icon: <FaMoneyBillWave />, label: 'Quản lý giao dịch' },
    { path: '/admin/categories', icon: <FaTags />, label: 'Quản lý loại phòng' },
    { path: '/admin/amenities', icon: <FaList />, label: 'Quản lý tiện nghi' },
    { path: '/', icon: <FaDoorOpen />, label: 'Thoát', isExit: true },
  ];

  // Kiểm tra đường dẫn hiện tại để highlight menu item
  const isActive = (path) => {
    if (path === '/admin') {
      return location.pathname === '/admin';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div
        className={`${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } fixed inset-y-0 left-0 z-30 w-64 bg-primary text-white transition duration-300 transform md:relative md:translate-x-0`}
      >
        <div className="flex items-center justify-between h-16 px-4 border-b border-primary-dark">
          <Link to="/admin" className="flex items-center">
            <FaUserShield className="w-6 h-6 mr-2" />
            <span className="text-xl font-semibold">Admin Panel</span>
          </Link>
          {isMobile && (
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-1 rounded-md hover:bg-primary-dark"
            >
              <FaTimes className="w-6 h-6" />
            </button>
          )}
        </div>

        <nav className="mt-5 px-2">
          <div className="space-y-1">
            {menuItems.map((item) => {
              if (item.isExit) {
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className="flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors text-white hover:bg-primary-dark border-t border-primary-dark mt-2 pt-4"
                  >
                    <span className="mr-3 text-xl">{item.icon}</span>
                    {item.label}
                  </Link>
                );
              }

              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors ${
                    isActive(item.path)
                      ? 'bg-primary-dark text-white'
                      : 'text-white hover:bg-primary-dark'
                  }`}
                >
                  <span className="mr-3 text-xl">{item.icon}</span>
                  {item.label}
                </Link>
              );
            })}
          </div>
        </nav>

        <div className="absolute bottom-0 w-full border-t border-primary-dark p-4">
          <button
            onClick={handleLogout}
            className="flex items-center w-full px-4 py-2 text-sm font-medium text-white rounded-md hover:bg-primary-dark transition-colors"
          >
            <FaSignOutAlt className="mr-3 text-xl" />
            Đăng xuất
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Top header */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-4 py-4 sm:px-6 lg:px-8 flex justify-between items-center">
            {isMobile && (
              <button
                onClick={() => setSidebarOpen(true)}
                className="p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none"
              >
                <FaBars className="h-6 w-6" />
              </button>
            )}
            <h1 className="text-lg font-semibold text-gray-900">
              {menuItems.find(item => isActive(item.path))?.label || 'Trang quản trị'}
            </h1>
            <div className="flex items-center">
              <span className="text-sm font-medium text-gray-700 mr-2">
                {user?.fullName || 'Admin'}
              </span>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8 bg-gray-50">
          <Outlet />
        </main>
      </div>

      {/* Mobile sidebar backdrop */}
      {sidebarOpen && isMobile && (
        <div
          className="fixed inset-0 z-20 bg-black bg-opacity-50 transition-opacity md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default AdminLayout;
