<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Toast Fix</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .countdown { font-size: 18px; font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Toast Error Fix</h1>
        <p>Kiểm tra xem toast error có bị suppress không</p>
        
        <div id="countdown" class="countdown">Đang đếm ngược...</div>
        
        <div class="status info">
            <strong>Trạng thái:</strong> <span id="status">Đang test...</span>
        </div>

        <div>
            <button class="btn-primary" onclick="testAPI()">Test API Call</button>
            <button class="btn-success" onclick="enableToast()">Bật Toast</button>
            <button class="btn-danger" onclick="suppressToast()">Tắt Toast</button>
        </div>

        <div id="logs" style="margin-top: 20px; max-height: 400px; overflow-y: auto;"></div>
    </div>

    <script>
        let startTime = Date.now();
        let countdownInterval;

        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            logsDiv.appendChild(div);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }

        function updateCountdown() {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const remaining = Math.max(0, 12 - elapsed);
            
            const countdownDiv = document.getElementById('countdown');
            if (remaining > 0) {
                countdownDiv.textContent = `Toast errors sẽ được bật sau: ${remaining}s`;
                countdownDiv.style.color = '#dc3545';
            } else {
                countdownDiv.textContent = '✅ Toast errors đã được bật';
                countdownDiv.style.color = '#28a745';
                clearInterval(countdownInterval);
            }
        }

        function testAPI() {
            log('🧪 Testing API call...', 'info');
            
            // Test với một URL không tồn tại để trigger error
            fetch('http://localhost:5000/api/nonexistent-endpoint')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    log('✅ API call thành công (bất ngờ!)', 'success');
                })
                .catch(error => {
                    log(`❌ API call thất bại: ${error.message} (Toast có hiển thị không?)`, 'warning');
                });
        }

        function enableToast() {
            localStorage.removeItem('suppressToastErrors');
            log('🔊 Toast errors đã được bật thủ công', 'success');
        }

        function suppressToast() {
            localStorage.setItem('suppressToastErrors', 'true');
            log('🔇 Toast errors đã bị tắt thủ công', 'warning');
        }

        // Bắt đầu countdown
        countdownInterval = setInterval(updateCountdown, 1000);
        updateCountdown();

        // Log initial state
        log('🚀 Test bắt đầu - Kiểm tra console để xem logs từ apiClient', 'info');
        log('📝 Trong 12 giây đầu, toast errors sẽ bị suppress', 'info');
        log('🔍 Mở Developer Tools > Console để xem chi tiết', 'info');

        // Test ngay lập tức
        setTimeout(() => {
            log('🧪 Auto test sau 2 giây...', 'info');
            testAPI();
        }, 2000);

        // Test sau 6 giây
        setTimeout(() => {
            log('🧪 Auto test sau 6 giây...', 'info');
            testAPI();
        }, 6000);

        // Test sau 15 giây (toast should show)
        setTimeout(() => {
            log('🧪 Auto test sau 15 giây (toast nên hiển thị)...', 'info');
            testAPI();
        }, 15000);
    </script>
</body>
</html>
