import apiClient from './apiClient';

// Cache để tránh duplicate requests
const requestCache = new Map();
const CACHE_DURATION = 1000; // 1 giây

// Service xử lý các API liên quan đến phòng trọ
const roomService = {
  // L<PERSON>y danh sách phòng trọ với các bộ lọc
  getRooms: async (filters = {}) => {
    try {
      // Tạo cache key từ filters
      const cacheKey = JSON.stringify(filters);
      const now = Date.now();

      // Kiểm tra cache
      if (requestCache.has(cacheKey)) {
        const cached = requestCache.get(cacheKey);
        if (now - cached.timestamp < CACHE_DURATION) {
          console.log('Returning cached rooms data');
          return cached.data;
        }
      }

      console.log('Fetching rooms with filters:', filters);
      const response = await apiClient.get('/rooms', {
        params: filters,
        timeout: 10000 // 10 giây timeout
      });

      // Cache response
      requestCache.set(cacheKey, {
        data: response.data,
        timestamp: now
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching rooms:', error);

      if (error.response) {
        console.error('Server error response:', error.response.data);
        throw error.response.data;
      } else if (error.request) {
        console.error('No response received:', error.request);
        throw { message: 'Không nhận được phản hồi từ máy chủ. Vui lòng kiểm tra kết nối mạng.' };
      } else {
        console.error('Request setup error:', error.message);
        throw { message: `Lỗi khi gửi yêu cầu: ${error.message}` };
      }
    }
  },

  // Lấy thông tin chi tiết phòng trọ
  getRoomById: async (id, options = {}) => {
    try {
      // Kiểm tra ID hợp lệ
      if (!id) {
        console.error('ID phòng trọ không hợp lệ:', id);
        throw { message: 'ID phòng trọ không hợp lệ' };
      }

      console.log('Fetching room details for ID:', id, 'with options:', options);

      // Xây dựng tham số truy vấn
      const params = {};

      // Kiểm tra tham số increaseView
      if (options.view === false || options.increaseView === false) {
        params.view = 'false';
      } else {
        // Mặc định tăng lượt xem
        params.view = 'true';
      }

      console.log('Request params for room details:', params);

      const response = await apiClient.get(`/rooms/${id}`, {
        params,
        timeout: 15000 // Tăng timeout lên 15 giây
      });

      // Kiểm tra dữ liệu trả về
      if (!response.data || !response.data.success) {
        console.error('Invalid response data:', response.data);
        throw { message: 'Dữ liệu phòng trọ không hợp lệ' };
      }

      // Kiểm tra dữ liệu phòng trọ
      if (!response.data.data) {
        console.error('Room data is missing:', response.data);
        throw { message: 'Không tìm thấy thông tin phòng trọ' };
      }

      return response.data;
    } catch (error) {
      console.error(`Error fetching room with ID ${id}:`, error);

      if (error.response) {
        // Xử lý lỗi 404 - Không tìm thấy phòng
        if (error.response.status === 404) {
          console.error('Room not found:', error.response.data);
          throw { message: 'Không tìm thấy phòng trọ với ID này', status: 404 };
        }

        // Xử lý các lỗi server khác
        console.error('Server error response:', error.response.data);
        throw error.response.data || { message: `Lỗi máy chủ: ${error.response.status}` };
      } else if (error.request) {
        console.error('No response received:', error.request);
        throw { message: 'Không nhận được phản hồi từ máy chủ. Vui lòng kiểm tra kết nối mạng.' };
      } else {
        console.error('Request setup error:', error.message);
        throw { message: `Lỗi khi gửi yêu cầu: ${error.message}` };
      }
    }
  },

  // Tạo phòng trọ mới
  createRoom: async (roomData) => {
    try {
      console.log('Creating room with data:', roomData);

      // Sử dụng FormData để gửi dữ liệu multipart/form-data (có hình ảnh)
      const formData = new FormData();

      // Thêm các trường thông tin cơ bản
      formData.append('title', roomData.title);
      formData.append('description', roomData.description);
      formData.append('price', roomData.price.toString());
      formData.append('area', roomData.area.toString());

      if (roomData.deposit) {
        formData.append('deposit', roomData.deposit.toString());
      }

      // Tạo đối tượng địa chỉ dưới dạng JSON string để tránh lỗi với dấu chấm trong key
      const addressData = JSON.stringify({
        street: roomData.address.street,
        ward: roomData.address.ward,
        district: roomData.address.district,
        city: roomData.address.city
      });
      formData.append('address', addressData);

      // Thêm loại phòng
      formData.append('category', roomData.category);

      // Thêm tiện nghi (nếu có)
      if (roomData.amenities && roomData.amenities.length > 0) {
        // Chuyển mảng amenities thành JSON string
        formData.append('amenities', JSON.stringify(roomData.amenities));
      }

      // Thêm hình ảnh
      if (roomData.images && roomData.images.length > 0) {
        for (let i = 0; i < roomData.images.length; i++) {
          formData.append('images', roomData.images[i]);
        }
      }

      // Log FormData để debug (không thể log trực tiếp)
      for (let [key, value] of formData.entries()) {
        console.log(`FormData: ${key} = ${value instanceof File ? value.name : value}`);
      }

      const response = await apiClient.post('/rooms', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        // Tăng timeout cho upload file
        timeout: 30000,
      });

      return response.data;
    } catch (error) {
      console.error('Error creating room:', error);

      // Xử lý và trả về thông báo lỗi chi tiết hơn
      if (error.response) {
        // Lỗi từ server với response
        console.error('Server error response:', error.response.data);
        throw error.response.data;
      } else if (error.request) {
        // Lỗi không nhận được response
        console.error('No response received:', error.request);
        throw { message: 'Không nhận được phản hồi từ máy chủ. Vui lòng kiểm tra kết nối mạng.' };
      } else {
        // Lỗi khi thiết lập request
        console.error('Request setup error:', error.message);
        throw { message: `Lỗi khi gửi yêu cầu: ${error.message}` };
      }
    }
  },

  // Cập nhật thông tin phòng trọ
  updateRoom: async (id, roomData) => {
    try {
      console.log('Updating room with data:', roomData);

      // Sử dụng FormData để gửi dữ liệu multipart/form-data (có hình ảnh)
      const formData = new FormData();

      // Thêm các trường thông tin cơ bản
      formData.append('title', roomData.title);
      formData.append('description', roomData.description);
      formData.append('price', roomData.price.toString());
      formData.append('area', roomData.area.toString());

      if (roomData.deposit) {
        formData.append('deposit', roomData.deposit.toString());
      }

      // Tạo đối tượng địa chỉ dưới dạng JSON string để tránh lỗi với dấu chấm trong key
      const addressData = JSON.stringify({
        street: roomData.address.street,
        ward: roomData.address.ward,
        district: roomData.address.district,
        city: roomData.address.city
      });
      formData.append('address', addressData);

      // Thêm loại phòng
      formData.append('category', roomData.category);

      // Thêm tiện nghi (nếu có)
      if (roomData.amenities && roomData.amenities.length > 0) {
        // Chuyển mảng amenities thành JSON string
        formData.append('amenities', JSON.stringify(roomData.amenities));
      }

      // Thêm hình ảnh mới (nếu có)
      if (roomData.images && roomData.images.length > 0) {
        for (let i = 0; i < roomData.images.length; i++) {
          formData.append('images', roomData.images[i]);
        }
      }

      // Thêm danh sách hình ảnh cần xóa (nếu có)
      if (roomData.removeImages && roomData.removeImages.length > 0) {
        formData.append('removeImages', JSON.stringify(roomData.removeImages));
      }

      // Log FormData để debug (không thể log trực tiếp)
      for (let [key, value] of formData.entries()) {
        console.log(`FormData: ${key} = ${value instanceof File ? value.name : value}`);
      }

      const response = await apiClient.put(`/rooms/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        // Tăng timeout cho upload file
        timeout: 30000,
      });

      return response.data;
    } catch (error) {
      console.error('Error updating room:', error);

      // Xử lý và trả về thông báo lỗi chi tiết hơn
      if (error.response) {
        // Lỗi từ server với response
        console.error('Server error response:', error.response.data);
        throw error.response.data;
      } else if (error.request) {
        // Lỗi không nhận được response
        console.error('No response received:', error.request);
        throw { message: 'Không nhận được phản hồi từ máy chủ. Vui lòng kiểm tra kết nối mạng.' };
      } else {
        // Lỗi khi thiết lập request
        console.error('Request setup error:', error.message);
        throw { message: `Lỗi khi gửi yêu cầu: ${error.message}` };
      }
    }
  },

  // Xóa phòng trọ
  deleteRoom: async (id) => {
    try {
      console.log('Deleting room with ID:', id);
      const response = await apiClient.delete(`/rooms/${id}`, {
        timeout: 10000 // 10 giây timeout
      });
      return response.data;
    } catch (error) {
      console.error(`Error deleting room with ID ${id}:`, error);

      if (error.response) {
        console.error('Server error response:', error.response.data);
        throw error.response.data;
      } else if (error.request) {
        console.error('No response received:', error.request);
        throw { message: 'Không nhận được phản hồi từ máy chủ. Vui lòng kiểm tra kết nối mạng.' };
      } else {
        console.error('Request setup error:', error.message);
        throw { message: `Lỗi khi gửi yêu cầu: ${error.message}` };
      }
    }
  },

  // Lấy danh sách phòng trọ của người dùng hiện tại
  getMyRooms: async (filters = {}) => {
    try {
      console.log('Fetching my rooms with filters:', filters);
      const response = await apiClient.get('/rooms/my-rooms', {
        params: filters,
        timeout: 10000 // 10 giây timeout
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching my rooms:', error);

      if (error.response) {
        console.error('Server error response:', error.response.data);
        throw error.response.data;
      } else if (error.request) {
        console.error('No response received:', error.request);
        throw { message: 'Không nhận được phản hồi từ máy chủ. Vui lòng kiểm tra kết nối mạng.' };
      } else {
        console.error('Request setup error:', error.message);
        throw { message: `Lỗi khi gửi yêu cầu: ${error.message}` };
      }
    }
  },

  // Cập nhật trạng thái phòng trọ
  updateRoomStatus: async (id, status) => {
    try {
      console.log(`Updating room status for ID ${id} to ${status}`);
      const response = await apiClient.put(`/rooms/${id}/status`, { status }, {
        timeout: 10000 // 10 giây timeout
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating room status for ID ${id}:`, error);

      if (error.response) {
        console.error('Server error response:', error.response.data);
        throw error.response.data;
      } else if (error.request) {
        console.error('No response received:', error.request);
        throw { message: 'Không nhận được phản hồi từ máy chủ. Vui lòng kiểm tra kết nối mạng.' };
      } else {
        console.error('Request setup error:', error.message);
        throw { message: `Lỗi khi gửi yêu cầu: ${error.message}` };
      }
    }
  },
};

export default roomService;
