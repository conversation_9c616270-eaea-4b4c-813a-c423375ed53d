import { useState, useEffect } from 'react';
import { FaSearch, FaTrash, FaLock, FaUnlock, FaExclamationTriangle } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useAuthContext } from '../../contexts';
import Pagination from '../../components/Pagination';
import Modal from '../../components/Modal';

const UserManagement = () => {
  const { token } = useAuthContext();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [search, setSearch] = useState('');
  const [role, setRole] = useState('');
  const [isActive, setIsActive] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Modals
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [newStatus, setNewStatus] = useState(false);

  // Fetch users
  const fetchUsers = async () => {
    try {
      setLoading(true);

      // Build query params
      const params = new URLSearchParams();
      params.append('page', pagination.page);
      params.append('limit', pagination.limit);
      if (search) params.append('search', search);
      if (role) params.append('role', role);
      if (isActive !== '') params.append('isActive', isActive);

      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/users?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Không thể lấy danh sách chủ trọ');
      }

      setUsers(data.data.users);
      setPagination(data.data.pagination);
    } catch (error) {
      console.error('Lỗi khi lấy danh sách chủ trọ:', error);
      setError(error.message);
      toast.error(`Lỗi: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [token, pagination.page, pagination.limit]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchUsers();
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // Handle status change
  const openStatusModal = (user, status) => {
    setSelectedUser(user);
    setNewStatus(status);
    setShowStatusModal(true);
  };

  const handleStatusChange = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/users/${selectedUser._id}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ isActive: newStatus })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Không thể cập nhật trạng thái chủ trọ');
      }

      // Update user in list
      setUsers(users.map(user =>
        user._id === selectedUser._id ? { ...user, isActive: newStatus } : user
      ));

      toast.success(`Đã ${newStatus ? 'kích hoạt' : 'khóa'} tài khoản ${selectedUser.email}`);
      setShowStatusModal(false);
    } catch (error) {
      console.error('Lỗi khi cập nhật trạng thái chủ trọ:', error);
      toast.error(`Lỗi: ${error.message}`);
    }
  };

  // Handle delete
  const openDeleteModal = (user) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/users/${selectedUser._id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Không thể xóa chủ trọ');
      }

      // Remove user from list
      setUsers(users.filter(user => user._id !== selectedUser._id));
      toast.success(`Đã xóa chủ trọ ${selectedUser.email}`);
      setShowDeleteModal(false);
    } catch (error) {
      console.error('Lỗi khi xóa chủ trọ:', error);
      toast.error(`Lỗi: ${error.message}`);
    }
  };

  // Reset filters
  const resetFilters = () => {
    setSearch('');
    setRole('');
    setIsActive('');
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchUsers();
  };

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <FaExclamationTriangle className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Đã xảy ra lỗi khi tải dữ liệu
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Quản lý chủ trọ</h1>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <form onSubmit={handleSearch} className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Tìm kiếm theo email, tên..."
              className="input input-bordered w-full pr-10"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <button type="submit" className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <FaSearch className="text-gray-400" />
            </button>
          </div>

          <div>
            <select
              className="select select-bordered w-full"
              value={role}
              onChange={(e) => setRole(e.target.value)}
            >
              <option value="">Tất cả vai trò</option>
              <option value="user">chủ trọ</option>
              <option value="admin">Quản trị viên</option>
            </select>
          </div>

          <div>
            <select
              className="select select-bordered w-full"
              value={isActive}
              onChange={(e) => setIsActive(e.target.value)}
            >
              <option value="">Tất cả trạng thái</option>
              <option value="true">Đang hoạt động</option>
              <option value="false">Đã khóa</option>
            </select>
          </div>

          <div className="flex space-x-2">
            <button type="submit" className="btn btn-primary flex-1">
              Lọc
            </button>
            <button type="button" onClick={resetFilters} className="btn btn-outline">
              Đặt lại
            </button>
          </div>
        </form>
      </div>

      {/* Users table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  chủ trọ
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vai trò
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ngày tạo
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thao tác
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center">
                    <div className="flex justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
                    </div>
                  </td>
                </tr>
              ) : users.length === 0 ? (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center text-sm text-gray-500">
                    Không tìm thấy chủ trọ nào
                  </td>
                </tr>
              ) : (
                users.map((user) => (
                  <tr key={user._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                          {user.fullName?.charAt(0) || user.email.charAt(0)}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.fullName || 'Chưa cập nhật'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {user.phone || 'Chưa cập nhật SĐT'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        user.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {user.role === 'admin' ? 'Quản trị viên' : 'chủ trọ'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {user.isActive ? 'Đang hoạt động' : 'Đã khóa'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(user.createdAt).toLocaleDateString('vi-VN')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        {user.isActive ? (
                          <button
                            onClick={() => openStatusModal(user, false)}
                            className="text-red-600 hover:text-red-900"
                            title="Khóa tài khoản"
                          >
                            <FaLock />
                          </button>
                        ) : (
                          <button
                            onClick={() => openStatusModal(user, true)}
                            className="text-green-600 hover:text-green-900"
                            title="Mở khóa tài khoản"
                          >
                            <FaUnlock />
                          </button>
                        )}
                        <button
                          onClick={() => openDeleteModal(user)}
                          className="text-red-600 hover:text-red-900"
                          title="Xóa"
                          disabled={user.role === 'admin'}
                        >
                          <FaTrash className={user.role === 'admin' ? 'opacity-50 cursor-not-allowed' : ''} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {!loading && users.length > 0 && (
          <div className="px-6 py-4 bg-white border-t border-gray-200">
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.pages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* Delete Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Xác nhận xóa chủ trọ"
      >
        <div className="p-6">
          <p className="text-sm text-gray-500">
            Bạn có chắc chắn muốn xóa chủ trọ <span className="font-semibold">{selectedUser?.email}</span>?
            Hành động này không thể hoàn tác.
          </p>
          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              className="btn btn-outline"
              onClick={() => setShowDeleteModal(false)}
            >
              Hủy
            </button>
            <button
              type="button"
              className="btn btn-error"
              onClick={handleDelete}
            >
              Xóa
            </button>
          </div>
        </div>
      </Modal>

      {/* Status Change Modal */}
      <Modal
        isOpen={showStatusModal}
        onClose={() => setShowStatusModal(false)}
        title={newStatus ? 'Kích hoạt tài khoản' : 'Khóa tài khoản'}
      >
        <div className="p-6">
          <p className="text-sm text-gray-500">
            {newStatus
              ? `Bạn có chắc chắn muốn kích hoạt tài khoản của chủ trọ ${selectedUser?.email}?`
              : `Bạn có chắc chắn muốn khóa tài khoản của chủ trọ ${selectedUser?.email}? chủ trọ sẽ không thể đăng nhập hoặc sử dụng hệ thống.`
            }
          </p>
          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              className="btn btn-outline"
              onClick={() => setShowStatusModal(false)}
            >
              Hủy
            </button>
            <button
              type="button"
              className={`btn ${newStatus ? 'btn-success' : 'btn-error'}`}
              onClick={handleStatusChange}
            >
              {newStatus ? 'Kích hoạt' : 'Khóa'}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default UserManagement;
