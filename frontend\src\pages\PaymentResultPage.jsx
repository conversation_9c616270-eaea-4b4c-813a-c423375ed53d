import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaCheckCircle, FaTimesCircle, FaHome, FaEye, FaCrown, FaStar } from 'react-icons/fa';
import { useRooms } from '../hooks';
import { formatCurrency } from '../utils/format';
import { applyVNPayPatches } from '../utils/vnpay-patch';
import { transactionService } from '../services';

const PaymentResultPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { room, fetchRoomById, isLoading: isRoomLoading } = useRooms();

  const [paymentResult, setPaymentResult] = useState(null);
  const [isVerifying, setIsVerifying] = useState(true);
  const [transactionInfo, setTransactionInfo] = useState(null);

  // Áp dụng bản vá lỗi VNPay khi trang được tải
  useEffect(() => {
    // Áp dụng bản vá lỗi VNPay (bao gồm tải jQuery và jQuery Validate)
    const applyPatches = async () => {
      try {
        const cleanup = await applyVNPayPatches();
        console.log('[VNPay] Đã áp dụng bản vá lỗi trên trang kết quả thanh toán');

        // Trả về hàm cleanup để sử dụng khi component unmount
        return cleanup;
      } catch (error) {
        console.error('[VNPay] Lỗi khi áp dụng bản vá lỗi:', error);
        return () => {}; // Trả về hàm rỗng nếu có lỗi
      }
    };

    // Biến để lưu hàm cleanup
    let cleanupFn;

    // Gọi hàm áp dụng bản vá lỗi và lưu hàm cleanup
    applyPatches().then(cleanup => {
      cleanupFn = cleanup;
    });

    // Cleanup khi component unmount
    return () => {
      if (typeof cleanupFn === 'function') {
        cleanupFn();
      }
    };
  }, []);

  // Xử lý kết quả thanh toán từ VNPay
  useEffect(() => {
    const verifyPaymentResult = async () => {
      setIsVerifying(true);
      try {
        // Lấy tham số truy vấn từ URL
        const queryParams = {};
        const searchParams = new URLSearchParams(location.search);
        for (const [key, value] of searchParams.entries()) {
          queryParams[key] = value;
        }

        console.log('[Payment Result] Verifying payment with params:', queryParams);

        // Xác nhận kết quả thanh toán với API mới
        const response = await transactionService.verifyPayment(queryParams);
        setPaymentResult(response);

        // Hiển thị thông báo
        if (response.success) {
          toast.success('Thanh toán thành công!');

          // Lưu thông tin transaction
          if (response.data) {
            setTransactionInfo(response.data);

            // Tải lại thông tin phòng để cập nhật trạng thái mới
            if (response.data.roomId) {
              await fetchRoomById(response.data.roomId, { increaseView: false });
              console.log('[Payment Result] Room data refreshed after successful payment');
            }
          }
        } else {
          toast.error('Thanh toán thất bại: ' + (response.message || 'Lỗi không xác định'));
        }
      } catch (err) {
        console.error('Lỗi khi xác nhận kết quả thanh toán:', err);
        toast.error('Có lỗi xảy ra khi xác nhận kết quả thanh toán');
        setPaymentResult({
          success: false,
          message: 'Có lỗi xảy ra khi xác nhận kết quả thanh toán: ' + (err.message || 'Lỗi không xác định'),
        });
      } finally {
        setIsVerifying(false);
      }
    };

    if (location.search) {
      verifyPaymentResult();
    } else {
      setIsVerifying(false);
      setPaymentResult({
        success: false,
        message: 'Không có thông tin thanh toán',
      });
    }
  }, [location.search, fetchRoomById]);

  // Hiển thị loading khi đang xác nhận kết quả thanh toán
  if (isVerifying || isRoomLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background dark:bg-background-dark">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-text dark:text-text-dark">
            {isVerifying ? 'Đang xác nhận kết quả thanh toán...' : 'Đang tải thông tin...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background dark:bg-background-dark min-h-screen py-12">
      <div className="container-custom">
        <div className="max-w-2xl mx-auto">
          <div className="bg-card dark:bg-card-dark rounded-lg shadow-md overflow-hidden">
            {/* Tiêu đề */}
            <div className="p-6 text-center border-b border-gray-200 dark:border-gray-700">
              <h1 className="text-2xl font-bold mb-2 text-text dark:text-text-dark">
                Kết quả thanh toán
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {paymentResult?.success
                  ? 'Cảm ơn bạn đã thanh toán. Gói tin của bạn đã được cập nhật thành công.'
                  : 'Thanh toán không thành công. Vui lòng thử lại sau.'}
              </p>
            </div>

            {/* Nội dung */}
            <div className="p-6">
              {/* Icon trạng thái */}
              <div className="text-center mb-6">
                {paymentResult?.success ? (
                  <FaCheckCircle className="text-green-500 text-6xl mx-auto" />
                ) : (
                  <FaTimesCircle className="text-red-500 text-6xl mx-auto" />
                )}
              </div>

              {/* Thông tin thanh toán */}
              {transactionInfo && (
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
                  <h2 className="font-bold text-lg mb-3 text-text dark:text-text-dark">Thông tin thanh toán</h2>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Mã giao dịch:</span>
                      <span className="font-medium text-text dark:text-text-dark">{transactionInfo.transactionId || transactionInfo._id}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Số tiền:</span>
                      <span className="font-medium text-text dark:text-text-dark">
                        {transactionInfo.amount === 0 ? 'Miễn phí' : formatCurrency(transactionInfo.amount)}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Gói đã chọn:</span>
                      <span className="font-medium text-text dark:text-text-dark">
                        {transactionInfo.packageType === 'regular' ? 'Gói Thường' : 'Gói Đặc Biệt'}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Thời gian:</span>
                      <span className="font-medium text-text dark:text-text-dark">
                        {new Date(transactionInfo.createdAt || transactionInfo.paymentDate).toLocaleString('vi-VN')}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Phương thức:</span>
                      <span className="font-medium text-text dark:text-text-dark">
                        {transactionInfo.amount === 0 ? 'Chuyển đổi miễn phí' : 'VNPay'}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Trạng thái:</span>
                      <span className={`font-medium ${paymentResult?.success ? 'text-green-500' : 'text-red-500'}`}>
                        {paymentResult?.success ? 'Thành công' : 'Thất bại'}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Thông tin tin đăng và gói */}
              {room && transactionInfo && (
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6 border border-blue-200 dark:border-blue-800">
                  <h2 className="font-bold text-lg mb-3 flex items-center text-blue-700 dark:text-blue-300">
                    {transactionInfo.packageType === 'special' ? (
                      <FaStar className="mr-2 text-yellow-500" />
                    ) : (
                      <FaHome className="mr-2 text-blue-500" />
                    )}
                    Thông tin tin đăng
                  </h2>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Tiêu đề:</span>
                      <span className="font-medium text-text dark:text-text-dark">{room.title}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Địa chỉ:</span>
                      <span className="font-medium text-text dark:text-text-dark">
                        {room.address?.ward}, {room.address?.district}, {room.address?.province}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Gói hiện tại:</span>
                      <span className={`font-medium flex items-center ${room.isHighlighted ? 'text-yellow-600' : 'text-gray-600'}`}>
                        {room.isHighlighted ? (
                          <>
                            <FaStar className="mr-1 text-yellow-500" />
                            Gói Đặc Biệt
                          </>
                        ) : (
                          'Gói Thường'
                        )}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Trạng thái:</span>
                      <span className="font-medium text-green-500">
                        {paymentResult?.success ? 'Đã cập nhật thành công' : 'Chưa cập nhật'}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Thông báo lỗi */}
              {!paymentResult?.success && paymentResult?.message && (
                <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 p-4 rounded-lg mb-6 border border-red-200 dark:border-red-800">
                  <p className="font-medium">Lý do thất bại:</p>
                  <p>{paymentResult.message}</p>
                </div>
              )}

              {/* Các nút điều hướng */}
              <div className="flex flex-col sm:flex-row gap-4 mt-6">
                <button
                  type="button"
                  onClick={() => navigate('/')}
                  className="flex-1 py-2 px-4 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center"
                >
                  <FaHome className="mr-2" />
                  Trang chủ
                </button>

                {(transactionInfo?.roomId || room?._id) && (
                  <button
                    type="button"
                    onClick={() => navigate(`/rooms/${transactionInfo?.roomId || room._id}`)}
                    className="flex-1 py-2 px-4 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center justify-center"
                  >
                    <FaEye className="mr-2" />
                    Xem tin đăng
                  </button>
                )}

                <button
                  type="button"
                  onClick={() => navigate('/my-rooms')}
                  className="flex-1 py-2 px-4 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center justify-center"
                >
                  <FaCrown className="mr-2" />
                  Quản lý tin đăng
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentResultPage;
