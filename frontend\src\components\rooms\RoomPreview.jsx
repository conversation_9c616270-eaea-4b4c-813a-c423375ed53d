import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { FaMapMarkerAlt, FaRuler, FaMoneyBillWave, FaCalendarAlt, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { formatCurrency, formatDate } from '../../utils/format';
import { ImageWithFallback } from '../../components';

const RoomPreview = ({
  formData,
  categories = [],
  amenities = [],
  previewImages = [],
  existingImages = []
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Tìm thông tin loại phòng dựa trên ID
  const category = categories.find(cat => cat._id === formData.category) || {};

  // Tìm thông tin tiện nghi dựa trên ID
  const selectedAmenities = amenities.filter(amenity =>
    formData.amenities && formData.amenities.includes(amenity._id)
  );

  // Xử lý hình ảnh với metadata để phân biệt loại
  const processImages = () => {
    // Xử lý existingImages để đảm bảo là mảng
    const processedExistingImages = Array.isArray(existingImages) ? existingImages : [];

    // Xử lý previewImages để đảm bảo là mảng
    const processedPreviewImages = Array.isArray(previewImages) ? previewImages : [];

    // Kết hợp tất cả hình ảnh với metadata để phân biệt loại
    const allImages = [
      ...processedExistingImages.map(img => ({
        url: img,
        type: 'existing',
        id: `existing-${img}`
      })),
      ...processedPreviewImages.map((img, index) => ({
        url: img,
        type: 'preview',
        id: `preview-${index}`
      }))
    ];

    return allImages;
  };

  // Tất cả hình ảnh (cả hiện có và mới)
  const allImages = processImages();

  // Đảm bảo currentImageIndex không vượt quá số lượng hình ảnh
  useEffect(() => {
    if (allImages.length > 0 && currentImageIndex >= allImages.length) {
      setCurrentImageIndex(0);
    }
  }, [allImages.length, currentImageIndex]);

  // Xử lý khi chuyển hình ảnh
  const handlePrevImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (allImages.length === 0) return;
    setCurrentImageIndex((prev) => (prev === 0 ? allImages.length - 1 : prev - 1));
  };

  const handleNextImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (allImages.length === 0) return;
    setCurrentImageIndex((prev) => (prev === allImages.length - 1 ? 0 : prev + 1));
  };

  // Xử lý khi click vào thumbnail hoặc dot
  const handleImageSelect = (e, index) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex(index);
  };

  // Render hình ảnh hiện tại dựa trên loại
  const renderCurrentImage = () => {
    if (allImages.length === 0) return null;

    const currentImage = allImages[currentImageIndex];

    if (currentImage.type === 'preview') {
      // Đối với preview images (blob URLs), sử dụng img tag trực tiếp
      return (
        <div className="relative w-full h-full">
          <img
            src={currentImage.url}
            alt={formData.title || 'Hình ảnh phòng trọ'}
            className="w-full h-full object-cover transition-opacity duration-300"
            onError={(e) => {
              console.error('Lỗi tải preview image:', currentImage.url);
              e.target.src = 'https://via.placeholder.com/800x600?text=Lỗi+hình+ảnh';
            }}
            onLoad={(e) => {
              e.target.style.opacity = '1';
            }}
            style={{ opacity: '0' }}
          />
          {/* Badge cho preview image */}
          <div className="absolute top-2 left-2 bg-blue-500 text-white px-2 py-1 rounded-md text-xs font-medium">
            Xem trước
          </div>
        </div>
      );
    } else {
      // Đối với existing images (server URLs), sử dụng ImageWithFallback
      return (
        <div className="relative w-full h-full">
          <ImageWithFallback
            src={currentImage.url}
            alt={formData.title || 'Hình ảnh phòng trọ'}
            className="w-full h-full object-cover"
            fallbackSrc="https://via.placeholder.com/800x600?text=Hình+ảnh+phòng+trọ"
          />
          {/* Badge cho existing image */}
          <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded-md text-xs font-medium">
            Đã lưu
          </div>
        </div>
      );
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Hình ảnh */}
      <div className="relative">
        {/* Main image */}
        <div className="relative h-64 md:h-80">
          {allImages.length > 0 ? (
            <>
              {renderCurrentImage()}

              {/* Nút điều hướng hình ảnh */}
              {allImages.length > 1 && (
                <>
                  <button
                    type="button"
                    onClick={handlePrevImage}
                    className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all duration-200 z-10 focus:outline-none focus:ring-2 focus:ring-white"
                    aria-label="Hình ảnh trước"
                    title="Hình ảnh trước"
                  >
                    <FaChevronLeft />
                  </button>
                  <button
                    type="button"
                    onClick={handleNextImage}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all duration-200 z-10 focus:outline-none focus:ring-2 focus:ring-white"
                    aria-label="Hình ảnh tiếp theo"
                    title="Hình ảnh tiếp theo"
                  >
                    <FaChevronRight />
                  </button>

                  {/* Chỉ số hình ảnh */}
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded-md text-sm z-10">
                    {currentImageIndex + 1}/{allImages.length}
                  </div>

                  {/* Dots indicator cho mobile */}
                  <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1 z-10 md:hidden">
                    {allImages.map((_, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={(e) => handleImageSelect(e, index)}
                        className={`w-2 h-2 rounded-full transition-all duration-200 ${
                          index === currentImageIndex
                            ? 'bg-white'
                            : 'bg-white bg-opacity-50 hover:bg-opacity-75'
                        }`}
                        aria-label={`Chuyển đến hình ảnh ${index + 1}`}
                      />
                    ))}
                  </div>
                </>
              )}
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-200">
              <div className="text-center">
                <div className="text-4xl text-gray-400 mb-2">📷</div>
                <p className="text-gray-500">Chưa có hình ảnh</p>
                <p className="text-xs text-gray-400 mt-1">Hình ảnh sẽ hiển thị ở đây</p>
              </div>
            </div>
          )}
        </div>

        {/* Thumbnail gallery - ẩn trên mobile, hiển thị trên desktop */}
        {allImages.length > 1 && (
          <div className="hidden md:block p-3 bg-gray-50 border-t">
            <div className="flex space-x-2 overflow-x-auto pb-1">
              {allImages.map((image, index) => (
                <button
                  key={image.id}
                  type="button"
                  onClick={(e) => handleImageSelect(e, index)}
                  className={`relative flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                    index === currentImageIndex
                      ? 'border-blue-500 ring-2 ring-blue-200 shadow-md'
                      : 'border-gray-300 hover:border-gray-400 hover:shadow-sm'
                  }`}
                  title={`Hình ảnh ${index + 1} - ${image.type === 'preview' ? 'Xem trước' : 'Đã lưu'}`}
                >
                  {image.type === 'preview' ? (
                    <img
                      src={image.url}
                      alt={`Thumbnail ${index + 1}`}
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  ) : (
                    <ImageWithFallback
                      src={image.url}
                      alt={`Thumbnail ${index + 1}`}
                      className="w-full h-full object-cover"
                      fallbackSrc="https://via.placeholder.com/80x80?text=?"
                    />
                  )}

                  {/* Badge nhỏ cho thumbnail */}
                  <div className={`absolute top-1 right-1 w-2 h-2 rounded-full ${
                    image.type === 'preview' ? 'bg-blue-500' : 'bg-green-500'
                  }`}></div>

                  {/* Active indicator */}
                  {index === currentImageIndex && (
                    <div className="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">{index + 1}</span>
                      </div>
                    </div>
                  )}
                </button>
              ))}
            </div>

            {/* Scroll hint */}
            {allImages.length > 5 && (
              <div className="text-center mt-2">
                <p className="text-xs text-gray-500">← Cuộn để xem thêm hình ảnh →</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Thông tin phòng */}
      <div className="p-4">
        <h2 className="text-xl font-bold text-gray-800 mb-2">{formData.title || 'Tiêu đề phòng trọ'}</h2>

        {/* Thông tin cơ bản */}
        <div className="flex flex-wrap gap-3 mb-3">
          <div className="flex items-center text-gray-600">
            <FaMapMarkerAlt className="mr-1 text-primary" />
            <span>
              {formData.address?.street}, {formData.address?.ward}, {formData.address?.district}, {formData.address?.city}
            </span>
          </div>
        </div>

        <div className="flex flex-wrap gap-4 mb-4">
          <div className="flex items-center text-gray-600">
            <FaMoneyBillWave className="mr-1 text-primary" />
            <span className="font-semibold text-primary">
              {formatCurrency(formData.price || 0)}/tháng
            </span>
          </div>

          <div className="flex items-center text-gray-600">
            <FaRuler className="mr-1 text-primary" />
            <span>{formData.area || 0} m²</span>
          </div>

          <div className="flex items-center text-gray-600">
            <FaCalendarAlt className="mr-1 text-primary" />
            <span>{formatDate(new Date())}</span>
          </div>
        </div>

        {/* Loại phòng */}
        {category.name && (
          <div className="mb-3">
            <span className="inline-block bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded-md">
              {category.name}
            </span>
          </div>
        )}

        {/* Mô tả */}
        <div className="mb-4">
          <h3 className="text-lg font-semibold mb-2">Mô tả</h3>
          <p className="text-gray-700 whitespace-pre-line">
            {formData.description || 'Chưa có mô tả'}
          </p>
        </div>

        {/* Tiện nghi */}
        {selectedAmenities.length > 0 && (
          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">Tiện nghi</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {selectedAmenities.map((amenity) => (
                <div key={amenity._id} className="flex items-center text-gray-700">
                  <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
                  {amenity.name}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Thông tin thêm */}
        {formData.deposit && (
          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">Thông tin thêm</h3>
            <div className="text-gray-700">
              <p><span className="font-medium">Tiền cọc:</span> {formatCurrency(formData.deposit)}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

RoomPreview.propTypes = {
  formData: PropTypes.object.isRequired,
  categories: PropTypes.array,
  amenities: PropTypes.array,
  previewImages: PropTypes.array,
  existingImages: PropTypes.array,
};

export default RoomPreview;
