<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Trigger Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 10px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Manual Trigger Test</h1>
        <p>Test các chức năng thủ công</p>
        
        <div>
            <button class="btn-primary" onclick="runExpiryJob()">Chạy Expiry Job</button>
            <button class="btn-success" onclick="checkExpiringRooms()">Kiểm tra tin sắp hết hạn</button>
            <button class="btn-danger" onclick="createTestRoom()">Tạo test room (1 phút)</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(div);
            console.log(message);
        }

        async function runExpiryJob() {
            try {
                log('🔄 Running expiry job...', 'info');
                
                const response = await fetch('http://localhost:5000/api/test/run-highlight-expiry-job', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Job completed: ${data.data.processed} rooms processed`, 'success');
                    log(`⏱️ Duration: ${data.data.duration}ms`, 'info');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function checkExpiringRooms() {
            try {
                log('🔍 Checking expiring rooms...', 'info');
                
                const response = await fetch('http://localhost:5000/api/test/expiring-highlights');
                const data = await response.json();
                
                if (data.success) {
                    log(`📊 Found ${data.data.length} expiring rooms`, 'info');
                    data.data.forEach(room => {
                        log(`   - ${room.title} (${room.minutesLeft} minutes left)`, 'info');
                    });
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function createTestRoom() {
            try {
                // First get a room to test with
                log('📡 Getting rooms...', 'info');
                const roomsResponse = await fetch('http://localhost:5000/api/rooms?limit=1');
                const roomsData = await roomsResponse.json();
                
                if (!roomsData.success || roomsData.data.rooms.length === 0) {
                    throw new Error('No rooms found');
                }
                
                const roomId = roomsData.data.rooms[0]._id;
                log(`🎯 Using room: ${roomsData.data.rooms[0].title}`, 'info');
                
                // Create test highlight
                log('🚀 Creating test highlight (1 minute)...', 'info');
                const response = await fetch('http://localhost:5000/api/test/create-test-highlight', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        roomId: roomId,
                        minutes: 1
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Test highlight created!`, 'success');
                    log(`⏰ Will expire in 1 minute`, 'info');
                    log(`🔗 Open homepage to see real-time update`, 'info');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Auto-check expiring rooms on load
        window.onload = () => {
            checkExpiringRooms();
        };
    </script>
</body>
</html>
