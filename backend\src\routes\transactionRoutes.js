const express = require('express');
const router = express.Router();
const {
  createPayment,
  vnpayReturn,
  verifyPayment,
  getTransactionById,
  getMyTransactions
} = require('../controllers/transactionController');
const { protect } = require('../middlewares/authMiddleware');

/**
 * @swagger
 * tags:
 *   name: Transactions
 *   description: API quản lý giao dịch thanh toán
 */

/**
 * @swagger
 * /transactions/vnpay-return:
 *   get:
 *     summary: Callback từ VNPay sau khi thanh toán
 *     tags: [Transactions]
 *     parameters:
 *       - in: query
 *         name: vnp_ResponseCode
 *         schema:
 *           type: string
 *         description: Mã phản hồi từ VNPay
 *       - in: query
 *         name: vnp_TxnRef
 *         schema:
 *           type: string
 *         description: Mã tham chiếu giao dịch
 *       - in: query
 *         name: vnp_TransactionNo
 *         schema:
 *           type: string
 *         description: Mã giao dịch VNPay
 *       - in: query
 *         name: vnp_Amount
 *         schema:
 *           type: string
 *         description: Số tiền thanh toán (đã nhân 100)
 *       - in: query
 *         name: vnp_SecureHash
 *         schema:
 *           type: string
 *         description: Mã hash bảo mật
 *     responses:
 *       302:
 *         description: Chuyển hướng đến trang kết quả thanh toán
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
// Debug endpoint to test if route is working
router.get('/test-callback', (req, res) => {
  console.log('[Test Callback] Route is working!');
  console.log('[Test Callback] Query params:', req.query);
  res.json({ success: true, message: 'Test callback route working', params: req.query });
});

router.get('/vnpay-return', vnpayReturn);

/**
 * @swagger
 * /transactions/verify-payment:
 *   get:
 *     summary: Xác nhận kết quả thanh toán từ VNPay
 *     tags: [Transactions]
 *     parameters:
 *       - in: query
 *         name: vnp_ResponseCode
 *         schema:
 *           type: string
 *         description: Mã phản hồi từ VNPay
 *       - in: query
 *         name: vnp_TxnRef
 *         schema:
 *           type: string
 *         description: Mã tham chiếu giao dịch
 *     responses:
 *       200:
 *         description: Kết quả xác nhận thanh toán
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/verify-payment', verifyPayment);

/**
 * @swagger
 * /transactions/create-payment:
 *   post:
 *     summary: Tạo giao dịch thanh toán mới
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - roomId
 *               - packageType
 *             properties:
 *               roomId:
 *                 type: string
 *                 description: ID của phòng trọ cần thanh toán
 *               packageType:
 *                 type: string
 *                 enum: [1_week, 2_weeks, 1_month]
 *                 description: Loại gói tin nổi bật
 *     responses:
 *       200:
 *         description: URL thanh toán VNPay
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     paymentUrl:
 *                       type: string
 *                       example: https://sandbox.vnpayment.vn/paymentv2/vpcpay.html?...
 *       400:
 *         description: Dữ liệu không hợp lệ
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Không có quyền thanh toán
 *       404:
 *         description: Không tìm thấy phòng trọ
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post('/create-payment', protect, createPayment);

/**
 * @swagger
 * /transactions/my-transactions:
 *   get:
 *     summary: Lấy danh sách giao dịch của người dùng hiện tại
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Danh sách giao dịch
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Transaction'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/my-transactions', protect, getMyTransactions);

/**
 * @swagger
 * /transactions/{id}:
 *   get:
 *     summary: Lấy chi tiết giao dịch
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: ID của giao dịch
 *     responses:
 *       200:
 *         description: Chi tiết giao dịch
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Transaction'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Không có quyền xem giao dịch này
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/:id', protect, getTransactionById);

module.exports = router;
