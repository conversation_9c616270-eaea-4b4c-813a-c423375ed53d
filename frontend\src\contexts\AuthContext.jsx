import { createContext, useContext } from 'react';
import { useAuth } from '../hooks';

// Tạo context cho xác thực
const AuthContext = createContext(null);

// Provider component
export const AuthProvider = ({ children }) => {
  const auth = useAuth();

  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook để sử dụng AuthContext
export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext phải được sử dụng trong AuthProvider');
  }
  return context;
};
