import { io } from 'socket.io-client';
import { toast } from 'react-toastify';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.isAuthenticated = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.listeners = new Map(); // Event listeners storage
  }

  // Khởi tạo kết nối WebSocket
  connect(token = null) {
    try {
      // Disconnect existing connection
      if (this.socket) {
        this.disconnect();
      }

      // Create new connection
      this.socket = io(import.meta.env.VITE_API_URL || 'http://localhost:5000', {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000
      });

      this.setupEventHandlers();

      // Authenticate if token provided
      if (token) {
        this.authenticate(token);
      }

      console.log('[SocketService] Attempting to connect...');

    } catch (error) {
      console.error('[SocketService] Connection error:', error);
    }
  }

  // Thiết lập event handlers c<PERSON> bản
  setupEventHandlers() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('[SocketService] Connected to server');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Re-authenticate if we have a token
      const token = localStorage.getItem('token');
      if (token && !this.isAuthenticated) {
        this.authenticate(token);
      }
    });

    this.socket.on('disconnect', (reason) => {
      console.log('[SocketService] Disconnected:', reason);
      this.isConnected = false;
      this.isAuthenticated = false;
    });

    this.socket.on('connect_error', (error) => {
      console.error('[SocketService] Connection error:', error);
      this.reconnectAttempts++;
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('[SocketService] Max reconnection attempts reached');
        toast.error('Mất kết nối với server. Vui lòng refresh trang.');
      }
    });

    // Authentication events
    this.socket.on('authenticated', (data) => {
      console.log('[SocketService] Authenticated:', data);
      this.isAuthenticated = true;
      
      // Join user-specific room
      this.socket.emit('join_user_room', data.userId);
    });

    this.socket.on('auth_error', (error) => {
      console.error('[SocketService] Authentication error:', error);
      this.isAuthenticated = false;
    });

    // Highlight expiry events
    this.socket.on('highlight_expired', (data) => {
      console.log('[SocketService] Highlight expired:', data);
      
      // Show toast notification
      toast.info(`Tin đăng "${data.title}" đã hết hạn gói tin nổi bật`, {
        position: 'top-right',
        autoClose: 5000
      });

      // Emit to custom listeners
      this.emitToListeners('highlight_expired', data);
    });

    this.socket.on('highlight_activated', (data) => {
      console.log('[SocketService] Highlight activated:', data);
      
      // Show toast notification
      toast.success(`Tin đăng "${data.title}" đã được nâng cấp lên gói tin nổi bật`, {
        position: 'top-right',
        autoClose: 5000
      });

      // Emit to custom listeners
      this.emitToListeners('highlight_activated', data);
    });

    this.socket.on('room_highlight_updated', (data) => {
      console.log('[SocketService] Room highlight updated:', data);
      
      // Emit to custom listeners for UI updates
      this.emitToListeners('room_highlight_updated', data);
    });

    // Ping/Pong for connection health
    this.socket.on('pong', () => {
      // Connection is healthy
    });

    // Send ping every 30 seconds
    setInterval(() => {
      if (this.isConnected) {
        this.socket.emit('ping');
      }
    }, 30000);
  }

  // Xác thực với server
  authenticate(token) {
    if (!this.socket || !this.isConnected) {
      console.warn('[SocketService] Cannot authenticate: not connected');
      return;
    }

    console.log('[SocketService] Authenticating...');
    this.socket.emit('authenticate', token);
  }

  // Ngắt kết nối
  disconnect() {
    if (this.socket) {
      console.log('[SocketService] Disconnecting...');
      this.socket.disconnect();
      this.socket = null;
    }
    
    this.isConnected = false;
    this.isAuthenticated = false;
    this.listeners.clear();
  }

  // Thêm event listener tùy chỉnh
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);

    // Return unsubscribe function
    return () => {
      const eventListeners = this.listeners.get(event);
      if (eventListeners) {
        eventListeners.delete(callback);
        if (eventListeners.size === 0) {
          this.listeners.delete(event);
        }
      }
    };
  }

  // Xóa event listener
  removeEventListener(event, callback) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(callback);
      if (eventListeners.size === 0) {
        this.listeners.delete(event);
      }
    }
  }

  // Emit event đến các listeners
  emitToListeners(event, data) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[SocketService] Error in listener for ${event}:`, error);
        }
      });
    }
  }

  // Kiểm tra trạng thái kết nối
  isSocketConnected() {
    return this.isConnected && this.socket && this.socket.connected;
  }

  // Kiểm tra trạng thái xác thực
  isSocketAuthenticated() {
    return this.isAuthenticated;
  }

  // Emit event đến server
  emit(event, data) {
    if (this.socket && this.isConnected) {
      this.socket.emit(event, data);
      return true;
    }
    console.warn(`[SocketService] Cannot emit ${event}: not connected`);
    return false;
  }

  // Lắng nghe event từ server (one-time)
  on(event, callback) {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  // Bỏ lắng nghe event
  off(event, callback) {
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }
}

// Export singleton instance
const socketService = new SocketService();

export default socketService;
