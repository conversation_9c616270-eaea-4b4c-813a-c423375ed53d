import { useState, useCallback } from 'react';
import { transactionService } from '../services';

// Hook quản lý các thao tác với giao dịch
const useTransactions = () => {
  const [transactions, setTransactions] = useState([]);
  const [transaction, setTransaction] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    totalPages: 0,
    totalResults: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Tạo giao dịch thanh toán mới cho gói tin nổi bật
  const createPayment = async (paymentData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await transactionService.createPayment(paymentData);
      return response;
    } catch (err) {
      setError(err.message || 'Không thể tạo giao dịch thanh toán');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // <PERSON><PERSON>y danh sách giao dịch của người dùng đã đăng nhập
  const fetchMyTransactions = useCallback(async (filters = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await transactionService.getMyTransactions(filters);
      if (response.success) {
        setTransactions(response.data.transactions);
        setPagination(response.data.pagination);
      }
      return response;
    } catch (err) {
      setError(err.message || 'Không thể lấy danh sách giao dịch');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Lấy thông tin chi tiết của một giao dịch
  const fetchTransactionById = useCallback(async (id) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await transactionService.getTransactionById(id);
      if (response.success) {
        setTransaction(response.data);
      }
      return response;
    } catch (err) {
      setError(err.message || 'Không thể lấy thông tin giao dịch');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    transactions,
    transaction,
    pagination,
    isLoading,
    error,
    createPayment,
    fetchMyTransactions,
    fetchTransactionById,
  };
};

export default useTransactions;
