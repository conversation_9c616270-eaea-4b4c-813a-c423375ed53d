const nodemailer = require('nodemailer');
// Sử dụng crypto có sẵn trong Node.js thay vì crypto-random-string
const crypto = require('crypto');

// Hàm tạo chuỗi ngẫu nhiên an toàn
const generateRandomString = (length, type = 'base64url') => {
  return crypto.randomBytes(length)
    .toString(type === 'url-safe' ? 'base64url' : 'hex')
    .slice(0, length);
};

// Tạo transporter cho nodemailer
const createTransporter = async () => {
  // Trong môi trường phát triển, sử dụng Ethereal để test email
  if (process.env.NODE_ENV === 'development') {
    // Tạo tài khoản Ethereal tạm thời nếu chưa có
    if (!process.env.ETHEREAL_EMAIL || !process.env.ETHEREAL_PASSWORD) {
      try {
        // Tạo tài khoản test Ethereal
        const testAccount = await nodemailer.createTestAccount();
        console.log('Tà<PERSON> khoản Ethereal đã được tạo:', {
          email: testAccount.user,
          password: testAccount.pass
        });

        // Sử dụng tài khoản vừa tạo
        return nodemailer.createTransport({
          host: 'smtp.ethereal.email',
          port: 587,
          secure: false,
          auth: {
            user: testAccount.user,
            pass: testAccount.pass
          }
        });
      } catch (error) {
        console.error('Không thể tạo tài khoản Ethereal:', error);
        // Fallback: sử dụng Gmail nếu không tạo được tài khoản Ethereal
        return nodemailer.createTransport({
          service: 'gmail',
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASSWORD
          }
        });
      }
    } else {
      // Sử dụng tài khoản Ethereal đã cấu hình
      return nodemailer.createTransport({
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: {
          user: process.env.ETHEREAL_EMAIL,
          pass: process.env.ETHEREAL_PASSWORD
        }
      });
    }
  }

  // Trong môi trường sản xuất, sử dụng Gmail
  console.log('Đang tạo transporter cho Gmail với tài khoản:', process.env.EMAIL_USER);

  // Cấu hình tối ưu cho Gmail
  const transporter = nodemailer.createTransport({
    service: 'gmail',  // Sử dụng cấu hình có sẵn của Gmail
    host: 'smtp.gmail.com',
    port: 465,
    secure: true,  // Sử dụng SSL
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD  // Phải là App Password nếu bật xác thực 2 bước
    },
    debug: true  // Bật debug để ghi log chi tiết
  });

  // Kiểm tra kết nối SMTP trước khi trả về transporter
  try {
    await transporter.verify();
    console.log('Kết nối SMTP với Gmail thành công');
    return transporter;
  } catch (error) {
    console.error('Lỗi kết nối SMTP với Gmail:', {
      name: error.name,
      message: error.message,
      code: error.code,
      command: error.command,
      responseCode: error.responseCode
    });

    // Thử lại với cấu hình khác
    console.log('Thử lại với cấu hình SMTP khác...');
    return nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD
      },
      tls: {
        rejectUnauthorized: false  // Giúp tránh lỗi chứng chỉ SSL
      }
    });
  }
};

// Tạo token xác nhận email
const generateVerificationToken = async () => {
  // Tạo token ngẫu nhiên
  const token = generateRandomString(32, 'url-safe');

  // Thời gian hết hạn (24 giờ)
  const expires = new Date();
  expires.setHours(expires.getHours() + 24);

  return {
    token,
    expires
  };
};

// Gửi email xác nhận
const sendVerificationEmail = async (user, verificationUrl) => {
  try {
    // Lấy transporter (bất đồng bộ)
    const transporter = await createTransporter();

    // Nội dung email
    const mailOptions = {
      from: `"Hệ thống tìm phòng trọ" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: user.email,
      subject: 'Xác nhận tài khoản của bạn',
      html: `
        <h1>Xin chào ${user.fullName}!</h1>
        <p>Cảm ơn bạn đã đăng ký tài khoản trên hệ thống tìm phòng trọ của chúng tôi.</p>
        <p>Vui lòng nhấp vào liên kết dưới đây để xác nhận địa chỉ email của bạn:</p>
        <p>
          <a href="${verificationUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            Xác nhận tài khoản
          </a>
        </p>
        <p>Hoặc bạn có thể sao chép và dán liên kết này vào trình duyệt của bạn:</p>
        <p>${verificationUrl}</p>
        <p>Liên kết này sẽ hết hạn sau 24 giờ.</p>
        <p>Nếu bạn không đăng ký tài khoản, vui lòng bỏ qua email này.</p>
        <p>Trân trọng,</p>
        <p>Đội ngũ hệ thống tìm phòng trọ</p>
      `
    };

    console.log('Đang gửi email đến:', user.email);

    // Gửi email
    console.log('Đang gửi email với cấu hình:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject
    });

    try {
      console.log('Bắt đầu gửi email với cấu hình:', {
        host: transporter.options.host,
        port: transporter.options.port,
        secure: transporter.options.secure,
        auth: {
          user: transporter.options.auth.user,
          // Không hiển thị mật khẩu đầy đủ
          pass: transporter.options.auth.pass ? '********' : 'không có'
        }
      });

      const info = await transporter.sendMail(mailOptions);
      console.log('Email đã được gửi thành công:', {
        messageId: info.messageId,
        response: info.response,
        envelope: info.envelope,
        accepted: info.accepted,
        rejected: info.rejected
      });

      // Trong môi trường phát triển, trả về URL để xem email
      if (process.env.NODE_ENV === 'development') {
        const previewUrl = nodemailer.getTestMessageUrl(info);
        console.log('URL để xem email: %s', previewUrl);

        // Chuyển tiếp email đến địa chỉ thực của người dùng
        try {
          console.log('Đang chuyển tiếp email đến địa chỉ thực:', user.email);

          // Tạo transporter Gmail
          const gmailTransporter = nodemailer.createTransport({
            service: 'gmail',
            auth: {
              user: process.env.EMAIL_USER,
              pass: process.env.EMAIL_PASSWORD
            }
          });

          // Tạo nội dung email chuyển tiếp
          const forwardOptions = {
            from: `"Hệ thống tìm phòng trọ" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
            to: user.email,
            subject: `${mailOptions.subject} [Chuyển tiếp từ môi trường phát triển]`,
            html: `
              <p><strong>Email này được chuyển tiếp từ môi trường phát triển.</strong></p>
              <p>Xem email gốc tại: <a href="${previewUrl}">${previewUrl}</a></p>
              <hr>
              ${mailOptions.html}
            `
          };

          // Gửi email chuyển tiếp
          const forwardResult = await gmailTransporter.sendMail(forwardOptions);
          console.log('Email chuyển tiếp đã được gửi thành công:', {
            messageId: forwardResult.messageId,
            response: forwardResult.response
          });
        } catch (forwardError) {
          console.error('Lỗi khi chuyển tiếp email:', {
            name: forwardError.name,
            message: forwardError.message,
            code: forwardError.code,
            command: forwardError.command,
            responseCode: forwardError.responseCode
          });
        }

        return {
          success: true,
          previewUrl
        };
      }

      return {
        success: true,
        messageId: info.messageId,
        response: info.response
      };
    } catch (sendError) {
      console.error('Lỗi khi gửi email:', {
        code: sendError.code,
        responseCode: sendError.responseCode,
        command: sendError.command,
        message: sendError.message,
        stack: sendError.stack
      });

      // Thử lại với cấu hình khác nếu gặp lỗi
      if (process.env.NODE_ENV === 'production') {
        try {
          console.log('Thử lại gửi email với cấu hình khác...');

          // Tạo transporter mới với cấu hình khác
          const fallbackTransporter = nodemailer.createTransport({
            host: 'smtp.gmail.com',
            port: 587,
            secure: false,
            auth: {
              user: process.env.EMAIL_USER,
              pass: process.env.EMAIL_PASSWORD
            },
            tls: {
              rejectUnauthorized: false
            }
          });

          // Gửi lại email
          const retryInfo = await fallbackTransporter.sendMail(mailOptions);
          console.log('Email đã được gửi thành công sau khi thử lại:', {
            messageId: retryInfo.messageId,
            response: retryInfo.response
          });

          return {
            success: true,
            messageId: retryInfo.messageId,
            response: retryInfo.response,
            retried: true
          };
        } catch (retryError) {
          console.error('Lỗi khi thử lại gửi email:', {
            code: retryError.code,
            responseCode: retryError.responseCode,
            message: retryError.message
          });

          throw sendError; // Ném lỗi ban đầu để xử lý ở catch bên ngoài
        }
      } else {
        throw sendError; // Ném lỗi để xử lý ở catch bên ngoài
      }
    }
  } catch (error) {
    console.error('Lỗi tổng thể khi gửi email xác nhận:', {
      name: error.name,
      message: error.message,
      code: error.code,
      command: error.command,
      responseCode: error.responseCode
    });

    return {
      success: false,
      error: error.message,
      code: error.code,
      responseCode: error.responseCode,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    };
  }
};

// Kiểm tra kết nối SMTP
const verifyEmailConnection = async () => {
  try {
    const transporter = await createTransporter();

    // Kiểm tra kết nối
    const verification = await transporter.verify();
    console.log('Kết nối SMTP thành công:', verification);

    return {
      success: true,
      message: 'Kết nối SMTP thành công'
    };
  } catch (error) {
    console.error('Lỗi kết nối SMTP:', {
      name: error.name,
      message: error.message,
      code: error.code,
      command: error.command,
      responseCode: error.responseCode
    });

    return {
      success: false,
      error: error.message,
      code: error.code,
      responseCode: error.responseCode
    };
  }
};

// Gửi email reset mật khẩu
const sendPasswordResetEmail = async (user, resetUrl) => {
  try {
    // Lấy transporter (bất đồng bộ)
    const transporter = await createTransporter();

    // Nội dung email
    const mailOptions = {
      from: `"Hệ thống tìm phòng trọ" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: user.email,
      subject: 'Yêu cầu reset mật khẩu',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #333; text-align: center;">Reset Mật Khẩu</h1>

          <p>Xin chào <strong>${user.fullName}</strong>!</p>

          <p>Chúng tôi nhận được yêu cầu reset mật khẩu cho tài khoản của bạn.</p>

          <p>Vui lòng nhấp vào nút dưới đây để tạo mật khẩu mới:</p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}"
               style="display: inline-block; background-color: #f44336; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
              Reset Mật Khẩu
            </a>
          </div>

          <p>Hoặc bạn có thể sao chép và dán liên kết này vào trình duyệt của bạn:</p>
          <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 5px;">
            ${resetUrl}
          </p>

          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
            <h3 style="color: #856404; margin-top: 0;">⚠️ Lưu ý quan trọng:</h3>
            <ul style="color: #856404; margin: 0;">
              <li>Liên kết này sẽ hết hạn sau 24 giờ</li>
              <li>Nếu bạn không yêu cầu reset mật khẩu, vui lòng bỏ qua email này</li>
              <li>Để bảo mật tài khoản, không chia sẻ liên kết này với ai khác</li>
            </ul>
          </div>

          <p>Nếu bạn gặp vấn đề, vui lòng liên hệ với chúng tôi.</p>

          <p style="margin-top: 30px;">
            Trân trọng,<br>
            <strong>Đội ngũ hỗ trợ</strong>
          </p>

          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          <p style="font-size: 12px; color: #666; text-align: center;">
            Email này được gửi tự động, vui lòng không trả lời.
          </p>
        </div>
      `
    };

    // Gửi email
    const info = await transporter.sendMail(mailOptions);
    console.log('Email reset mật khẩu đã được gửi:', {
      messageId: info.messageId,
      to: user.email,
      response: info.response
    });

    let previewUrl = null;

    // Nếu sử dụng Ethereal Email (môi trường phát triển)
    if (process.env.NODE_ENV === 'development' && info.messageId) {
      try {
        previewUrl = nodemailer.getTestMessageUrl(info);
        console.log('Preview URL:', previewUrl);

        // Chuyển tiếp email đến địa chỉ thực của người dùng
        try {
          console.log('Đang chuyển tiếp email reset mật khẩu đến địa chỉ thực:', user.email);

          // Tạo transporter Gmail
          const gmailTransporter = nodemailer.createTransporter({
            service: 'gmail',
            auth: {
              user: process.env.EMAIL_USER,
              pass: process.env.EMAIL_PASSWORD
            }
          });

          // Tạo nội dung email chuyển tiếp
          const forwardOptions = {
            from: `"Hệ thống tìm phòng trọ" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
            to: user.email,
            subject: `${mailOptions.subject} [Chuyển tiếp từ môi trường phát triển]`,
            html: `
              <p><strong>Email này được chuyển tiếp từ môi trường phát triển.</strong></p>
              <p>Xem email gốc tại: <a href="${previewUrl}">${previewUrl}</a></p>
              <hr>
              ${mailOptions.html}
            `
          };

          // Gửi email chuyển tiếp
          const forwardInfo = await gmailTransporter.sendMail(forwardOptions);
          console.log('Email reset mật khẩu đã được chuyển tiếp:', {
            messageId: forwardInfo.messageId,
            to: user.email,
            response: forwardInfo.response
          });
        } catch (forwardError) {
          console.error('Lỗi chuyển tiếp email reset mật khẩu:', forwardError);
          // Không throw error vì email chính đã được gửi thành công
        }
      } catch (previewError) {
        console.error('Lỗi tạo preview URL:', previewError);
      }
    }

    return {
      success: true,
      messageId: info.messageId,
      response: info.response,
      previewUrl
    };
  } catch (error) {
    console.error('Lỗi gửi email reset mật khẩu:', {
      name: error.name,
      message: error.message,
      code: error.code,
      command: error.command,
      responseCode: error.responseCode
    });

    return {
      success: false,
      error: error.message,
      code: error.code,
      responseCode: error.responseCode
    };
  }
};

module.exports = {
  generateVerificationToken,
  sendVerificationEmail,
  sendPasswordResetEmail,
  verifyEmailConnection,
  createTransporter
};
