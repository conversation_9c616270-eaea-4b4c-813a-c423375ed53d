import { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaUser, FaSignOutAlt, FaPlus, FaSearch, FaBars, FaTimes, FaUserShield } from 'react-icons/fa';
import { useAuthContext } from '../contexts';

const Header = () => {
  const { isAuthenticated, user, logout, checkAuthStatus } = useAuthContext();
  const navigate = useNavigate();

  // State cho menu di động
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // State cho dropdown menu
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  // Ref cho dropdown menu
  const userMenuRef = useRef(null);

  // Listen for storage changes để cập nhật Header real-time
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'token' || e.key === 'user') {
        console.log('Header: Detected localStorage change, checking auth status');
        checkAuthStatus();
      }
    };

    // Listen for storage events (từ tabs khác)
    window.addEventListener('storage', handleStorageChange);

    // Listen for custom events (từ cùng tab)
    const handleAuthChange = () => {
      console.log('Header: Detected auth change event, checking auth status');
      checkAuthStatus();
    };

    window.addEventListener('authChange', handleAuthChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('authChange', handleAuthChange);
    };
  }, [checkAuthStatus]);

  // Đóng dropdown menu khi click ra ngoài
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Function để scroll to top mượt mà
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  };

  // Xử lý click navigation link (scroll to top + đóng mobile menu)
  const handleNavigationClick = () => {
    setMobileMenuOpen(false);
    // Delay scroll để đảm bảo navigation hoàn tất
    setTimeout(() => {
      scrollToTop();
    }, 100);
  };

  // Xử lý đăng xuất
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Lỗi khi đăng xuất:', error);
    }
  };

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="container-custom py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-3" onClick={handleNavigationClick}>
              {/* Logo */}
              <div className="w-14 h-14 rounded-lg overflow-hidden flex items-center justify-center">
                <img
                  src="../assets/logo.jpg"
                  alt="Logo"
                  className="w-full h-full object-contain"
                  onError={(e) => {
                    // Fallback to SVG icon if image fails to load
                    e.target.style.display = 'none';
                    e.target.nextElementSibling.style.display = 'block';
                  }}
                />
                {/* Fallback SVG icon */}
                <div className="w-14 h-14 bg-primary rounded-lg flex items-center justify-center" style={{display: 'none'}}>
                  <svg
                    className="w-6 h-6 text-white"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                  </svg>
                </div>
              </div>
              {/* Text */}
              <span className="text-2xl font-bold text-primary">
                Tìm Phòng Trọ
              </span>
            </Link>
          </div>

          {/* Menu desktop */}
          <nav className="hidden md:flex space-x-6">
            <Link to="/" className="nav-link" onClick={handleNavigationClick}>
              Trang Chủ
            </Link>
            <Link to="/search" className="nav-link" onClick={handleNavigationClick}>
              Tìm Phòng
            </Link>
            <Link to="/rooms/create" className="nav-link" onClick={handleNavigationClick}>
              Đăng Tin
            </Link>
            <Link to="/about" className="nav-link" onClick={handleNavigationClick}>
              Giới Thiệu
            </Link>
          </nav>

          <div className="flex items-center space-x-3">
            {/* Nút tìm kiếm (hiển thị trên mobile) */}
            <Link to="/search" className="md:hidden p-2 rounded-full hover:bg-gray-100" onClick={handleNavigationClick}>
              <FaSearch className="text-gray-700" />
            </Link>

            {/* Đăng nhập/Đăng ký hoặc Menu người dùng */}
            {isAuthenticated ? (
              <div className="relative" ref={userMenuRef}>
                <button
                  className="flex items-center space-x-2 py-2 px-3 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                >
                  <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white">
                    <FaUser />
                  </div>
                  <span className="hidden md:block font-medium text-gray-800">
                    {user?.fullName?.split(' ').pop() || 'Tài khoản'}
                  </span>
                </button>

                {/* Dropdown menu */}
                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-10 border border-gray-200">
                    <div className="px-4 py-2 border-b border-gray-200">
                      <p className="text-sm font-medium text-gray-800">{user?.fullName}</p>
                      <p className="text-xs text-gray-500 truncate">{user?.email}</p>
                    </div>
                    <Link
                      to="/profile"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => {
                        setUserMenuOpen(false);
                        scrollToTop();
                      }}
                    >
                      Tài khoản của tôi
                    </Link>
                    <Link
                      to="/my-rooms"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => {
                        setUserMenuOpen(false);
                        scrollToTop();
                      }}
                    >
                      Phòng trọ của tôi
                    </Link>
                    {user?.role === 'admin' && (
                      <Link
                        to="/admin"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setUserMenuOpen(false);
                          scrollToTop();
                        }}
                      >
                        <div className="flex items-center">
                          <FaUserShield className="mr-2" />
                          Quản trị hệ thống
                        </div>
                      </Link>
                    )}
                    <button
                      className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                      onClick={handleLogout}
                    >
                      <div className="flex items-center">
                        <FaSignOutAlt className="mr-2" />
                        Đăng xuất
                      </div>
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <Link to="/login" className="btn btn-primary" onClick={scrollToTop}>
                  Đăng Nhập
                </Link>
                <Link to="/register" className="hidden md:block btn btn-outline" onClick={scrollToTop}>
                  Đăng Ký
                </Link>
              </div>
            )}

            {/* Nút đăng tin nhanh (hiển thị trên desktop) */}
            <Link
              to="/rooms/create"
              className="hidden md:flex items-center space-x-1 btn btn-secondary"
              onClick={handleNavigationClick}
            >
              <FaPlus className="text-xs" />
              <span>Đăng Tin</span>
            </Link>

            {/* Nút menu di động */}
            <button
              className="md:hidden p-2 rounded-full hover:bg-gray-100"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <FaTimes className="text-gray-700" /> : <FaBars className="text-gray-700" />}
            </button>
          </div>
        </div>

        {/* Menu di động */}
        {mobileMenuOpen && (
          <div className="md:hidden mt-4 py-4 border-t border-gray-200">
            <nav className="flex flex-col space-y-4">
              <Link
                to="/"
                className="nav-link"
                onClick={handleNavigationClick}
              >
                Trang Chủ
              </Link>
              <Link
                to="/search"
                className="nav-link"
                onClick={handleNavigationClick}
              >
                Tìm Phòng
              </Link>
              <Link
                to="/rooms/create"
                className="nav-link"
                onClick={handleNavigationClick}
              >
                Đăng Tin
              </Link>
              <Link
                to="/about"
                className="nav-link"
                onClick={handleNavigationClick}
              >
                Giới Thiệu
              </Link>

              {!isAuthenticated && (
                <Link
                  to="/register"
                  className="nav-link"
                  onClick={handleNavigationClick}
                >
                  Đăng Ký
                </Link>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}

export default Header
