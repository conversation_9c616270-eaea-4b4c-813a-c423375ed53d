import { useState, useEffect } from 'react';
import { FaSearch, FaEdit, FaTrash, FaPlus, FaExclamationTriangle } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useAuthContext } from '../../contexts';
import Pagination from '../../components/Pagination';
import Modal from '../../components/Modal';

const AmenityManagement = () => {
  const { token } = useAuthContext();
  const [amenities, setAmenities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [search, setSearch] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Modals
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedAmenity, setSelectedAmenity] = useState(null);
  const [formData, setFormData] = useState({
    name: ''
  });

  // Fetch amenities
  const fetchAmenities = async () => {
    try {
      setLoading(true);

      // Build query params
      const params = new URLSearchParams();
      params.append('page', pagination.page);
      params.append('limit', pagination.limit);
      if (search) params.append('search', search);

      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/amenities?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Không thể lấy danh sách tiện nghi');
      }

      setAmenities(data.data.amenities);
      setPagination({
        page: data.data.pagination.page,
        limit: data.data.pagination.limit,
        total: data.data.pagination.total,
        pages: data.data.pagination.pages
      });
    } catch (error) {
      console.error('Lỗi khi lấy danh sách tiện nghi:', error);
      setError(error.message);
      toast.error(`Lỗi: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAmenities();
  }, [token, pagination.page, pagination.limit]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchAmenities();
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Open add modal
  const openAddModal = () => {
    setFormData({ name: '' });
    setShowAddModal(true);
  };

  // Open edit modal
  const openEditModal = (amenity) => {
    setSelectedAmenity(amenity);
    setFormData({
      name: amenity.name
    });
    setShowEditModal(true);
  };

  // Open delete modal
  const openDeleteModal = (amenity) => {
    setSelectedAmenity(amenity);
    setShowDeleteModal(true);
  };

  // Add amenity
  const handleAddAmenity = async (e) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Tên tiện nghi là bắt buộc');
      return;
    }

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/amenities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Không thể tạo tiện nghi');
      }

      toast.success('Tạo tiện nghi thành công');
      setShowAddModal(false);
      fetchAmenities();
    } catch (error) {
      console.error('Lỗi khi tạo tiện nghi:', error);
      toast.error(`Lỗi: ${error.message}`);
    }
  };

  // Edit amenity
  const handleEditAmenity = async (e) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Tên tiện nghi là bắt buộc');
      return;
    }

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/amenities/${selectedAmenity._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Không thể cập nhật tiện nghi');
      }

      toast.success('Cập nhật tiện nghi thành công');
      setShowEditModal(false);
      fetchAmenities();
    } catch (error) {
      console.error('Lỗi khi cập nhật tiện nghi:', error);
      toast.error(`Lỗi: ${error.message}`);
    }
  };

  // Delete amenity
  const handleDeleteAmenity = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/amenities/${selectedAmenity._id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Không thể xóa tiện nghi');
      }

      toast.success('Xóa tiện nghi thành công');
      setShowDeleteModal(false);
      fetchAmenities();
    } catch (error) {
      console.error('Lỗi khi xóa tiện nghi:', error);
      toast.error(`Lỗi: ${error.message}`);
    }
  };

  // Reset filters
  const resetFilters = () => {
    setSearch('');
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchAmenities();
  };



  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <FaExclamationTriangle className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Đã xảy ra lỗi khi tải dữ liệu
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Quản lý tiện nghi</h1>
        <button
          className="btn btn-primary flex items-center"
          onClick={openAddModal}
        >
          <FaPlus className="mr-2" />
          Thêm tiện nghi
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <form onSubmit={handleSearch} className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="relative">
            <input
              type="text"
              placeholder="Tìm kiếm theo tên tiện nghi..."
              className="input input-bordered w-full pr-10"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <button type="submit" className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <FaSearch className="text-gray-400" />
            </button>
          </div>

          <div className="flex space-x-2 md:col-span-2">
            <button type="submit" className="btn btn-primary flex-1 md:flex-none md:w-32">
              Tìm kiếm
            </button>
            <button type="button" onClick={resetFilters} className="btn btn-outline">
              Đặt lại
            </button>
          </div>
        </form>
      </div>

      {/* Amenities table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tên tiện nghi
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Số lượng phòng
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thao tác
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="4" className="px-6 py-4 text-center">
                    <div className="flex justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
                    </div>
                  </td>
                </tr>
              ) : amenities.length === 0 ? (
                <tr>
                  <td colSpan="4" className="px-6 py-4 text-center text-sm text-gray-500">
                    Không tìm thấy tiện nghi nào
                  </td>
                </tr>
              ) : (
                amenities.map((amenity) => (
                  <tr key={amenity._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {amenity._id.substring(0, 8)}...
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {amenity.name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {amenity.roomCount || 0} phòng
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => openEditModal(amenity)}
                          className="text-indigo-600 hover:text-indigo-900"
                          title="Chỉnh sửa"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => openDeleteModal(amenity)}
                          className={`text-red-600 hover:text-red-900 ${amenity.roomCount > 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                          title={amenity.roomCount > 0 ? 'Không thể xóa tiện nghi đang được sử dụng' : 'Xóa'}
                          disabled={amenity.roomCount > 0}
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {!loading && amenities.length > 0 && (
          <div className="px-6 py-4 bg-white border-t border-gray-200">
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.pages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* Add Amenity Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Thêm tiện nghi mới"
      >
        <div className="p-6">
          <form onSubmit={handleAddAmenity}>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tên tiện nghi <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  className="input input-bordered w-full"
                  placeholder="Nhập tên tiện nghi"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => setShowAddModal(false)}
              >
                Hủy
              </button>
              <button
                type="submit"
                className="btn btn-primary"
              >
                Thêm
              </button>
            </div>
          </form>
        </div>
      </Modal>

      {/* Edit Amenity Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Chỉnh sửa tiện nghi"
      >
        <div className="p-6">
          <form onSubmit={handleEditAmenity}>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tên tiện nghi <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  className="input input-bordered w-full"
                  placeholder="Nhập tên tiện nghi"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => setShowEditModal(false)}
              >
                Hủy
              </button>
              <button
                type="submit"
                className="btn btn-primary"
              >
                Cập nhật
              </button>
            </div>
          </form>
        </div>
      </Modal>

      {/* Delete Amenity Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Xác nhận xóa tiện nghi"
      >
        <div className="p-6">
          <p className="text-sm text-gray-500">
            Bạn có chắc chắn muốn xóa tiện nghi <span className="font-semibold">"{selectedAmenity?.name}"</span>?
            Hành động này không thể hoàn tác.
          </p>
          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              className="btn btn-outline"
              onClick={() => setShowDeleteModal(false)}
            >
              Hủy
            </button>
            <button
              type="button"
              className="btn btn-error"
              onClick={handleDeleteAmenity}
            >
              Xóa
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default AmenityManagement;
