@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }

  h1 {
    @apply text-3xl font-bold mb-4;
  }

  h2 {
    @apply text-2xl font-bold mb-3;
  }

  h3 {
    @apply text-xl font-bold mb-2;
  }
}

@layer components {
  .btn {
    @apply px-5 py-3 rounded-lg font-medium transition-colors text-base;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-dark;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-dark;
  }

  .btn-outline {
    @apply border border-gray-300 text-gray-700 hover:bg-gray-100;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Form elements */
  .form-input {
    @apply w-full px-4 py-3 text-base rounded-lg border-gray-300 shadow-sm
           focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-50
           placeholder:text-gray-400;
  }

  .form-select {
    @apply form-input appearance-none bg-no-repeat bg-right pr-10;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-size: 1.5em 1.5em;
  }

  .form-checkbox {
    @apply h-5 w-5 rounded border-gray-300 text-primary
           focus:ring-2 focus:ring-primary focus:ring-opacity-50;
  }

  .form-label {
    @apply block text-base font-medium text-gray-700 mb-2;
  }

  .form-error {
    @apply mt-1 text-sm text-red-600;
  }

  .form-group {
    @apply mb-5;
  }

  /* Navigation */
  .nav-link {
    @apply text-gray-700 hover:text-primary font-medium;
  }

  .nav-link-active {
    @apply text-primary font-medium;
  }
}
