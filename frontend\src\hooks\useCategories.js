import { useState, useCallback } from 'react';
import { categoryService } from '../services';

// Hook quản lý các thao tác với loại phòng
const useCategories = () => {
  const [categories, setCategories] = useState([]);
  const [category, setCategory] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Lấy danh sách tất cả các loại phòng
  const fetchCategories = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await categoryService.getCategories();
      if (response.success) {
        setCategories(response.data);
      }
      return response;
    } catch (err) {
      setError(err.message || 'Không thể lấy danh sách loại phòng');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // L<PERSON>y thông tin chi tiết của một loại phòng
  const fetchCategoryById = useCallback(async (id) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await categoryService.getCategoryById(id);
      if (response.success) {
        setCategory(response.data);
      }
      return response;
    } catch (err) {
      setError(err.message || 'Không thể lấy thông tin loại phòng');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Tạo loại phòng mới (chỉ admin)
  const createCategory = async (categoryData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await categoryService.createCategory(categoryData);
      return response;
    } catch (err) {
      setError(err.message || 'Không thể tạo loại phòng mới');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Cập nhật thông tin loại phòng (chỉ admin)
  const updateCategory = async (id, categoryData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await categoryService.updateCategory(id, categoryData);
      return response;
    } catch (err) {
      setError(err.message || 'Không thể cập nhật thông tin loại phòng');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Xóa loại phòng (chỉ admin)
  const deleteCategory = async (id) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await categoryService.deleteCategory(id);
      return response;
    } catch (err) {
      setError(err.message || 'Không thể xóa loại phòng');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    categories,
    category,
    isLoading,
    error,
    fetchCategories,
    fetchCategoryById,
    createCategory,
    updateCategory,
    deleteCategory,
  };
};

export default useCategories;
