import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaArrowLeft, FaCreditCard, FaSpinner, FaCheck, FaStar } from 'react-icons/fa';
import { useRooms } from '../hooks';
import { useAuthContext } from '../contexts';
import { applyVNPayPatches, redirectToVNPayWithPatches } from '../utils/vnpay-patch';
import { transactionService } from '../services';

const UpgradeRoomPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthContext();
  const { room, fetchRoomById } = useRooms();

  // Định nghĩa 2 gói đơn giản
  const packages = [
    {
      id: 'regular',
      name: '<PERSON><PERSON><PERSON>',
      price: 0,
      description: 'Tin đăng thường, hiển thị bình thường trong danh sách',
      features: [
        'Hiển thị trong danh sách tìm kiếm',
        'Thông tin cơ bản đầy đủ',
        'Hỗ trợ khách hàng cơ bản'
      ],
      isHighlighted: false
    },
    {
      id: 'special',
      name: 'Gói Đặc Biệt',
      price: 100000,
      description: 'Tin đăng nổi bật, ưu tiên hiển thị và thu hút nhiều người xem hơn',
      features: [
        'Hiển thị ưu tiên trong tìm kiếm',
        'Giao diện nổi bật với viền màu',
        'Biểu tượng "Tin nổi bật"',
        'Hỗ trợ khách hàng ưu tiên',
        'Thống kê chi tiết'
      ],
      isHighlighted: true
    }
  ];

  const [selectedPackage, setSelectedPackage] = useState(null);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);

  // Kiểm tra xác thực
  useEffect(() => {
    if (!isAuthenticated) {
      toast.error('Bạn cần đăng nhập để nâng cấp tin đăng');
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // Lấy thông tin tin đăng
  useEffect(() => {
    const loadData = async () => {
      setIsPageLoading(true);
      try {
        // Lấy thông tin tin đăng (không tăng lượt xem)
        await fetchRoomById(id, { increaseView: false });
      } catch (err) {
        console.error('Lỗi khi tải dữ liệu:', err);
        toast.error('Có lỗi xảy ra khi tải thông tin');
        navigate('/my-rooms');
      } finally {
        setIsPageLoading(false);
      }
    };

    if (id) {
      loadData();
    }
  }, [id, fetchRoomById, navigate]);

  // Xử lý khi chọn gói tin nổi bật
  const handleSelectPackage = (pkg) => {
    setSelectedPackage(pkg);
  };

  // Xử lý khi nhấn nút thanh toán
  const handlePayment = async () => {
    if (!selectedPackage) {
      toast.warning('Vui lòng chọn gói tin nổi bật');
      return;
    }

    setIsProcessing(true);
    try {
      console.log('[Payment] Processing package:', selectedPackage.id, 'for room:', id);

      // Tạo yêu cầu thanh toán với API mới
      const response = await transactionService.createPayment({
        roomId: id,
        packageType: selectedPackage.id // 'regular' hoặc 'special'
      });

      console.log('[Payment] API response:', response);

      if (response.success) {
        // Kiểm tra xem có cần thanh toán qua VNPay không
        if (response.data.paymentUrl) {
          // Gói có phí - cần thanh toán qua VNPay
          console.log('[VNPay] Chuyển hướng với enhanced patches system...');

          // Thông báo cho user về lỗi JavaScript cosmetic
          toast.info('Đang chuyển hướng đến VNPay. Nếu thấy lỗi JavaScript, đây là lỗi hiển thị không ảnh hưởng thanh toán.', {
            duration: 5000
          });

          try {
            // Sử dụng intermediate page approach để đảm bảo patches được áp dụng
            redirectToVNPayWithPatches(response.data.paymentUrl);
            console.log('[VNPay] Enhanced redirect initiated successfully');
          } catch (enhancedError) {
            console.warn('[VNPay] Enhanced redirect failed, using fallback approach:', enhancedError);

            try {
              // Fallback: Apply basic patches and redirect
              await applyVNPayPatches();
              console.log('[VNPay] Basic patches applied, redirecting...');
              window.location.href = response.data.paymentUrl;
            } catch (patchError) {
              console.warn('[VNPay] All patch methods failed, using direct redirect:', patchError);
              // Ultimate fallback: direct redirect
              window.location.href = response.data.paymentUrl;
            }
          }
        } else {
          // Gói miễn phí - xử lý thành công ngay lập tức
          console.log('[Payment] Free package processed successfully');
          toast.success(`Đã chuyển đổi thành công sang ${selectedPackage.name}!`);

          // Tải lại thông tin phòng để cập nhật trạng thái
          await fetchRoomById(id, { increaseView: false });

          // Chuyển về trang quản lý tin đăng
          setTimeout(() => {
            navigate('/my-rooms');
          }, 2000);
        }
      } else {
        throw new Error(response.message || 'Không thể tạo yêu cầu thanh toán');
      }
    } catch (err) {
      console.error('Lỗi khi tạo yêu cầu thanh toán:', err);
      toast.error(err.message || 'Có lỗi xảy ra khi tạo yêu cầu thanh toán');
    } finally {
      setIsProcessing(false);
    }
  };

  // Hiển thị loading khi đang tải dữ liệu
  if (isPageLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background dark:bg-background-dark py-8">
      <div className="container-custom">
        <div className="max-w-6xl mx-auto">
          {/* Tiêu đề và nút quay lại */}
          <div className="flex items-center mb-6">
            <button
              type="button"
              onClick={() => navigate(`/rooms/${id}`)}
              className="mr-4 p-2 rounded-full hover:bg-gray-100"
            >
              <FaArrowLeft className="text-gray-600" />
            </button>
            <h1 className="text-2xl md:text-3xl font-bold text-text dark:text-text-dark">
              Nâng cấp tin đăng
            </h1>
          </div>

          {/* Nội dung chính */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Danh sách gói tin nổi bật */}
            <div className="lg:col-span-2">
              <div className="bg-card dark:bg-card-dark rounded-lg shadow-md p-6">
                <h2 className="text-xl font-bold mb-4 text-text dark:text-text-dark">
                  Chọn gói tin nổi bật
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {packages.map((pkg) => (
                    <div
                      key={pkg.id}
                      className={`border-2 rounded-lg p-6 cursor-pointer transition-all duration-200 ${
                        selectedPackage && selectedPackage.id === pkg.id
                          ? 'border-primary bg-primary/5'
                          : 'border-gray-200 hover:border-primary/50'
                      }`}
                      onClick={() => handleSelectPackage(pkg)}
                    >
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-bold text-text dark:text-text-dark">
                          {pkg.name}
                        </h3>
                        {pkg.isHighlighted && (
                          <FaStar className="text-yellow-500" />
                        )}
                      </div>

                      <div className="mb-4">
                        <span className="text-2xl font-bold text-primary">
                          {pkg.price === 0 ? 'Miễn phí' : `${pkg.price.toLocaleString('vi-VN')} VNĐ`}
                        </span>
                      </div>

                      <p className="text-gray-600 mb-4">{pkg.description}</p>

                      <ul className="space-y-2">
                        {pkg.features.map((feature, index) => (
                          <li key={index} className="flex items-center text-sm text-gray-600">
                            <FaCheck className="text-green-500 mr-2 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>

                      {selectedPackage && selectedPackage.id === pkg.id && (
                        <div className="mt-4 p-2 bg-primary text-white text-center rounded-md">
                          Đã chọn
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Tóm tắt thanh toán */}
            <div className="lg:col-span-1">
              {selectedPackage ? (
                <div className="sticky top-4">
                  <div className="bg-card dark:bg-card-dark rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-bold mb-4 text-text dark:text-text-dark">
                      Tóm tắt thanh toán
                    </h3>

                    <div className="space-y-3 mb-4">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tin đăng:</span>
                        <span className="font-medium">{room?.title}</span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-gray-600">Gói đã chọn:</span>
                        <span className="font-medium">{selectedPackage.name}</span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-gray-600">Giá:</span>
                        <span className="font-bold text-primary">
                          {selectedPackage.price === 0 ? 'Miễn phí' : `${selectedPackage.price.toLocaleString('vi-VN')} VNĐ`}
                        </span>
                      </div>

                      {selectedPackage.price > 0 && (
                        <div className="border-t pt-3">
                          <div className="flex justify-between text-lg font-bold">
                            <span>Tổng cộng:</span>
                            <span className="text-primary">{selectedPackage.price.toLocaleString('vi-VN')} VNĐ</span>
                          </div>
                        </div>
                      )}
                    </div>

                    <button
                      type="button"
                      onClick={handlePayment}
                      disabled={isProcessing}
                      className="w-full py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                      {isProcessing ? (
                        <>
                          <FaSpinner className="animate-spin mr-2" />
                          Đang xử lý...
                        </>
                      ) : selectedPackage.price === 0 ? (
                        <>
                          <FaCheck className="mr-2" />
                          Chuyển về gói thường
                        </>
                      ) : (
                        <>
                          <FaCreditCard className="mr-2" />
                          Thanh toán qua VNPay
                        </>
                      )}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="bg-card dark:bg-card-dark rounded-lg shadow-md p-6 text-center">
                  <p className="text-gray-600 mb-4">
                    Vui lòng chọn gói tin để xem thông tin thanh toán
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpgradeRoomPage;
