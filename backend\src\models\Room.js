const mongoose = require('mongoose');

const roomSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, 'Tiêu đề là bắt buộc'],
      trim: true
    },
    description: {
      type: String,
      required: [true, '<PERSON><PERSON> tả là bắt buộc']
    },
    price: {
      type: Number,
      required: [true, '<PERSON>i<PERSON> thuê là bắt buộc'],
      min: [0, '<PERSON><PERSON><PERSON> thuê không được âm']
    },
    area: {
      type: Number,
      required: [true, 'Diện tích là bắt buộc'],
      min: [0, 'Diện tích không được âm']
    },
    deposit: {
      type: Number,
      default: 0
    },
    address: {
      street: {
        type: String,
        required: [true, 'Tên đường là bắt buộc']
      },
      ward: {
        type: String,
        required: [true, 'Phường/Xã là bắt buộc']
      },
      district: {
        type: String,
        required: [true, 'Quận/Huyện là bắt buộc']
      },
      city: {
        type: String,
        required: [true, 'Tỉnh/Thành phố là bắt buộc']
      },
      location: {
        type: {
          type: String,
          enum: ['Point'],
          default: 'Point'
        },
        coordinates: {
          type: [Number],
          default: [0, 0]
        }
      }
    },
    images: [String],
    amenities: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Amenity'
      }
    ],
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
      required: [true, 'Loại phòng là bắt buộc']
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Người đăng tin là bắt buộc']
    },
    status: {
      type: String,
      enum: ['available', 'rented', 'hidden'],
      default: 'available'
    },
    isHighlighted: {
      type: Boolean,
      default: false
    },
    highlightExpiry: {
      type: Date,
      default: null
    },
    views: {
      type: Number,
      default: 0
    }
  },
  {
    timestamps: true
  }
);

// Tạo index cho tìm kiếm địa lý
roomSchema.index({ 'address.location': '2dsphere' });

// Tạo index cho tìm kiếm theo giá và diện tích
roomSchema.index({ price: 1, area: 1 });

// Tạo index cho tìm kiếm theo địa điểm
roomSchema.index({ 'address.city': 1, 'address.district': 1, 'address.ward': 1 });

// Tạo index cho tìm kiếm theo chủ trọ
roomSchema.index({ user: 1 });

// Tạo index cho tìm kiếm theo loại phòng
roomSchema.index({ category: 1 });

// Tạo index cho tìm kiếm theo trạng thái
roomSchema.index({ status: 1 });

// Tạo index cho tìm kiếm theo tin nổi bật
roomSchema.index({ isHighlighted: 1 });

const Room = mongoose.model('Room', roomSchema);

module.exports = Room;
