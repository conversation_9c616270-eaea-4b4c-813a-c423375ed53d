const crypto = require('crypto');
const querystring = require('qs'); // Sử dụng qs như VNPay demo
const moment = require('moment');

// Tạo URL thanh toán VNPay
const createPaymentUrl = (ipAddr, orderId, amount, orderInfo, returnUrl) => {
  const tmnCode = process.env.VNPAY_TMN_CODE;
  const secretKey = process.env.VNPAY_HASH_SECRET;
  const vnpUrl = process.env.VNPAY_URL;

  const date = new Date();
  const createDate = moment(date).format('YYYYMMDDHHmmss');

  const currCode = 'VND';
  const locale = 'vn';

  const vnpParams = {
    vnp_Version: '2.1.0',
    vnp_Command: 'pay',
    vnp_TmnCode: tmnCode,
    vnp_Locale: locale,
    vnp_CurrCode: currCode,
    vnp_TxnRef: orderId,
    vnp_OrderInfo: orderInfo,
    vnp_OrderType: 'other',
    vnp_Amount: amount * 100, // Nhân với 100 vì VNPay yêu cầu số tiền * 100
    vnp_ReturnUrl: returnUrl,
    vnp_IpAddr: ipAddr,
    vnp_CreateDate: createDate
  };

  // Sắp xếp các tham số theo thứ tự a-z (theo chuẩn VNPay demo)
  const sortedParams = sortObject(vnpParams);

  // Tạo chuỗi ký (theo chuẩn VNPay demo)
  const signData = querystring.stringify(sortedParams, { encode: false });
  const hmac = crypto.createHmac('sha512', secretKey);
  const signed = hmac.update(new Buffer(signData, 'utf-8')).digest('hex');

  // Thêm chữ ký vào tham số gốc
  vnpParams.vnp_SecureHash = signed;

  // Tạo URL thanh toán (theo chuẩn VNPay demo)
  const paymentUrl = vnpUrl + '?' + querystring.stringify(vnpParams, { encode: false });

  return paymentUrl;
};

// Xác thực callback từ VNPay
const verifyReturnUrl = (vnpParams) => {
  try {
    console.log('[VNPay Verify] Starting signature verification');
    console.log('[VNPay Verify] Received params keys:', Object.keys(vnpParams));

    const secretKey = process.env.VNPAY_HASH_SECRET;

    // Lấy secure hash từ params
    const secureHash = vnpParams.vnp_SecureHash;
    console.log('[VNPay Verify] Received hash:', secureHash?.substring(0, 20) + '...');

    if (!secureHash) {
      console.error('[VNPay Verify] No secure hash found in params');
      return false;
    }

    // Tạo bản sao để không modify object gốc
    const paramsForVerify = { ...vnpParams };

    // Xóa secure hash để tạo chuỗi ký mới
    delete paramsForVerify.vnp_SecureHash;
    delete paramsForVerify.vnp_SecureHashType;

    console.log('[VNPay Verify] Params for verification:', Object.keys(paramsForVerify));

    // Sắp xếp các tham số theo thứ tự a-z
    const sortedParams = sortObject(paramsForVerify);

    // Tạo chuỗi ký (theo chuẩn VNPay demo)
    const signData = querystring.stringify(sortedParams, { encode: false });
    console.log('[VNPay Verify] Sign data:', signData);

    const hmac = crypto.createHmac('sha512', secretKey);
    const signed = hmac.update(new Buffer(signData, 'utf-8')).digest('hex');
    console.log('[VNPay Verify] Generated hash:', signed.substring(0, 20) + '...');

    // So sánh chữ ký
    const isValid = secureHash === signed;
    console.log('[VNPay Verify] Signature match:', isValid);

    return isValid;
  } catch (error) {
    console.error('[VNPay Verify] Error during verification:', error);
    return false;
  }
};

// Hàm sắp xếp object theo key (theo chuẩn VNPay demo - FIXED for req.query)
function sortObject(obj) {
  let sorted = {};
  let str = [];
  let key;
  for (key in obj){
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      str.push(encodeURIComponent(key));
    }
  }
  str.sort();
  for (key = 0; key < str.length; key++) {
    sorted[str[key]] = encodeURIComponent(obj[str[key]]).replace(/%20/g, "+");
  }
  return sorted;
}

module.exports = { createPaymentUrl, verifyReturnUrl };
