const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Người dùng là bắt buộc']
    },
    room: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Room',
      required: [true, 'Phòng trọ là bắt buộc']
    },
    amount: {
      type: Number,
      required: [true, 'Số tiền là bắt buộc'],
      min: [0, 'Số tiền không được âm']
    },
    packageType: {
      type: String,
      enum: ['regular', 'special'],
      required: [true, 'Loại gói là bắt buộc']
    },
    vnpayTxnRef: {
      type: String,
      unique: true
    },
    vnpayTxnNo: String,
    vnpayResponseCode: String,
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed'],
      default: 'pending'
    },
    completedAt: {
      type: Date
    }
  },
  {
    timestamps: true
  }
);

// Tạo index cho tìm kiếm giao dịch theo người dùng
transactionSchema.index({ user: 1 });

// Tạo index cho tìm kiếm giao dịch theo phòng trọ
transactionSchema.index({ room: 1 });

// Index cho vnpayTxnRef đã được tạo tự động thông qua unique: true

const Transaction = mongoose.model('Transaction', transactionSchema);

module.exports = Transaction;
