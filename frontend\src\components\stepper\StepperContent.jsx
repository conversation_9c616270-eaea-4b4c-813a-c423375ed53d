import PropTypes from 'prop-types';
import { motion, AnimatePresence } from 'framer-motion';

const StepperContent = ({ children, currentStep }) => {
  // Chỉ hiển thị nội dung của b<PERSON>ớc hiện tại
  const currentChild = Array.isArray(children) ? children[currentStep] : children;

  // Animation variants
  const variants = {
    enter: (direction) => ({
      x: direction > 0 ? 100 : -100,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction) => ({
      x: direction < 0 ? 100 : -100,
      opacity: 0,
    }),
  };

  return (
    <div className="relative overflow-hidden">
      <AnimatePresence initial={false} custom={currentStep}>
        <motion.div
          key={currentStep}
          custom={currentStep}
          variants={variants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{
            x: { type: 'spring', stiffness: 300, damping: 30 },
            opacity: { duration: 0.2 },
          }}
          className="w-full"
        >
          {currentChild}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

StepperContent.propTypes = {
  children: PropTypes.node.isRequired,
  currentStep: PropTypes.number.isRequired,
};

export default StepperContent;
