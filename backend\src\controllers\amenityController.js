const Amenity = require('../models/Amenity');

// @desc    L<PERSON>y danh sách tiện nghi
// @route   GET /api/amenities
// @access  Public
const getAmenities = async (req, res) => {
  try {
    const amenities = await Amenity.find().sort({ name: 1 });

    res.status(200).json({
      success: true,
      data: amenities
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy chi tiết tiện nghi
// @route   GET /api/amenities/:id
// @access  Public
const getAmenityById = async (req, res) => {
  try {
    const amenity = await Amenity.findById(req.params.id);

    if (!amenity) {
      return res.status(404).json({
        success: false,
        message: '<PERSON>hông tìm thấy tiện nghi'
      });
    }

    res.status(200).json({
      success: true,
      data: amenity
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Tạo tiện nghi mới
// @route   POST /api/amenities
// @access  Private (Admin)
const createAmenity = async (req, res) => {
  try {
    const { name, icon } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Tên tiện nghi là bắt buộc'
      });
    }

    // Kiểm tra xem tiện nghi đã tồn tại chưa
    const existingAmenity = await Amenity.findOne({ name: { $regex: new RegExp(`^${name}$`, 'i') } });
    if (existingAmenity) {
      return res.status(400).json({
        success: false,
        message: `Tiện nghi "${name}" đã tồn tại`,
        data: existingAmenity
      });
    }

    // Tạo tiện nghi mới
    const amenity = await Amenity.create({
      name,
      icon: icon || ''
    });

    res.status(201).json({
      success: true,
      data: amenity
    });
  } catch (error) {
    console.error(error);

    // Xử lý lỗi trùng lặp khóa
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Tiện nghi này đã tồn tại',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Cập nhật tiện nghi
// @route   PUT /api/amenities/:id
// @access  Private (Admin)
const updateAmenity = async (req, res) => {
  try {
    const { name, icon } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Tên tiện nghi là bắt buộc'
      });
    }

    // Kiểm tra xem tiện nghi cần cập nhật có tồn tại không
    const currentAmenity = await Amenity.findById(req.params.id);
    if (!currentAmenity) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy tiện nghi'
      });
    }

    // Nếu tên mới khác tên cũ, kiểm tra xem tên mới đã tồn tại chưa
    if (name !== currentAmenity.name) {
      const existingAmenity = await Amenity.findOne({
        name: { $regex: new RegExp(`^${name}$`, 'i') },
        _id: { $ne: req.params.id } // Loại trừ tiện nghi hiện tại
      });

      if (existingAmenity) {
        return res.status(400).json({
          success: false,
          message: `Tiện nghi "${name}" đã tồn tại`,
          data: existingAmenity
        });
      }
    }

    // Tìm và cập nhật tiện nghi
    const amenity = await Amenity.findByIdAndUpdate(
      req.params.id,
      { name, icon },
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      data: amenity
    });
  } catch (error) {
    console.error(error);

    // Xử lý lỗi trùng lặp khóa
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Tiện nghi này đã tồn tại',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Xóa tiện nghi
// @route   DELETE /api/amenities/:id
// @access  Private (Admin)
const deleteAmenity = async (req, res) => {
  try {
    // Kiểm tra xem tiện nghi có tồn tại không
    const amenity = await Amenity.findById(req.params.id);

    if (!amenity) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy tiện nghi'
      });
    }

    // Sử dụng deleteOne thay vì remove (đã bị loại bỏ trong Mongoose mới)
    await Amenity.deleteOne({ _id: req.params.id });

    res.status(200).json({
      success: true,
      message: 'Xóa tiện nghi thành công'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getAmenities,
  getAmenityById,
  createAmenity,
  updateAmenity,
  deleteAmenity
};
