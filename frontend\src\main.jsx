import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { AuthProvider } from './contexts'
import App from './App.jsx'
import './index.css'
import './styles/vnpay-fixes.css' // Import VNPay CSS fixes
import { applyGlobalPatches } from './utils/global-patches'

// Áp dụng các bản vá lỗi toàn cục
applyGlobalPatches();

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <BrowserRouter>
      <AuthProvider>
        <App />
      </AuthProvider>
    </BrowserRouter>
  </React.StrictMode>,
)
