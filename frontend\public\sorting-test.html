<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Sorting Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .step h3 { margin: 0 0 10px 0; color: #333; }
        button { padding: 12px 24px; margin: 10px; font-size: 16px; cursor: pointer; border: none; border-radius: 4px; }
        .btn-success { background: #28a745; color: white; }
        .btn-primary { background: #007bff; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .demo-section { display: flex; gap: 20px; margin-top: 20px; }
        .demo-column { flex: 1; }
        .room-item { padding: 10px; margin: 5px 0; border-radius: 4px; border: 1px solid #ddd; }
        .room-highlighted { background: #fff8e1; border-color: #ffc107; }
        .room-regular { background: #f8f9fa; border-color: #dee2e6; }
        .countdown { font-weight: bold; color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Auto-Sorting Real-time Test</h1>
        <p>Test tính năng sắp xếp lại danh sách khi tin nổi bật hết hạn</p>
        
        <div class="step">
            <h3>🎯 Demo Auto-Sorting</h3>
            <button class="btn-success" onclick="startDemo()">Start Demo - Tạo Multiple Highlights</button>
            <button class="btn-primary" onclick="openHomePage()">Mở HomePage</button>
            <button class="btn-warning" onclick="triggerAllExpiry()">Trigger All Expiry</button>
            <div id="demo-result" class="result"></div>
        </div>

        <div class="step">
            <h3>📊 Live Room Status</h3>
            <button class="btn-primary" onclick="loadRoomStatus()">Load Current Status</button>
            <div id="status-result" class="result"></div>
            <div class="demo-section">
                <div class="demo-column">
                    <h4>🌟 Highlighted Rooms</h4>
                    <div id="highlighted-rooms"></div>
                </div>
                <div class="demo-column">
                    <h4>📝 Regular Rooms</h4>
                    <div id="regular-rooms"></div>
                </div>
            </div>
        </div>

        <div class="step">
            <h3>✅ Expected Behavior</h3>
            <div class="result info">
                <strong>Khi tin nổi bật hết hạn:</strong><br>
                ✅ Tin tự động chuyển từ highlighted → regular<br>
                ✅ Toàn bộ danh sách được sắp xếp lại<br>
                ✅ Tin nổi bật luôn ở trên đầu<br>
                ✅ Tin thường được sắp xếp theo thời gian<br>
                ✅ Không cần refresh trang<br>
                ✅ Smooth animation transitions
            </div>
        </div>
    </div>

    <script>
        let demoInterval = null;
        let statusInterval = null;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
            console.log(`[SortingTest] ${message}`);
        }

        async function startDemo() {
            try {
                log('demo-result', '🚀 Starting auto-sorting demo...', 'info');
                
                // Get multiple rooms
                const roomsResponse = await fetch('http://localhost:5000/api/rooms?limit=5');
                const roomsData = await roomsResponse.json();
                
                if (!roomsData.success || roomsData.data.rooms.length < 3) {
                    throw new Error('Cần ít nhất 3 phòng để demo');
                }
                
                const rooms = roomsData.data.rooms.slice(0, 3);
                log('demo-result', `📝 Using ${rooms.length} rooms for demo`, 'info');
                
                // Create highlights with different expiry times
                const highlights = [];
                for (let i = 0; i < rooms.length; i++) {
                    const room = rooms[i];
                    const minutes = 0.5 + (i * 0.5); // 30s, 60s, 90s
                    
                    const response = await fetch('http://localhost:5000/api/test/create-test-highlight', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ roomId: room._id, minutes: minutes })
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        highlights.push({
                            roomId: room._id,
                            title: room.title,
                            expiryMinutes: minutes
                        });
                    }
                }
                
                log('demo-result', `✅ Created ${highlights.length} test highlights!<br>⏰ They will expire at different times<br>🔗 Open HomePage to see auto-sorting`, 'success');
                
                // Auto-open HomePage
                window.open('http://localhost:3000', '_blank');
                
                // Start live status updates
                startStatusUpdates();
                
            } catch (error) {
                log('demo-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        function openHomePage() {
            window.open('http://localhost:3000', '_blank');
            log('demo-result', '✅ HomePage opened in new tab', 'success');
        }

        async function triggerAllExpiry() {
            try {
                log('demo-result', '🔄 Triggering all expiry...', 'warning');
                
                const response = await fetch('http://localhost:5000/api/test/run-highlight-expiry-job', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log('demo-result', `✅ All highlights expired!<br>📊 Processed: ${data.data.processed} rooms<br>🔍 Check HomePage for sorting update`, 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log('demo-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function loadRoomStatus() {
            try {
                log('status-result', '🔄 Loading room status...', 'info');
                
                const response = await fetch('http://localhost:5000/api/rooms?limit=20');
                const data = await response.json();
                
                if (data.success) {
                    const rooms = data.data.rooms;
                    const highlighted = rooms.filter(room => room.isHighlighted);
                    const regular = rooms.filter(room => !room.isHighlighted);
                    
                    log('status-result', `✅ Loaded ${rooms.length} rooms<br>🌟 ${highlighted.length} highlighted • 📝 ${regular.length} regular`, 'success');
                    
                    displayRooms(highlighted, regular);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log('status-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        function displayRooms(highlighted, regular) {
            const highlightedDiv = document.getElementById('highlighted-rooms');
            const regularDiv = document.getElementById('regular-rooms');
            
            // Display highlighted rooms
            highlightedDiv.innerHTML = '';
            highlighted.forEach((room, index) => {
                const div = document.createElement('div');
                div.className = 'room-item room-highlighted';
                
                let expiryInfo = '';
                if (room.highlightExpiry) {
                    const expiry = new Date(room.highlightExpiry);
                    const now = new Date();
                    const timeLeft = expiry - now;
                    
                    if (timeLeft > 0) {
                        const minutes = Math.floor(timeLeft / (1000 * 60));
                        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                        expiryInfo = `<div class="countdown">⏰ ${minutes}:${seconds.toString().padStart(2, '0')}</div>`;
                    } else {
                        expiryInfo = '<div class="countdown">⏰ Expired</div>';
                    }
                }
                
                div.innerHTML = `
                    <strong>#${index + 1} ${room.title}</strong><br>
                    <small>🌟 Highlighted • ${room.price?.toLocaleString() || 0} VNĐ/tháng</small>
                    ${expiryInfo}
                `;
                
                highlightedDiv.appendChild(div);
            });
            
            // Display regular rooms
            regularDiv.innerHTML = '';
            regular.slice(0, 10).forEach((room, index) => {
                const div = document.createElement('div');
                div.className = 'room-item room-regular';
                
                div.innerHTML = `
                    <strong>#${index + 1} ${room.title}</strong><br>
                    <small>📝 Regular • ${room.price?.toLocaleString() || 0} VNĐ/tháng</small>
                `;
                
                regularDiv.appendChild(div);
            });
        }

        function startStatusUpdates() {
            if (statusInterval) {
                clearInterval(statusInterval);
            }
            
            statusInterval = setInterval(() => {
                loadRoomStatus();
            }, 2000); // Update every 2 seconds
        }

        function stopStatusUpdates() {
            if (statusInterval) {
                clearInterval(statusInterval);
                statusInterval = null;
            }
        }

        // Auto-load status on page load
        window.onload = () => {
            console.log('[SortingTest] 🔄 Auto-Sorting Test Ready');
            loadRoomStatus();
        };

        // Cleanup on page unload
        window.onbeforeunload = () => {
            stopStatusUpdates();
        };
    </script>
</body>
</html>
