import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaPlus, FaFilter, FaSearch } from 'react-icons/fa';
import { useAuthContext } from '../contexts';
import { useRooms, useSocket } from '../hooks';
import RoomList from '../components/rooms/RoomList';
import RoomStats from '../components/rooms/RoomStats';

const MyRoomsPage = () => {
  const { isAuthenticated, isLoading: authLoading } = useAuthContext();
  const { rooms, pagination, isLoading: roomsLoading, fetchMyRooms, updateRoomStatus } = useRooms();
  const { addEventListener } = useSocket();
  const navigate = useNavigate();

  const [filters, setFilters] = useState({
    status: '',
    search: '',
    page: 1,
    limit: 10000
  });

  const [stats, setStats] = useState({
    total: 0,
    available: 0,
    rented: 0,
    hidden: 0
  });

  // <PERSON>yển hướng nếu chưa đăng nhập
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      toast.error('Vui lòng đăng nhập để xem trang này');
      navigate('/login');
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Lấy danh sách phòng trọ của người dùng
  useEffect(() => {
    if (isAuthenticated) {
      const loadRooms = async () => {
        try {
          await fetchMyRooms(filters);
        } catch (err) {
          toast.error('Không thể tải danh sách phòng trọ');
        }
      };

      loadRooms();
    }
  }, [isAuthenticated, fetchMyRooms, filters]);

  // Tính toán thống kê
  useEffect(() => {
    if (rooms.length > 0) {
      const newStats = {
        total: rooms.length,
        available: rooms.filter(room => room.status === 'available').length,
        rented: rooms.filter(room => room.status === 'rented').length,
        hidden: rooms.filter(room => room.status === 'hidden').length
      };

      setStats(newStats);
    }
  }, [rooms]);

  // Lắng nghe WebSocket events cho real-time updates
  useEffect(() => {
    const handleHighlightExpired = (data) => {
      console.log('[MyRoomsPage] Highlight expired for room:', data.roomId);

      // Reload danh sách phòng để cập nhật trạng thái
      fetchMyRooms(filters).catch(err => {
        console.error('Error reloading rooms after highlight expired:', err);
      });

      toast.info(`Tin đăng "${data.title}" đã hết hạn gói nổi bật`, {
        position: 'top-right',
        autoClose: 5000
      });
    };

    const handleRoomHighlightUpdated = (data) => {
      console.log('[MyRoomsPage] Room highlight updated:', data);

      // Reload danh sách phòng để cập nhật trạng thái
      fetchMyRooms(filters).catch(err => {
        console.error('Error reloading rooms after highlight update:', err);
      });
    };

    const unsubscribeExpired = addEventListener('highlight_expired', handleHighlightExpired);
    const unsubscribeUpdated = addEventListener('room_highlight_updated', handleRoomHighlightUpdated);

    return () => {
      unsubscribeExpired();
      unsubscribeUpdated();
    };
  }, [addEventListener, fetchMyRooms, filters]);

  // Xử lý thay đổi bộ lọc
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value,
      page: 1 // Reset về trang 1 khi thay đổi bộ lọc
    });
  };

  // Xử lý tìm kiếm
  const handleSearch = (e) => {
    e.preventDefault();
    setFilters({
      ...filters,
      page: 1 // Reset về trang 1 khi tìm kiếm
    });
  };

  // Xử lý thay đổi trang
  const handlePageChange = (newPage) => {
    if (newPage > 0 && newPage <= pagination.totalPages) {
      setFilters({
        ...filters,
        page: newPage
      });
    }
  };

  // Xử lý cập nhật trạng thái phòng
  const handleStatusUpdate = async (roomId, newStatus) => {
    try {
      // Hiển thị toast loading
      const loadingToast = toast.loading('Đang cập nhật trạng thái...');

      // Gọi API cập nhật trạng thái
      const response = await updateRoomStatus(roomId, newStatus);

      // Cập nhật lại danh sách phòng
      await fetchMyRooms(filters);

      // Cập nhật toast thành công
      toast.update(loadingToast, {
        render: 'Cập nhật trạng thái phòng thành công',
        type: 'success',
        isLoading: false,
        autoClose: 3000
      });
    } catch (err) {
      console.error('Lỗi khi cập nhật trạng thái phòng:', err);
      toast.error(err.message || 'Không thể cập nhật trạng thái phòng');
    }
  };

  // Xử lý sau khi xóa phòng thành công
  const handleRoomDeleted = async (deletedRoomId) => {
    try {
      console.log('Phòng đã được xóa, đang refresh danh sách...', deletedRoomId);

      // Refresh danh sách phòng để cập nhật UI
      await fetchMyRooms(filters);

      console.log('Đã refresh danh sách phòng sau khi xóa');
    } catch (err) {
      console.error('Lỗi khi refresh danh sách sau khi xóa:', err);
      toast.error('Đã xóa phòng nhưng không thể cập nhật danh sách. Vui lòng tải lại trang.');
    }
  };

  // Hiển thị loading khi đang kiểm tra trạng thái đăng nhập
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  // Hiển thị nội dung trang khi đã đăng nhập
  return (
    <div className="bg-gray-50 py-8">
      <div className="container-custom">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <h1 className="text-3xl font-bold">Quản lý phòng trọ</h1>

          <Link
            to="/rooms/create"
            className="mt-4 md:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <FaPlus className="mr-2" />
            Đăng tin mới
          </Link>
        </div>

        {/* Thống kê */}
        <RoomStats stats={stats} />

        {/* Bộ lọc và tìm kiếm */}
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <form onSubmit={handleSearch} className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Tìm kiếm phòng trọ..."
                  className="form-input pr-10 w-full"
                  name="search"
                  value={filters.search}
                  onChange={handleFilterChange}
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-dark-400 hover:text-primary"
                >
                  <FaSearch />
                </button>
              </div>
            </form>

            <div className="w-full md:w-48">
              <select
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="form-select w-full"
              >
                <option value="">Tất cả trạng thái</option>
                <option value="available">Còn trống</option>
                <option value="rented">Đã cho thuê</option>
                <option value="hidden">Đã ẩn</option>
              </select>
            </div>

            {/* <div className="w-full md:w-48">
              <select
                name="limit"
                value={filters.limit}
                onChange={handleFilterChange}
                className="form-select w-full"
              >
                <option value="5">5 phòng</option>
                <option value="10">10 phòng</option>
                <option value="20">20 phòng</option>
                <option value="50">50 phòng</option>
              </select>
            </div> */}
          </div>
        </div>

        {/* Danh sách phòng trọ */}
        <RoomList
          rooms={rooms}
          pagination={pagination}
          isLoading={roomsLoading}
          onPageChange={handlePageChange}
          onStatusUpdate={handleStatusUpdate}
          onRoomDeleted={handleRoomDeleted}
        />
      </div>
    </div>
  );
};

export default MyRoomsPage;
