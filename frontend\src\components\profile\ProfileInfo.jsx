import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { <PERSON>a<PERSON>ser, FaEnvelope, FaPhone, FaMapMarkerAlt, FaSave } from 'react-icons/fa';
import { authService } from '../../services';
import { useAuthContext } from '../../contexts';

const ProfileInfo = ({ user }) => {
  const { checkAuthStatus } = useAuthContext();
  const [formData, setFormData] = useState({
    fullName: '',
    phone: '',
    address: {
      street: '',
      ward: '',
      district: '',
      city: ''
    }
  });
  const [isLoading, setIsLoading] = useState(false);

  // Cập nhật formData khi user thay đổi
  useEffect(() => {
    if (user) {
      setFormData({
        fullName: user.fullName || '',
        phone: user.phone || '',
        address: {
          street: user.address?.street || '',
          ward: user.address?.ward || '',
          district: user.address?.district || '',
          city: user.address?.city || ''
        }
      });
    }
  }, [user]);

  // <PERSON>ử lý thay đổi input
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent],
          [child]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Xử lý submit form
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await authService.updateProfile(formData);
      
      if (response.success) {
        toast.success('Cập nhật thông tin thành công');
        // Cập nhật thông tin người dùng trong context
        await checkAuthStatus();
      } else {
        toast.error(response.message || 'Cập nhật thông tin thất bại');
      }
    } catch (error) {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật thông tin');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6 pb-2 border-b">Thông tin cá nhân</h2>
      
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Email - Không thể thay đổi */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <div className="mt-1 relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaEnvelope className="text-gray-400" />
              </div>
              <input
                type="email"
                value={user?.email || ''}
                className="bg-gray-100 focus:ring-primary focus:border-primary block w-full pl-10 sm:text-sm border-gray-300 rounded-md py-3 cursor-not-allowed"
                disabled
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">Email không thể thay đổi</p>
          </div>

          {/* Họ tên */}
          <div>
            <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
              Họ tên
            </label>
            <div className="mt-1 relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaUser className="text-gray-400" />
              </div>
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                className="focus:ring-primary focus:border-primary block w-full pl-10 sm:text-sm border-gray-300 rounded-md py-3"
                placeholder="Nhập họ tên đầy đủ"
                required
              />
            </div>
          </div>

          {/* Số điện thoại */}
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
              Số điện thoại
            </label>
            <div className="mt-1 relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaPhone className="text-gray-400" />
              </div>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className="focus:ring-primary focus:border-primary block w-full pl-10 sm:text-sm border-gray-300 rounded-md py-3"
                placeholder="Nhập số điện thoại"
              />
            </div>
          </div>

          {/* Địa chỉ */}
          <div>
            <h3 className="text-lg font-medium mb-3 flex items-center">
              <FaMapMarkerAlt className="mr-2 text-primary" />
              Địa chỉ
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="address.street" className="block text-sm font-medium text-gray-700 mb-1">
                  Đường/Số nhà
                </label>
                <input
                  type="text"
                  id="address.street"
                  name="address.street"
                  value={formData.address.street}
                  onChange={handleChange}
                  className="focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md py-3"
                  placeholder="Nhập đường/số nhà"
                />
              </div>
              
              <div>
                <label htmlFor="address.ward" className="block text-sm font-medium text-gray-700 mb-1">
                  Phường/Xã
                </label>
                <input
                  type="text"
                  id="address.ward"
                  name="address.ward"
                  value={formData.address.ward}
                  onChange={handleChange}
                  className="focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md py-3"
                  placeholder="Nhập phường/xã"
                />
              </div>
              
              <div>
                <label htmlFor="address.district" className="block text-sm font-medium text-gray-700 mb-1">
                  Quận/Huyện
                </label>
                <input
                  type="text"
                  id="address.district"
                  name="address.district"
                  value={formData.address.district}
                  onChange={handleChange}
                  className="focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md py-3"
                  placeholder="Nhập quận/huyện"
                />
              </div>
              
              <div>
                <label htmlFor="address.city" className="block text-sm font-medium text-gray-700 mb-1">
                  Tỉnh/Thành phố
                </label>
                <input
                  type="text"
                  id="address.city"
                  name="address.city"
                  value={formData.address.city}
                  onChange={handleChange}
                  className="focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md py-3"
                  placeholder="Nhập tỉnh/thành phố"
                />
              </div>
            </div>
          </div>

          {/* Nút lưu */}
          <div className="pt-4">
            <button
              type="submit"
              className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span>
                  Đang lưu...
                </>
              ) : (
                <>
                  <FaSave className="mr-2" />
                  Lưu thay đổi
                </>
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ProfileInfo;
