<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Socket</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 10px 20px; margin: 10px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; width: 300px; }
        .logs { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-top: 20px; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Frontend Socket</h1>
        <p>Test socket connection và events từ frontend</p>
        
        <div class="status info">
            <strong>Instructions:</strong><br>
            1. Mở Developer Console để xem logs<br>
            2. Mở HomePage trong tab khác<br>
            3. Test emit events và xem HomePage có nhận được không
        </div>

        <div>
            <button class="btn-success" onclick="testSocketConnection()">Test Socket Connection</button>
            <button class="btn-primary" onclick="testEmitEvent()">Test Emit Event</button>
            <button class="btn-danger" onclick="simulateHighlightExpired()">Simulate Highlight Expired</button>
        </div>

        <div>
            <input type="text" id="roomIdInput" placeholder="Room ID" value="687cb5b5ff0dac93286b6116">
            <button class="btn-primary" onclick="emitRoomHighlightUpdated()">Emit Room Updated</button>
        </div>
        
        <div id="logs" class="logs">
            <strong>Frontend Socket Test Logs:</strong><br>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            logsDiv.appendChild(div);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(`[TestPage] ${message}`);
        }

        function testSocketConnection() {
            log('🔍 Testing socket connection...', 'info');
            
            // Access React app's socket service
            if (window.socketService) {
                log('✅ socketService found in window', 'success');
                log(`Connection status: ${window.socketService.isConnected()}`, 'info');
                
                if (window.socketService.socket) {
                    log(`Socket ID: ${window.socketService.socket.id}`, 'info');
                    log(`Socket connected: ${window.socketService.socket.connected}`, 'info');
                } else {
                    log('❌ No socket instance found', 'error');
                }
            } else {
                log('❌ socketService not found in window', 'error');
                log('💡 Make sure to expose socketService to window in React app', 'warning');
            }
        }

        function testEmitEvent() {
            log('🧪 Testing emit event...', 'info');
            
            if (window.socketService && window.socketService.isConnected()) {
                // Test emit a custom event
                window.socketService.socket.emit('test_frontend_event', {
                    message: 'Hello from test page',
                    timestamp: new Date().toISOString()
                });
                log('✅ Test event emitted', 'success');
            } else {
                log('❌ Socket not connected', 'error');
            }
        }

        function simulateHighlightExpired() {
            const roomId = document.getElementById('roomIdInput').value;
            if (!roomId) {
                log('❌ Please enter Room ID', 'error');
                return;
            }

            log('🎭 Simulating highlight expired event...', 'info');
            
            if (window.socketService) {
                // Simulate the event that would come from server
                const data = {
                    roomId: roomId,
                    title: 'Test Room Title',
                    userId: 'test-user-id',
                    timestamp: new Date().toISOString()
                };

                // Trigger the event handler directly
                window.socketService.emitToListeners('highlight_expired', data);
                log('✅ highlight_expired event simulated', 'success');
                log(`Data: ${JSON.stringify(data)}`, 'info');
            } else {
                log('❌ socketService not available', 'error');
            }
        }

        function emitRoomHighlightUpdated() {
            const roomId = document.getElementById('roomIdInput').value;
            if (!roomId) {
                log('❌ Please enter Room ID', 'error');
                return;
            }

            log('🔄 Simulating room_highlight_updated event...', 'info');
            
            if (window.socketService) {
                // Simulate the event that would come from server
                const data = {
                    roomId: roomId,
                    isHighlighted: false,
                    highlightExpiry: null,
                    timestamp: new Date().toISOString()
                };

                // Trigger the event handler directly
                window.socketService.emitToListeners('room_highlight_updated', data);
                log('✅ room_highlight_updated event simulated', 'success');
                log(`Data: ${JSON.stringify(data)}`, 'info');
                log('🔗 Check HomePage to see if it received the event', 'warning');
            } else {
                log('❌ socketService not available', 'error');
            }
        }

        // Auto-test on page load
        window.onload = () => {
            setTimeout(() => {
                testSocketConnection();
            }, 2000);
        };

        // Listen for messages from React app
        window.addEventListener('message', (event) => {
            if (event.data.type === 'SOCKET_EVENT') {
                log(`📨 Received socket event: ${event.data.event}`, 'info');
                log(`Data: ${JSON.stringify(event.data.data)}`, 'info');
            }
        });
    </script>
</body>
</html>
