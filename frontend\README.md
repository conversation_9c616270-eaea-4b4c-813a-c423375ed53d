# Dự Án Frontend - Tì<PERSON> Phòng Trọ

Dự án Frontend cho hệ thống tìm phòng trọ, đư<PERSON><PERSON> xây dựng bằng React, Vite và TailwindCSS.

## Cấu Trúc <PERSON>h<PERSON>

```
frontend/
├── public/             # Tài nguyên tĩnh
├── src/                # Mã nguồn
│   ├── assets/         # Hình ảnh, font chữ, etc.
│   ├── components/     # Các component tái sử dụng
│   ├── hooks/          # Custom hooks
│   ├── pages/          # Các trang
│   ├── utils/          # Các hàm tiện ích
│   ├── App.jsx         # Component chính
│   ├── main.jsx        # Điểm khởi đầu
│   └── index.css       # CSS toàn cục và cấu hình TailwindCSS
├── index.html          # HTML gốc
├── package.json        # Cấu hình npm
├── vite.config.js      # Cấu hình Vite
├── tailwind.config.js  # Cấu hình TailwindCSS
└── postcss.config.js   # <PERSON><PERSON><PERSON> hình PostCSS
```

## Cài Đặt

### Yêu <PERSON>

- Node.js (phiên bản 14.x trở lên)
- npm hoặc yarn

### <PERSON><PERSON><PERSON> Cài Đặt

1. Clone dự án về máy:

```bash
git clone <repository-url>
cd DoAn-DuyNoi/frontend
```

2. Cài đặt các dependencies:

```bash
npm install
# hoặc
yarn install
```

## Chạy Dự Án

### Môi Trường Phát Triển

Để chạy dự án ở môi trường phát triển:

```bash
npm run dev
# hoặc
yarn dev
```

Ứng dụng sẽ chạy tại địa chỉ [http://localhost:3000](http://localhost:3000).

### Build Dự Án

Để build dự án cho môi trường production:

```bash
npm run build
# hoặc
yarn build
```

Kết quả build sẽ được lưu trong thư mục `dist/`.

### Preview Bản Build

Để xem trước bản build:

```bash
npm run preview
# hoặc
yarn preview
```

## Sử Dụng TailwindCSS

Dự án này sử dụng TailwindCSS làm framework CSS chính. Dưới đây là một số ví dụ về cách sử dụng:

### Các Lớp Tiện Ích Cơ Bản

```jsx
// Ví dụ về sử dụng các lớp tiện ích của TailwindCSS
<div className="flex items-center justify-between p-4 bg-white shadow-md rounded-lg">
  <h2 className="text-xl font-bold text-gray-800">Tiêu đề</h2>
  <button className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
    Nút bấm
  </button>
</div>
```

### Các Component Tùy Chỉnh

Dự án đã định nghĩa sẵn một số component và lớp tiện ích tùy chỉnh trong file `src/index.css`:

```css
/* Ví dụ về các lớp tiện ích tùy chỉnh */
.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-primary {
  @apply bg-primary text-white hover:bg-primary-dark;
}

.card {
  @apply bg-white rounded-lg shadow-md p-6;
}
```

Sử dụng các lớp này trong component:

```jsx
<button className="btn btn-primary">Đăng Nhập</button>
<div className="card">Nội dung thẻ</div>
```

## Các Component Có Sẵn

Dự án đã bao gồm một số component cơ bản:

- `Button`: Component nút bấm với nhiều biến thể
- `Card`: Component thẻ hiển thị thông tin phòng trọ
- `Header`: Component thanh điều hướng
- `Footer`: Component chân trang
- `SearchBar`: Component thanh tìm kiếm

## Tùy Chỉnh Theme

Bạn có thể tùy chỉnh theme của TailwindCSS trong file `tailwind.config.js`:

```js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          light: '#4da6ff',
          DEFAULT: '#0066cc',
          dark: '#004d99',
        },
        // Thêm màu sắc tùy chỉnh khác
      },
      // Tùy chỉnh font, spacing, etc.
    },
  },
  // ...
}
```

## Lưu Ý Quan Trọng

- Đảm bảo luôn import `index.css` trong file `main.jsx` để áp dụng các styles của TailwindCSS.
- Khi thêm các tệp mới, đảm bảo cập nhật đường dẫn trong cấu hình `content` của TailwindCSS nếu cần.
- Sử dụng các component có sẵn để đảm bảo tính nhất quán trong giao diện.

## Tài Liệu Tham Khảo

- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [Vite Documentation](https://vitejs.dev/guide/)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
- [React Router Documentation](https://reactrouter.com/docs/en/v6)
