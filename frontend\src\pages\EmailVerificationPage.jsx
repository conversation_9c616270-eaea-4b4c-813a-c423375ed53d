import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaCheckCircle, FaTimesCircle, FaSpinner, FaEnvelope } from 'react-icons/fa';
import { authService } from '../services';

const EmailVerificationPage = () => {
  const { token } = useParams();
  const navigate = useNavigate();
  const [verificationStatus, setVerificationStatus] = useState('verifying'); // 'verifying', 'success', 'error'
  const [message, setMessage] = useState('');
  const [isResending, setIsResending] = useState(false);

  useEffect(() => {
    const verifyEmail = async () => {
      if (!token) {
        setVerificationStatus('error');
        setMessage('Token xác nhận không hợp lệ.');
        return;
      }

      try {
        const response = await authService.verifyEmail(token);
        if (response.success) {
          setVerificationStatus('success');
          setMessage(response.message || 'Email đã được xác nhận thành công!');
          
          // Hiển thị thông báo thành công
          toast.success('Email đã được xác nhận thành công! Bạn có thể đăng nhập ngay bây giờ.');
          
          // Chuyển hướng đến trang đăng nhập sau 3 giây
          setTimeout(() => {
            navigate('/login');
          }, 3000);
        }
      } catch (error) {
        setVerificationStatus('error');
        setMessage(error.message || 'Xác nhận email thất bại. Vui lòng thử lại.');
        toast.error(error.message || 'Xác nhận email thất bại.');
      }
    };

    verifyEmail();
  }, [token, navigate]);

  const handleResendEmail = async () => {
    const email = prompt('Vui lòng nhập địa chỉ email của bạn để gửi lại email xác nhận:');
    
    if (!email) {
      return;
    }

    if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email)) {
      toast.error('Địa chỉ email không hợp lệ.');
      return;
    }

    setIsResending(true);
    try {
      const response = await authService.resendVerificationEmail(email);
      if (response.success) {
        toast.success('Email xác nhận đã được gửi lại thành công!');
      }
    } catch (error) {
      toast.error(error.message || 'Không thể gửi lại email xác nhận. Vui lòng thử lại sau.');
    } finally {
      setIsResending(false);
    }
  };

  const renderContent = () => {
    switch (verificationStatus) {
      case 'verifying':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-6">
              <FaSpinner className="h-8 w-8 text-blue-600 animate-spin" />
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Đang xác nhận email...
            </h2>
            <p className="mt-2 text-base text-gray-600">
              Vui lòng chờ trong giây lát
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
              <FaCheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Xác nhận thành công!
            </h2>
            <p className="mt-2 text-base text-gray-600">
              {message}
            </p>
            <div className="mt-6">
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <FaCheckCircle className="h-5 w-5 text-green-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">
                      Tài khoản đã được kích hoạt
                    </h3>
                    <div className="mt-2 text-sm text-green-700">
                      <p>Bạn sẽ được chuyển hướng đến trang đăng nhập trong vài giây...</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-6">
              <Link
                to="/login"
                className="w-full btn btn-primary"
              >
                Đăng nhập ngay
              </Link>
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
              <FaTimesCircle className="h-8 w-8 text-red-600" />
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Xác nhận thất bại
            </h2>
            <p className="mt-2 text-base text-gray-600">
              {message}
            </p>
            <div className="mt-6">
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <FaTimesCircle className="h-5 w-5 text-red-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      Có thể do các nguyên nhân sau:
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <ul className="list-disc list-inside space-y-1">
                        <li>Liên kết xác nhận đã hết hạn (sau 24 giờ)</li>
                        <li>Liên kết xác nhận không hợp lệ</li>
                        <li>Email đã được xác nhận trước đó</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-6 space-y-4">
              <button
                onClick={handleResendEmail}
                disabled={isResending}
                className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isResending ? (
                  <>
                    <FaSpinner className="animate-spin h-4 w-4 mr-2" />
                    Đang gửi...
                  </>
                ) : (
                  <>
                    <FaEnvelope className="h-4 w-4 mr-2" />
                    Gửi lại email xác nhận
                  </>
                )}
              </button>
              <div className="text-center">
                <Link
                  to="/register"
                  className="font-medium text-primary hover:text-primary-dark"
                >
                  Đăng ký tài khoản mới
                </Link>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white py-8 px-6 shadow rounded-lg">
          {renderContent()}
          
          <div className="mt-8 text-center">
            <Link
              to="/"
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              ← Quay về trang chủ
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationPage;
