import apiClient from './apiClient';

// Service xử lý các API liên quan đến xác thực
const authService = {
  // Đăng ký tài khoản mới
  register: async (userData) => {
    try {
      const response = await apiClient.post('/auth/register', userData);
      // Không tự động đăng nhập sau khi đăng ký
      // User cần xác nhận email trước khi có thể đăng nhập
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Đăng nhập
  login: async (credentials) => {
    try {
      const response = await apiClient.post('/auth/login', credentials);
      if (response.data.success) {
        // Lưu token và thông tin người dùng vào localStorage
        localStorage.setItem('token', response.data.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.data.user));

        // Dispatch custom event để notify components
        window.dispatchEvent(new CustomEvent('authChange', {
          detail: { type: 'login', user: response.data.data.user }
        }));
      }
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Đăng xuất
  logout: async () => {
    try {
      const response = await apiClient.post('/auth/logout');
      // Xóa token và thông tin người dùng khỏi localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Dispatch custom event để notify components
      window.dispatchEvent(new CustomEvent('authChange', {
        detail: { type: 'logout' }
      }));

      return response.data;
    } catch (error) {
      // Xóa token và thông tin người dùng khỏi localStorage ngay cả khi API gặp lỗi
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Dispatch custom event để notify components
      window.dispatchEvent(new CustomEvent('authChange', {
        detail: { type: 'logout' }
      }));

      throw error.response ? error.response.data : error;
    }
  },

  // Lấy thông tin người dùng hiện tại
  getCurrentUser: async () => {
    try {
      const response = await apiClient.get('/auth/me');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Cập nhật thông tin người dùng
  updateProfile: async (userData) => {
    try {
      const response = await apiClient.put('/auth/me', userData);
      if (response.data.success) {
        // Cập nhật thông tin người dùng trong localStorage
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
        const updatedUser = { ...currentUser, ...response.data.data };
        localStorage.setItem('user', JSON.stringify(updatedUser));
      }
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Đổi mật khẩu
  changePassword: async (passwordData) => {
    try {
      const response = await apiClient.put('/auth/change-password', passwordData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Xác nhận email
  verifyEmail: async (token) => {
    try {
      const response = await apiClient.get(`/auth/verify-email/${token}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Gửi lại email xác nhận
  resendVerificationEmail: async (email) => {
    try {
      const response = await apiClient.post('/auth/resend-verification', { email });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Kiểm tra xem người dùng đã đăng nhập hay chưa
  isAuthenticated: () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');

    // Kiểm tra cả token và thông tin người dùng
    return !!token && !!user;
  },

  // Lấy thông tin người dùng từ localStorage
  getUser: () => {
    try {
      const userStr = localStorage.getItem('user');
      if (!userStr) return null;

      // Parse thông tin người dùng
      const user = JSON.parse(userStr);

      // Kiểm tra xem có đủ thông tin cần thiết không
      if (!user || !user._id) {
        console.warn('Thông tin người dùng không hợp lệ, đang xóa...');
        localStorage.removeItem('user');
        return null;
      }

      return user;
    } catch (error) {
      console.error('Lỗi khi lấy thông tin người dùng:', error);
      // Nếu có lỗi khi parse JSON, xóa dữ liệu không hợp lệ
      localStorage.removeItem('user');
      return null;
    }
  },

  // Lấy token từ localStorage
  getToken: () => {
    return localStorage.getItem('token');
  },

  // Quên mật khẩu - Gửi email reset
  forgotPassword: async (email) => {
    try {
      const response = await apiClient.post('/auth/forgot-password', { email });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Verify reset token
  verifyResetToken: async (token) => {
    try {
      const response = await apiClient.get(`/auth/verify-reset-token/${token}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Reset mật khẩu với token
  resetPassword: async (token, password) => {
    try {
      const response = await apiClient.post('/auth/reset-password', { token, password });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default authService;
