import { useEffect, useState } from 'react';
import { disableToastErrors, enableToastErrors } from '../services/apiClient';

const DebugToast = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [logs, setLogs] = useState([]);
  const [initCountdown, setInitCountdown] = useState(10);

  useEffect(() => {
    // Show debug panel in development
    if (import.meta.env.DEV) {
      setIsVisible(true);
    }

    // Countdown timer for initialization period
    const countdownInterval = setInterval(() => {
      setInitCountdown(prev => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Override console methods to capture logs
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;

    const addLog = (type, message) => {
      const timestamp = new Date().toLocaleTimeString();
      setLogs(prev => [...prev.slice(-20), { type, message, timestamp }]);
    };

    console.log = (...args) => {
      originalLog(...args);
      if (args[0]?.includes?.('[ApiClient]')) {
        addLog('log', args.join(' '));
      }
    };

    console.warn = (...args) => {
      originalWarn(...args);
      if (args[0]?.includes?.('[ApiClient]')) {
        addLog('warn', args.join(' '));
      }
    };

    console.error = (...args) => {
      originalError(...args);
      if (args[0]?.includes?.('[ApiClient]')) {
        addLog('error', args.join(' '));
      }
    };

    return () => {
      console.log = originalLog;
      console.warn = originalWarn;
      console.error = originalError;
      clearInterval(countdownInterval);
    };
  }, []);

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-90 text-white p-4 rounded-lg max-w-md max-h-64 overflow-y-auto z-50">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-bold">
          API Debug {initCountdown > 0 && (
            <span className="text-yellow-400">({initCountdown}s)</span>
          )}
        </h3>
        <div className="flex gap-2">
          <button
            onClick={disableToastErrors}
            className="text-xs bg-red-600 px-2 py-1 rounded"
          >
            Disable Toast
          </button>
          <button
            onClick={enableToastErrors}
            className="text-xs bg-green-600 px-2 py-1 rounded"
          >
            Enable Toast
          </button>
          <button
            onClick={() => setIsVisible(false)}
            className="text-xs bg-gray-600 px-2 py-1 rounded"
          >
            ×
          </button>
        </div>
      </div>
      
      <div className="text-xs space-y-1">
        {logs.map((log, index) => (
          <div
            key={index}
            className={`${
              log.type === 'error' ? 'text-red-300' :
              log.type === 'warn' ? 'text-yellow-300' :
              'text-green-300'
            }`}
          >
            <span className="text-gray-400">{log.timestamp}</span> {log.message}
          </div>
        ))}
        {logs.length === 0 && (
          <div className="text-gray-400">No API logs yet...</div>
        )}
      </div>
    </div>
  );
};

export default DebugToast;
