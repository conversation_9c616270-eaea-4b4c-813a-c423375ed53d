import { useState, useEffect } from 'react';
import { FaUsers, FaHome, FaMoneyBillWave, FaChartLine, FaExclamationTriangle } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { formatCurrency } from '../../utils/format';
import { useAuthContext } from '../../contexts';

const Dashboard = () => {
  const { token } = useAuthContext();
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/statistics`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || '<PERSON>hông thể lấy dữ liệu thống kê');
        }

        setStatistics(data.data);
      } catch (error) {
        console.error('Lỗi khi lấy thống kê:', error);
        setError(error.message);
        // Không hiển thị toast error cho admin dashboard
        console.log('🔇 Admin dashboard error - không hiển thị toast');
      } finally {
        setLoading(false);
      }
    };

    fetchStatistics();
  }, [token]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <FaExclamationTriangle className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Đã xảy ra lỗi khi tải dữ liệu
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Dữ liệu thống kê mẫu nếu API chưa trả về
  const stats = statistics || {
    users: { total: 0, new: 0 },
    rooms: { total: 0, rented: 0, available: 0, highlighted: 0, new: 0 },
    transactions: { completed: 0, totalRevenue: 0, recentRevenue: 0 }
  };

  // Các thẻ thống kê
  const statCards = [
    {
      title: 'Tổng chủ trọ',
      value: stats.users.total,
      icon: <FaUsers className="h-8 w-8 text-blue-500" />,
      change: stats.users.new,
      changeText: 'mới trong 7 ngày',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Tổng tin đăng',
      value: stats.rooms.total,
      icon: <FaHome className="h-8 w-8 text-green-500" />,
      change: stats.rooms.new,
      changeText: 'mới trong 7 ngày',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      title: 'Tổng doanh thu',
      value: formatCurrency(stats.transactions.totalRevenue),
      icon: <FaMoneyBillWave className="h-8 w-8 text-yellow-500" />,
      change: formatCurrency(stats.transactions.recentRevenue),
      changeText: 'trong 7 ngày qua',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200'
    },
    {
      title: 'Giao dịch thành công',
      value: stats.transactions.completed,
      icon: <FaChartLine className="h-8 w-8 text-purple-500" />,
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    }
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Tổng quan hệ thống</h1>
      
      {/* Thống kê chính */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((card, index) => (
          <div 
            key={index} 
            className={`${card.bgColor} ${card.borderColor} border overflow-hidden rounded-lg shadow`}
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  {card.icon}
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {card.title}
                    </dt>
                    <dd>
                      <div className="text-lg font-medium text-gray-900">
                        {card.value}
                      </div>
                    </dd>
                    {card.change && (
                      <dd className="text-xs text-gray-500 mt-1">
                        {card.change} {card.changeText}
                      </dd>
                    )}
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Thống kê phòng trọ */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Thống kê phòng trọ</h2>
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-500">Còn trống</h3>
            <p className="text-2xl font-semibold text-gray-900">{stats.rooms.available}</p>
            <p className="text-sm text-gray-500 mt-1">
              {stats.rooms.total > 0 
                ? `${Math.round((stats.rooms.available / stats.rooms.total) * 100)}% tổng số phòng` 
                : '0% tổng số phòng'}
            </p>
          </div>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-500">Đã cho thuê</h3>
            <p className="text-2xl font-semibold text-gray-900">{stats.rooms.rented}</p>
            <p className="text-sm text-gray-500 mt-1">
              {stats.rooms.total > 0 
                ? `${Math.round((stats.rooms.rented / stats.rooms.total) * 100)}% tổng số phòng` 
                : '0% tổng số phòng'}
            </p>
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-500">Tin nổi bật</h3>
            <p className="text-2xl font-semibold text-gray-900">{stats.rooms.highlighted}</p>
            <p className="text-sm text-gray-500 mt-1">
              {stats.rooms.total > 0 
                ? `${Math.round((stats.rooms.highlighted / stats.rooms.total) * 100)}% tổng số phòng` 
                : '0% tổng số phòng'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
