const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const swaggerUi = require('swagger-ui-express');
const swaggerSpecs = require('./config/swagger');
const connectDB = require('./config/db');
const { notFound, errorHandler } = require('./middlewares/errorMiddleware');
const scheduledService = require('./services/scheduledService');

// Load env vars
dotenv.config();

// Connect to database
connectDB();

// Route files
const authRoutes = require('./routes/authRoutes');
const roomRoutes = require('./routes/roomRoutes');
const transactionRoutes = require('./routes/transactionRoutes');
const amenityRoutes = require('./routes/amenityRoutes');
const categoryRoutes = require('./routes/categoryRoutes');

const adminRoutes = require('./routes/adminRoutes');

const app = express();

// Body parser
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Enable CORS với cấu hình phù hợp cho credentials
// Danh sách các origin được phép truy cập API
const allowedOrigins = [
  'http://localhost:5000', 
  'http://localhost:3000',           // Frontend local development
  'http://127.0.0.1:3000',           // Alternative local address
  'http://localhost:5173',           // Vite default port
  'https://timphongtro.vercel.app'   // Production frontend (thay đổi nếu cần)
];

const corsOptions = {
  origin: function (origin, callback) {
    // Cho phép requests không có origin (như mobile apps hoặc curl)
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,                 // Cho phép gửi credentials (cookies, auth headers)
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], // Các methods được phép
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'], // Các headers được phép
  exposedHeaders: ['Content-Range', 'X-Total-Count'], // Headers mà client có thể đọc
  maxAge: 86400,                     // Cache preflight request trong 24 giờ
  optionsSuccessStatus: 200          // Một số trình duyệt cũ (IE11) có vấn đề với status 204
};

app.use(cors(corsOptions));

// Log CORS errors
app.use((err, req, res, next) => {
  if (err.message === 'Not allowed by CORS') {
    console.error('CORS Error:', err.message, 'Origin:', req.headers.origin);
    return res.status(403).json({
      success: false,
      error: 'CORS Error: Origin not allowed'
    });
  }
  next(err);
});

// Set static folder for uploads
const uploadPath = process.env.UPLOAD_PATH || 'uploads';
const uploadsDir = path.join(__dirname, uploadPath);

// Đảm bảo thư mục uploads tồn tại
if (!fs.existsSync(uploadsDir)) {
  console.log(`Thư mục ${uploadPath} không tồn tại, đang tạo mới...`);
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Phục vụ thư mục uploads với cấu hình CORS
app.use('/uploads', (req, res, next) => {
  // Thêm headers CORS cho tài nguyên tĩnh
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
}, express.static(uploadsDir));

console.log(`Phục vụ thư mục uploads tại: ${uploadsDir} (với CORS enabled)`);

// Mount routes
app.use('/api/auth', authRoutes);
app.use('/api/rooms', roomRoutes);
app.use('/api/transactions', transactionRoutes);
app.use('/api/amenities', amenityRoutes);
app.use('/api/categories', categoryRoutes);

app.use('/api/admin', adminRoutes);

// API documentation route
app.get('/', (req, res) => {
  res.json({
    message: 'Chào mừng đến với API hệ thống tìm phòng trọ',
    version: '1.0.0'
  });
});

// Route kiểm tra CORS
app.get('/api/cors-test', (req, res) => {
  res.json({
    success: true,
    message: 'CORS đã được cấu hình đúng!',
    origin: req.headers.origin || 'Không có origin',
    credentials: true,
    timestamp: new Date().toISOString()
  });
});

// Route test scheduled service (chỉ dùng để debug)
app.get('/api/test-highlight-expiry', async (req, res) => {
  try {
    console.log('[Test] Chạy thủ công job hạ cấp tin nổi bật...');
    const result = await scheduledService.runHighlightExpiryJobManually();

    res.json({
      success: true,
      message: 'Đã chạy thủ công job hạ cấp tin nổi bật',
      result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('[Test] Lỗi khi chạy job:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi chạy job hạ cấp tin nổi bật',
      error: error.message
    });
  }
});

// Route kiểm tra trạng thái scheduled jobs
app.get('/api/scheduled-jobs/status', (req, res) => {
  try {
    const status = scheduledService.getStatus();
    res.json({
      success: true,
      message: 'Trạng thái scheduled jobs',
      data: {
        jobs: status,
        serverTime: new Date().toISOString(),
        uptime: process.uptime()
      }
    });
  } catch (error) {
    console.error('[Status] Lỗi khi lấy trạng thái jobs:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi lấy trạng thái scheduled jobs',
      error: error.message
    });
  }
});

// Route kiểm tra CORS với cookies
app.post('/api/cors-test-with-credentials', (req, res) => {
  // Thiết lập một cookie để kiểm tra
  res.cookie('cors-test-cookie', 'success', {
    maxAge: 60000, // 1 phút
    httpOnly: true,
    sameSite: 'strict'
  });

  res.json({
    success: true,
    message: 'CORS với credentials đã được cấu hình đúng!',
    origin: req.headers.origin || 'Không có origin',
    receivedCredentials: !!req.cookies, // Kiểm tra xem có nhận được cookies không
    timestamp: new Date().toISOString()
  });
});

// Route kiểm tra upload file
const handleUpload = require('./middlewares/uploadMiddleware');
app.post('/api/upload-test', handleUpload, (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Không có file nào được upload'
      });
    }

    const uploadedFiles = req.files.map(file => ({
      filename: file.filename,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: `${(file.size / 1024).toFixed(2)} KB`,
      path: `/uploads/${file.filename}`
    }));

    res.json({
      success: true,
      message: `Đã upload ${req.files.length} file thành công`,
      files: uploadedFiles,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Lỗi kiểm tra upload:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi kiểm tra upload',
      error: error.message
    });
  }
});

// Route kiểm tra hình ảnh
app.get('/api/images-test', (req, res) => {
  try {
    // Lấy danh sách tất cả các file trong thư mục uploads
    fs.readdir(uploadsDir, (err, files) => {
      if (err) {
        console.error('Lỗi khi đọc thư mục uploads:', err);
        return res.status(500).json({
          success: false,
          message: 'Lỗi khi đọc thư mục uploads',
          error: err.message
        });
      }

      // Lọc ra các file hình ảnh
      const imageFiles = files.filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ['.jpg', '.jpeg', '.png', '.gif'].includes(ext);
      });

      // Tạo đường dẫn đầy đủ cho mỗi file
      const images = imageFiles.map(file => ({
        filename: file,
        path: `/uploads/${file}`,
        fullPath: path.join(uploadsDir, file),
        size: fs.statSync(path.join(uploadsDir, file)).size,
        url: `${req.protocol}://${req.get('host')}/uploads/${file}`
      }));

      res.json({
        success: true,
        message: `Tìm thấy ${images.length} hình ảnh trong thư mục uploads`,
        uploadDir: uploadsDir,
        serverUrl: `${req.protocol}://${req.get('host')}`,
        images
      });
    });
  } catch (error) {
    console.error('Lỗi kiểm tra hình ảnh:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi kiểm tra hình ảnh',
      error: error.message
    });
  }
});

// Swagger UI
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'API Hệ thống tìm phòng trọ'
}));

// Error handling middlewares
app.use(notFound);
app.use(errorHandler);

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`Server đang chạy trên cổng ${PORT}`);

  // Khởi tạo và bắt đầu scheduled services
  try {
    scheduledService.init();
    scheduledService.startAll();
    console.log('✓ Scheduled services đã được khởi động thành công');
  } catch (error) {
    console.error('✗ Lỗi khởi động scheduled services:', error);
  }
});
