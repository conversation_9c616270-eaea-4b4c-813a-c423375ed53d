<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Highlight Expiry Real-time</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .room-list { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; margin-top: 20px; }
        .room-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: white; }
        .room-card.highlighted { border-color: #ffc107; background: #fff8e1; }
        .room-card.regular { border-color: #6c757d; background: #f8f9fa; }
        .room-title { font-weight: bold; color: #dc3545; margin-bottom: 10px; }
        .room-status { font-size: 12px; padding: 4px 8px; border-radius: 4px; }
        .status-highlighted { background: #ffc107; color: #000; }
        .status-regular { background: #6c757d; color: white; }
        .countdown { font-weight: bold; color: #dc3545; }
        .logs { max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Highlight Expiry Real-time</h1>
        <p>Kiểm tra tính năng tự động chuyển tin nổi bật thành tin thường real-time</p>
        
        <div class="status info">
            <strong>Socket Status:</strong> <span id="socketStatus">Disconnected</span>
        </div>

        <div>
            <button class="btn-primary" onclick="loadRooms()">Load Rooms</button>
            <button class="btn-success" onclick="connectSocket()">Connect Socket</button>
            <button class="btn-danger" onclick="disconnectSocket()">Disconnect Socket</button>
        </div>

        <div id="roomsContainer" class="room-list"></div>
        
        <div id="logs" class="logs">
            <strong>Event Logs:</strong><br>
        </div>
    </div>

    <script>
        let socket = null;
        let rooms = [];
        let countdownIntervals = {};

        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            logsDiv.appendChild(div);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }

        function updateSocketStatus(status) {
            document.getElementById('socketStatus').textContent = status;
        }

        function connectSocket() {
            if (socket) {
                socket.disconnect();
            }

            socket = io('http://localhost:5000');

            socket.on('connect', () => {
                log('✅ Socket connected', 'success');
                updateSocketStatus('Connected');
            });

            socket.on('disconnect', () => {
                log('❌ Socket disconnected', 'error');
                updateSocketStatus('Disconnected');
            });

            socket.on('highlight_expired', (data) => {
                log(`🔄 Highlight expired: ${data.title} (${data.roomId})`, 'warning');
                updateRoomStatus(data.roomId, false);
            });

            socket.on('room_highlight_updated', (data) => {
                log(`🔄 Room highlight updated: ${data.roomId} -> ${data.isHighlighted ? 'Highlighted' : 'Regular'}`, 'info');
                updateRoomStatus(data.roomId, data.isHighlighted);
            });

            socket.on('highlight_activated', (data) => {
                log(`✨ Highlight activated: ${data.title} (${data.roomId})`, 'success');
                updateRoomStatus(data.roomId, true);
            });
        }

        function disconnectSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
                updateSocketStatus('Disconnected');
                log('🔌 Socket disconnected manually', 'info');
            }
        }

        async function loadRooms() {
            try {
                log('📡 Loading rooms...', 'info');
                
                // Load highlighted rooms
                const highlightedResponse = await fetch('http://localhost:5000/api/rooms?isHighlighted=true&limit=10');
                const highlightedData = await highlightedResponse.json();
                
                // Load regular rooms
                const regularResponse = await fetch('http://localhost:5000/api/rooms?isHighlighted=false&limit=10');
                const regularData = await regularResponse.json();
                
                rooms = [
                    ...(highlightedData.success ? highlightedData.data.rooms : []),
                    ...(regularData.success ? regularData.data.rooms : [])
                ];
                
                log(`✅ Loaded ${rooms.length} rooms (${highlightedData.data?.rooms?.length || 0} highlighted, ${regularData.data?.rooms?.length || 0} regular)`, 'success');
                renderRooms();
                
            } catch (error) {
                log(`❌ Error loading rooms: ${error.message}`, 'error');
            }
        }

        function renderRooms() {
            const container = document.getElementById('roomsContainer');
            container.innerHTML = '';
            
            rooms.forEach(room => {
                const roomDiv = document.createElement('div');
                roomDiv.className = `room-card ${room.isHighlighted ? 'highlighted' : 'regular'}`;
                roomDiv.id = `room-${room._id}`;
                
                let countdownHtml = '';
                if (room.isHighlighted && room.highlightExpiry) {
                    const expiry = new Date(room.highlightExpiry);
                    const now = new Date();
                    const timeLeft = expiry - now;
                    
                    if (timeLeft > 0) {
                        const minutes = Math.floor(timeLeft / (1000 * 60));
                        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                        countdownHtml = `<div class="countdown">Còn lại: ${minutes}:${seconds.toString().padStart(2, '0')}</div>`;
                        
                        // Start countdown
                        startCountdown(room._id, expiry);
                    } else {
                        countdownHtml = '<div class="countdown">Đã hết hạn</div>';
                    }
                }
                
                roomDiv.innerHTML = `
                    <div class="room-title">${room.title}</div>
                    <div class="room-status ${room.isHighlighted ? 'status-highlighted' : 'status-regular'}">
                        ${room.isHighlighted ? 'Tin nổi bật' : 'Tin thường'}
                    </div>
                    <div>Giá: ${room.price?.toLocaleString() || 0} VNĐ/tháng</div>
                    <div>Địa chỉ: ${room.address?.district || ''}, ${room.address?.city || ''}</div>
                    ${countdownHtml}
                `;
                
                container.appendChild(roomDiv);
            });
        }

        function startCountdown(roomId, expiry) {
            // Clear existing countdown
            if (countdownIntervals[roomId]) {
                clearInterval(countdownIntervals[roomId]);
            }
            
            countdownIntervals[roomId] = setInterval(() => {
                const now = new Date();
                const timeLeft = expiry - now;
                
                if (timeLeft <= 0) {
                    clearInterval(countdownIntervals[roomId]);
                    delete countdownIntervals[roomId];
                    
                    const roomDiv = document.getElementById(`room-${roomId}`);
                    if (roomDiv) {
                        const countdownDiv = roomDiv.querySelector('.countdown');
                        if (countdownDiv) {
                            countdownDiv.textContent = 'Đã hết hạn';
                        }
                    }
                    return;
                }
                
                const minutes = Math.floor(timeLeft / (1000 * 60));
                const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                
                const roomDiv = document.getElementById(`room-${roomId}`);
                if (roomDiv) {
                    const countdownDiv = roomDiv.querySelector('.countdown');
                    if (countdownDiv) {
                        countdownDiv.textContent = `Còn lại: ${minutes}:${seconds.toString().padStart(2, '0')}`;
                    }
                }
            }, 1000);
        }

        function updateRoomStatus(roomId, isHighlighted) {
            const roomIndex = rooms.findIndex(r => r._id === roomId);
            if (roomIndex !== -1) {
                rooms[roomIndex].isHighlighted = isHighlighted;
                if (!isHighlighted) {
                    rooms[roomIndex].highlightExpiry = null;
                    // Clear countdown
                    if (countdownIntervals[roomId]) {
                        clearInterval(countdownIntervals[roomId]);
                        delete countdownIntervals[roomId];
                    }
                }
                renderRooms();
            }
        }

        // Auto-connect and load on page load
        window.onload = () => {
            connectSocket();
            loadRooms();
        };

        // Cleanup on page unload
        window.onbeforeunload = () => {
            Object.values(countdownIntervals).forEach(interval => clearInterval(interval));
            if (socket) {
                socket.disconnect();
            }
        };
    </script>
</body>
</html>
