import apiClient from './apiClient';

// Service xử lý các API liên quan đến giao dịch
const transactionService = {
  // Tạo giao dịch thanh toán mới cho gói tin nổi bật
  createPayment: async (paymentData) => {
    try {
      const response = await apiClient.post('/transactions/create-payment', paymentData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Lấy danh sách giao dịch của người dùng đã đăng nhập
  getMyTransactions: async (filters = {}) => {
    try {
      const response = await apiClient.get('/transactions/my-transactions', { params: filters });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // L<PERSON>y thông tin chi tiết của một giao dịch
  getTransactionById: async (id) => {
    try {
      const response = await apiClient.get(`/transactions/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Xác nhận kết quả thanh toán từ VNPay
  verifyPayment: async (queryParams) => {
    try {
      const response = await apiClient.get('/transactions/verify-payment', { params: queryParams });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default transactionService;
