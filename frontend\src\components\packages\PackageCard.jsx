import PropTypes from 'prop-types';
import { FaCheck, FaCrown, FaStar, FaRegStar } from 'react-icons/fa';
import { formatCurrency } from '../../utils/format';

/**
 * Component hiển thị thông tin gói tin nổi bật
 * 
 * @param {Object} props - Props của component
 * @param {Object} props.packageData - Thông tin gói tin nổi bật
 * @param {boolean} props.isSelected - Trạng thái đã chọn
 * @param {Function} props.onSelect - Hàm xử lý khi chọn gói
 */
const PackageCard = ({ packageData, isSelected, onSelect }) => {
  // Xác định màu sắc và icon dựa trên loại gói
  const getPackageStyle = () => {
    switch (packageData.type) {
      case 'vip':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-400',
          textColor: 'text-yellow-600',
          icon: <FaStar className="text-yellow-500" size={20} />,
          selectedBg: 'bg-yellow-100',
        };
      case 'super_vip':
        return {
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-400',
          textColor: 'text-purple-600',
          icon: <FaCrown className="text-purple-500" size={20} />,
          selectedBg: 'bg-purple-100',
        };
      default:
        return {
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-300',
          textColor: 'text-gray-600',
          icon: <FaRegStar className="text-gray-500" size={20} />,
          selectedBg: 'bg-gray-100',
        };
    }
  };

  const style = getPackageStyle();

  return (
    <div
      className={`relative rounded-lg border-2 transition-all duration-200 cursor-pointer overflow-hidden
        ${isSelected ? `${style.selectedBg} ${style.borderColor} border-2` : `${style.bgColor} border-gray-200`}`}
      onClick={() => onSelect(packageData)}
    >
      {/* Badge loại gói */}
      <div className={`absolute top-0 right-0 ${style.textColor} ${style.bgColor} px-3 py-1 text-sm font-medium rounded-bl-lg`}>
        {style.icon}
        <span className="ml-1">
          {packageData.type === 'super_vip' ? 'Super VIP' : packageData.type === 'vip' ? 'VIP' : 'Thường'}
        </span>
      </div>

      {/* Nội dung chính */}
      <div className="p-5">
        <h3 className="text-lg font-bold mb-2">{packageData.name}</h3>
        <p className="text-gray-600 text-sm mb-4">{packageData.description}</p>
        
        {/* Giá và thời hạn */}
        <div className="mb-4">
          <p className="text-2xl font-bold text-primary">{formatCurrency(packageData.price)}</p>
          <p className="text-sm text-gray-500">Thời hạn: {packageData.duration} ngày</p>
        </div>
        
        {/* Danh sách tính năng */}
        <div className="space-y-2">
          {packageData.features.map((feature, index) => (
            <div key={index} className="flex items-start">
              <FaCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
              <span className="text-sm">{feature}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Nút chọn */}
      <div className={`p-3 text-center border-t ${isSelected ? style.borderColor : 'border-gray-200'}`}>
        <button
          type="button"
          className={`w-full py-2 px-4 rounded-md transition-colors
            ${isSelected 
              ? 'bg-primary text-white' 
              : 'bg-white text-primary border border-primary hover:bg-primary hover:text-white'
            }`}
          onClick={(e) => {
            e.stopPropagation();
            onSelect(packageData);
          }}
        >
          {isSelected ? 'Đã chọn' : 'Chọn gói này'}
        </button>
      </div>
    </div>
  );
};

PackageCard.propTypes = {
  packageData: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    price: PropTypes.number.isRequired,
    duration: PropTypes.number.isRequired,
    type: PropTypes.string.isRequired,
    features: PropTypes.arrayOf(PropTypes.string).isRequired,
  }).isRequired,
  isSelected: PropTypes.bool,
  onSelect: PropTypes.func.isRequired,
};

PackageCard.defaultProps = {
  isSelected: false,
};

export default PackageCard;
