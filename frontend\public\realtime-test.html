<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .step h3 { margin: 0 0 10px 0; color: #333; }
        button { padding: 12px 24px; margin: 10px; font-size: 16px; cursor: pointer; border: none; border-radius: 4px; }
        .btn-success { background: #28a745; color: white; }
        .btn-primary { background: #007bff; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Real-time Transition Test</h1>
        <p>Test tính năng tin nổi bật tự động chuyển xuống "PHÒNG TRỌ MỚI NHẤT"</p>
        
        <div class="step">
            <h3>🎯 Quick Test (Recommended)</h3>
            <button class="btn-success" onclick="quickTest()">Quick Test - Tạo & Trigger Ngay</button>
            <div id="quick-result" class="result"></div>
        </div>

        <div class="step">
            <h3>📋 Manual Steps</h3>
            <button class="btn-primary" onclick="createTestHighlight()">1. Tạo Test Highlight</button>
            <button class="btn-warning" onclick="openHomePage()">2. Mở HomePage</button>
            <button class="btn-danger" onclick="triggerExpiry()">3. Trigger Expiry</button>
            <div id="manual-result" class="result"></div>
        </div>

        <div class="step">
            <h3>🔍 Debug Info</h3>
            <button class="btn-primary" onclick="checkBackendStatus()">Check Backend</button>
            <button class="btn-primary" onclick="checkSocketConnection()">Check Socket</button>
            <div id="debug-result" class="result"></div>
        </div>

        <div class="step">
            <h3>✅ Expected Results</h3>
            <div class="result info">
                <strong>Khi test thành công, bạn sẽ thấy:</strong><br>
                ✅ Tin biến mất khỏi section "🌟 Phòng Trọ Nổi Bật"<br>
                ✅ Tin xuất hiện ở vị trí đầu tiên trong "Phòng Trọ Mới Nhất"<br>
                ✅ Toast notification hiển thị<br>
                ✅ Không cần refresh trang<br>
                ✅ Transition mượt mà
            </div>
        </div>
    </div>

    <script>
        let testRoomId = null;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
            console.log(`[RealTimeTest] ${message}`);
        }

        async function quickTest() {
            try {
                log('quick-result', '🚀 Starting quick test...', 'info');
                
                // Step 1: Create test highlight
                const roomsResponse = await fetch('http://localhost:5000/api/rooms?limit=1');
                const roomsData = await roomsResponse.json();
                
                if (!roomsData.success || roomsData.data.rooms.length === 0) {
                    throw new Error('Không tìm thấy phòng nào');
                }
                
                testRoomId = roomsData.data.rooms[0]._id;
                const roomTitle = roomsData.data.rooms[0].title;
                
                log('quick-result', `📝 Using room: "${roomTitle}"<br>🆔 Room ID: ${testRoomId}`, 'info');
                
                // Create test highlight
                const createResponse = await fetch('http://localhost:5000/api/test/create-test-highlight', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ roomId: testRoomId, minutes: 0.5 })
                });
                
                const createData = await createResponse.json();
                if (!createData.success) {
                    throw new Error(createData.message);
                }
                
                log('quick-result', '✅ Test highlight created!<br>⏰ Expires in 30 seconds', 'success');
                
                // Step 2: Open HomePage
                window.open('http://localhost:3000', '_blank');
                
                // Step 3: Wait 2 seconds then trigger expiry
                setTimeout(async () => {
                    try {
                        log('quick-result', '🔄 Triggering expiry job...', 'warning');
                        
                        const expiryResponse = await fetch('http://localhost:5000/api/test/run-highlight-expiry-job', {
                            method: 'POST'
                        });
                        
                        const expiryData = await expiryResponse.json();
                        
                        if (expiryData.success) {
                            log('quick-result', `🎉 SUCCESS!<br>✅ HomePage opened<br>✅ Expiry job triggered<br>📊 Processed: ${expiryData.data.processed} rooms<br>🔍 Check HomePage for real-time transition!`, 'success');
                        } else {
                            throw new Error(expiryData.message);
                        }
                    } catch (error) {
                        log('quick-result', `❌ Error triggering expiry: ${error.message}`, 'error');
                    }
                }, 2000);
                
            } catch (error) {
                log('quick-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function createTestHighlight() {
            try {
                log('manual-result', '🔄 Creating test highlight...', 'info');
                
                const roomsResponse = await fetch('http://localhost:5000/api/rooms?limit=1');
                const roomsData = await roomsResponse.json();
                
                if (!roomsData.success || roomsData.data.rooms.length === 0) {
                    throw new Error('Không tìm thấy phòng nào');
                }
                
                testRoomId = roomsData.data.rooms[0]._id;
                const roomTitle = roomsData.data.rooms[0].title;
                
                const response = await fetch('http://localhost:5000/api/test/create-test-highlight', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ roomId: testRoomId, minutes: 1 })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log('manual-result', `✅ Test highlight created!<br>📝 Room: "${roomTitle}"<br>🆔 Room ID: ${testRoomId}<br>⏰ Expires in 1 minute`, 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log('manual-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        function openHomePage() {
            window.open('http://localhost:3000', '_blank');
            log('manual-result', '✅ HomePage opened in new tab<br>🔍 Open Developer Console to see logs', 'success');
        }

        async function triggerExpiry() {
            try {
                log('manual-result', '🔄 Triggering expiry job...', 'warning');
                
                const response = await fetch('http://localhost:5000/api/test/run-highlight-expiry-job', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log('manual-result', `✅ Expiry job completed!<br>📊 Processed: ${data.data.processed} rooms<br>⏱️ Duration: ${data.data.duration}ms<br>🔍 Check HomePage for real-time update`, 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log('manual-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function checkBackendStatus() {
            try {
                log('debug-result', '🔄 Checking backend status...', 'info');
                
                const response = await fetch('http://localhost:5000/api/test/expiring-highlights');
                const data = await response.json();
                
                if (data.success) {
                    log('debug-result', `✅ Backend OK<br>📊 Found ${data.data.length} expiring rooms<br>🔗 Backend is running properly`, 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log('debug-result', `❌ Backend Error: ${error.message}<br>🔧 Make sure backend is running on port 5000`, 'error');
            }
        }

        async function checkSocketConnection() {
            try {
                log('debug-result', '🔄 Checking socket connection...', 'info');
                
                // This will be checked from the opened HomePage
                log('debug-result', '🔍 Socket check requires HomePage to be open<br>📋 Steps:<br>1. Open HomePage<br>2. Open Developer Console<br>3. Look for socket connection logs', 'warning');
                
            } catch (error) {
                log('debug-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        // Auto-instructions
        window.onload = () => {
            console.log('[RealTimeTest] 🚀 Real-time Transition Test Ready');
            console.log('[RealTimeTest] 💡 Recommendation: Use "Quick Test" for fastest results');
        };
    </script>
</body>
</html>
