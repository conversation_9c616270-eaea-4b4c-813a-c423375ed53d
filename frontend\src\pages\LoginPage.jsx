import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { FaEnvelope } from 'react-icons/fa';
import { useAuthContext } from '../contexts';

const LoginPage = () => {
  const { login } = useAuthContext();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [showEmailVerificationAlert, setShowEmailVerificationAlert] = useState(false);
  const [userEmail, setUserEmail] = useState('');

  const { register, handleSubmit, formState: { errors } } = useForm();

  const onSubmit = async (data) => {
    setIsLoading(true);
    setShowEmailVerificationAlert(false);

    try {
      await login(data);
      toast.success('Đăng nhập thành công!');
      navigate('/');
    } catch (error) {
      console.log('Login error:', error);

      // <PERSON><PERSON>m tra nếu lỗi là do email chưa được xác nhận
      if (error.isEmailVerified === false) {
        setUserEmail(data.email);
        setShowEmailVerificationAlert(true);
        toast.error('Vui lòng xác nhận email trước khi đăng nhập.');
      } else {
        // Hiển thị thông báo lỗi cụ thể từ server
        let errorMessage = 'Đăng nhập thất bại. Vui lòng thử lại.';

        if (error.message) {
          errorMessage = error.message;
        } else if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }

        toast.error(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Đăng nhập vào tài khoản
          </h2>
          <p className="mt-2 text-base text-gray-600">
            Hoặc{' '}
            <Link to="/register" className="font-medium text-primary hover:text-primary-dark">
              đăng ký tài khoản mới
            </Link>
          </p>
        </div>

        <div className="mt-8">
          <div className="bg-white py-8 px-6 shadow rounded-lg">
            {/* Email Verification Alert */}
            {showEmailVerificationAlert && (
              <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <FaEnvelope className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Email chưa được xác nhận
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>Tài khoản của bạn chưa được kích hoạt. Vui lòng kiểm tra email để xác nhận tài khoản.</p>
                    </div>
                    <div className="mt-4">
                      <div className="flex space-x-3">
                        <Link
                          to="/resend-verification"
                          state={{ email: userEmail }}
                          className="text-sm bg-yellow-100 text-yellow-800 rounded-md px-3 py-2 font-medium hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                        >
                          Gửi lại email xác nhận
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              <div className="form-group">
                <label htmlFor="email" className="form-label">
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  autoComplete="email"
                  placeholder="Nhập địa chỉ email của bạn"
                  className="form-input"
                  {...register('email', {
                    required: 'Email là bắt buộc',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Email không hợp lệ',
                    },
                  })}
                />
                {errors.email && (
                  <p className="form-error">{errors.email.message}</p>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  Mật khẩu
                </label>
                <input
                  id="password"
                  type="password"
                  autoComplete="current-password"
                  placeholder="Nhập mật khẩu của bạn"
                  className="form-input"
                  {...register('password', {
                    required: 'Mật khẩu là bắt buộc',
                    minLength: {
                      value: 6,
                      message: 'Mật khẩu phải có ít nhất 6 ký tự',
                    },
                  })}
                />
                {errors.password && (
                  <p className="form-error">{errors.password.message}</p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="form-checkbox"
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-gray-700">
                    Ghi nhớ đăng nhập
                  </label>
                </div>

                <div>
                  <Link to="/forgot-password" className="font-medium text-primary hover:text-primary-dark">
                    Quên mật khẩu?
                  </Link>
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full btn btn-primary"
                >
                  {isLoading ? 'Đang xử lý...' : 'Đăng nhập'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
