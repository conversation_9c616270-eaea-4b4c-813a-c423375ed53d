import { useState, useEffect, useCallback } from 'react';
import { authService } from '../services';

// Hook quản lý trạng thái xác thực người dùng
const useAuth = () => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Hàm đăng xuất (định nghĩa trước để sử dụng trong checkAuthStatus)
  const logout = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      await authService.logout();
    } catch (err) {
      setError(err.message || 'Đăng xuất thất bại');
    } finally {
      setUser(null);
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  }, []);

  // Hàm kiểm tra trạng thái xác thực khi component mount
  const checkAuthStatus = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Đang kiểm tra trạng thái xác thực...');

      // Kiểm tra xem có token trong localStorage không
      if (authService.isAuthenticated()) {
        console.log('Đã tìm thấy token trong localStorage');

        // Lấy thông tin người dùng từ localStorage
        const userData = authService.getUser();

        if (userData) {
          console.log('Đã tìm thấy thông tin người dùng trong localStorage');
          setUser(userData);
          setIsAuthenticated(true);

          // Kiểm tra tính hợp lệ của token bằng cách gọi API
          try {
            console.log('Đang xác thực token với server...');
            const response = await authService.getCurrentUser();

            if (response.success) {
              console.log('Token hợp lệ, cập nhật thông tin người dùng');
              // Cập nhật thông tin người dùng từ server
              setUser(response.data);

              // Cập nhật thông tin người dùng trong localStorage
              localStorage.setItem('user', JSON.stringify(response.data));
            }
          } catch (err) {
            console.error('Lỗi khi xác thực token:', err);
            // Nếu API gặp lỗi, có thể token đã hết hạn
            if (err.status === 401) {
              console.log('Token không hợp lệ hoặc đã hết hạn, đang đăng xuất...');
              await logout();
            }
          }
        } else {
          console.log('Không tìm thấy thông tin người dùng, đang lấy từ API...');
          // Nếu không có thông tin người dùng, lấy từ API
          try {
            const response = await authService.getCurrentUser();
            if (response.success) {
              console.log('Đã lấy thông tin người dùng từ API');
              setUser(response.data);
              setIsAuthenticated(true);

              // Lưu thông tin người dùng vào localStorage
              localStorage.setItem('user', JSON.stringify(response.data));
            }
          } catch (err) {
            console.error('Lỗi khi lấy thông tin người dùng từ API:', err);
            // Nếu API gặp lỗi, đăng xuất
            await logout();
          }
        }
      } else {
        console.log('Không tìm thấy token hoặc token không hợp lệ');
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (err) {
      console.error('Lỗi khi kiểm tra trạng thái xác thực:', err);
      setError(err.message || 'Có lỗi xảy ra khi kiểm tra trạng thái xác thực');
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, [logout]);

  // Hàm đăng nhập
  const login = async (credentials) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.login(credentials);
      if (response.success) {
        setUser(response.data.user);
        setIsAuthenticated(true);
        return response;
      }
    } catch (err) {
      console.log('useAuth login error:', err);
      setError(err.message || 'Đăng nhập thất bại');
      // Đảm bảo throw error với đầy đủ thông tin
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm đăng ký
  const register = async (userData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.register(userData);
      if (response.success) {
        // Không tự động đăng nhập sau khi đăng ký
        // User cần xác nhận email trước
        return response;
      }
    } catch (err) {
      setError(err.message || 'Đăng ký thất bại');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };



  // Hàm cập nhật thông tin người dùng
  const updateProfile = async (userData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.updateProfile(userData);
      if (response.success) {
        setUser(response.data);
        return response;
      }
    } catch (err) {
      setError(err.message || 'Cập nhật thông tin thất bại');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm đổi mật khẩu
  const changePassword = async (passwordData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.changePassword(passwordData);
      return response;
    } catch (err) {
      setError(err.message || 'Đổi mật khẩu thất bại');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Kiểm tra trạng thái xác thực khi component mount
  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    // Lấy token trực tiếp từ localStorage mỗi khi được gọi
    get token() {
      return authService.getToken();
    },
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    checkAuthStatus,
  };
};

export default useAuth;
