const Transaction = require('../models/Transaction');
const Room = require('../models/Room');
const { createPaymentUrl, verifyReturnUrl } = require('../utils/vnpayUtils');
const moment = require('moment');

// @desc    Tạo giao dịch thanh toán VNPay
// @route   POST /api/transactions/create-payment
// @access  Private
const createPayment = async (req, res) => {
  try {
    const { roomId, packageType } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!roomId || !packageType) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin phòng trọ hoặc gói thanh toán'
      });
    }

    // Kiểm tra gói thanh toán hợp lệ (chỉ có 2 gói: regular và special)
    if (!['regular', 'special'].includes(packageType)) {
      return res.status(400).json({
        success: false,
        message: '<PERSON><PERSON><PERSON> toán không hợp lệ. Chỉ hỗ trợ gói "regular" hoặc "special"'
      });
    }

    // Tìm phòng trọ
    const room = await Room.findById(roomId);

    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy phòng trọ'
      });
    }

    // Kiểm tra quyền sở hữu
    if (room.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Bạn không có quyền thanh toán cho phòng trọ này'
      });
    }

    // Tính số tiền thanh toán dựa trên gói (2 gói đơn giản)
    let amount = 0;

    switch (packageType) {
      case 'regular':
        amount = 0; // Gói thường - miễn phí (chuyển về gói thường)
        break;
      case 'special':
        amount = 100000; // Gói đặc biệt - 100,000 VNĐ (nâng cấp lên gói đặc biệt)
        break;
    }

    console.log(`[Transaction] Package: ${packageType}, Amount: ${amount} VNĐ`);

    // Xử lý gói miễn phí (regular) - không cần VNPay
    if (packageType === 'regular' && amount === 0) {
      console.log('[Transaction] Processing free package (regular)...');

      // Tạo transaction với trạng thái completed ngay lập tức
      const transaction = new Transaction({
        user: req.user._id,
        room: roomId,
        packageType,
        amount,
        status: 'completed',
        completedAt: Date.now(),
        vnpayTxnRef: `FREE_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        vnpayResponseCode: '00' // Mã thành công
      });

      await transaction.save();
      console.log(`[Transaction] Free transaction created: ${transaction._id}`);

      // Cập nhật trạng thái phòng ngay lập tức
      room.isHighlighted = false; // Gói regular = không highlighted
      await room.save();
      console.log(`[Transaction] Room ${room._id} updated to regular package (isHighlighted: false)`);

      return res.status(200).json({
        success: true,
        message: 'Đã chuyển đổi thành công sang gói thường',
        data: {
          transactionId: transaction._id,
          packageType: transaction.packageType,
          amount: transaction.amount,
          status: transaction.status,
          roomId: room._id,
          isRoomHighlighted: room.isHighlighted
        }
      });
    }

    // Tạo mã giao dịch
    const txnRef = moment().format('YYYYMMDDHHmmss') + '-' + roomId.substring(0, 8);

    // Tạo giao dịch trong database
    const transaction = await Transaction.create({
      user: req.user._id,
      room: roomId,
      amount,
      packageType,
      vnpayTxnRef: txnRef,
      status: 'pending'
    });

    // Tạo thông tin đơn hàng
    const orderInfo = `Thanh toan tin dang noi bat - ${packageType}`;

    // Tạo URL thanh toán VNPay
    const ipAddr = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    const returnUrl = process.env.VNPAY_RETURN_URL;

    const paymentUrl = createPaymentUrl(
      ipAddr,
      txnRef,
      amount,
      orderInfo,
      returnUrl
    );

    res.status(200).json({
      success: true,
      data: {
        paymentUrl,
        transaction
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Xử lý callback từ VNPay
// @route   GET /api/transactions/vnpay-return
// @access  Public
const vnpayReturn = async (req, res) => {
  try {
    console.log('[VNPay Return] Received callback with params:', req.query);
    const vnpParams = req.query;

    // Kiểm tra chữ ký
    console.log('[VNPay Return] Verifying signature...');
    const isValidSignature = verifyReturnUrl(vnpParams);
    console.log('[VNPay Return] Signature valid:', isValidSignature);

    if (!isValidSignature) {
      console.error('[VNPay Return] Invalid signature');
      return res.status(400).json({
        success: false,
        message: 'Chữ ký không hợp lệ'
      });
    }

    // Lấy thông tin giao dịch
    const txnRef = vnpParams.vnp_TxnRef;
    const responseCode = vnpParams.vnp_ResponseCode;
    const txnNo = vnpParams.vnp_TransactionNo;

    console.log('[VNPay Return] Transaction details:', { txnRef, responseCode, txnNo });

    // Tìm giao dịch trong database
    console.log('[VNPay Return] Finding transaction with txnRef:', txnRef);
    const transaction = await Transaction.findOne({ vnpayTxnRef: txnRef });

    if (!transaction) {
      console.error('[VNPay Return] Transaction not found for txnRef:', txnRef);
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy giao dịch'
      });
    }

    console.log('[VNPay Return] Found transaction:', transaction._id);

    // Cập nhật thông tin giao dịch
    transaction.vnpayTxnNo = txnNo;
    transaction.vnpayResponseCode = responseCode;

    // Kiểm tra kết quả thanh toán
    console.log('[VNPay Return] Processing payment result, responseCode:', responseCode);
    if (responseCode === '00') {
      // Thanh toán thành công
      console.log('[VNPay Return] Payment successful, updating transaction status');
      transaction.status = 'completed';
      transaction.completedAt = Date.now();

      // Cập nhật trạng thái phòng trọ dựa trên gói thanh toán
      console.log('[VNPay Return] Finding room:', transaction.room);
      const room = await Room.findById(transaction.room);

      if (room) {
        console.log('[VNPay Return] Found room:', room._id, 'Current isHighlighted:', room.isHighlighted);
        // Cập nhật trạng thái highlighted dựa trên packageType
        switch (transaction.packageType) {
          case 'regular':
            room.isHighlighted = false; // Chuyển về gói thường
            break;
          case 'special':
            room.isHighlighted = true; // Nâng cấp lên gói đặc biệt
            break;
        }

        await room.save();
        console.log(`[VNPay Return] Room ${room._id} updated to package: ${transaction.packageType}, isHighlighted: ${room.isHighlighted}`);
      } else {
        console.error('[VNPay Return] Room not found:', transaction.room);
      }
    } else {
      // Thanh toán thất bại
      console.log('[VNPay Return] Payment failed, responseCode:', responseCode);
      transaction.status = 'failed';
    }

    await transaction.save();
    console.log('[VNPay Return] Transaction saved with status:', transaction.status);

    // Chuyển hướng về trang frontend
    const frontendUrl = process.env.FRONTEND_URL
      ? `${process.env.FRONTEND_URL}/payment-result`
      : 'http://localhost:3000/payment-result';

    const redirectUrl = `${frontendUrl}?vnp_TxnRef=${txnRef}&vnp_ResponseCode=${responseCode}&vnp_TransactionNo=${txnNo}`;
    console.log('[VNPay Return] Redirecting to:', redirectUrl);

    res.redirect(redirectUrl);
  } catch (error) {
    console.error('[VNPay Return] Error processing callback:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy thông tin giao dịch
// @route   GET /api/transactions/:id
// @access  Private
const getTransactionById = async (req, res) => {
  try {
    const transaction = await Transaction.findById(req.params.id)
      .populate('room', 'title')
      .populate('user', 'fullName email');

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy giao dịch'
      });
    }

    // Kiểm tra quyền truy cập
    if (transaction.user._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Bạn không có quyền xem giao dịch này'
      });
    }

    res.status(200).json({
      success: true,
      data: transaction
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy danh sách giao dịch của người dùng hiện tại
// @route   GET /api/transactions/my-transactions
// @access  Private
const getMyTransactions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Xây dựng query với user ID
    const query = { user: req.user._id };

    // Lọc theo trạng thái nếu có
    if (req.query.status && req.query.status.trim() !== '') {
      const statusValue = req.query.status.trim();
      // Validate status value
      const validStatuses = ['pending', 'completed', 'failed'];
      if (validStatuses.includes(statusValue)) {
        query.status = statusValue;
      }
    }

    // Đếm tổng số giao dịch với query filter
    const totalResults = await Transaction.countDocuments(query);
    const totalPages = Math.ceil(totalResults / limit);

    // Lấy giao dịch với phân trang và filter
    const transactions = await Transaction.find(query)
      .populate('room', 'title address images')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    res.status(200).json({
      success: true,
      data: {
        transactions,
        pagination: {
          page,
          limit,
          totalPages,
          totalResults,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });
  } catch (error) {
    console.error('[Transaction] Error fetching my transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Xác nhận kết quả thanh toán (trả về JSON)
// @route   GET /api/transactions/verify-payment
// @access  Public
const verifyPayment = async (req, res) => {
  try {
    const vnpParams = req.query;
    console.log('[VNPay Verify] Received params:', vnpParams);

    // Lấy thông tin giao dịch
    const txnRef = vnpParams.vnp_TxnRef;
    const responseCode = vnpParams.vnp_ResponseCode;

    if (!txnRef) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu mã tham chiếu giao dịch'
      });
    }

    // Tìm giao dịch trong database
    const transaction = await Transaction.findOne({ vnpayTxnRef: txnRef })
      .populate('room', 'title address isHighlighted')
      .populate('user', 'fullName email');

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy giao dịch'
      });
    }

    // Kiểm tra kết quả thanh toán
    const isSuccess = responseCode === '00';

    res.status(200).json({
      success: isSuccess,
      message: isSuccess ? 'Thanh toán thành công' : 'Thanh toán thất bại',
      data: {
        transactionId: transaction._id,
        vnpayTxnRef: transaction.vnpayTxnRef,
        vnpayTxnNo: vnpParams.vnp_TransactionNo,
        amount: transaction.amount,
        packageType: transaction.packageType,
        status: transaction.status,
        roomId: transaction.room._id,
        roomTitle: transaction.room.title,
        roomAddress: transaction.room.address,
        isRoomHighlighted: transaction.room.isHighlighted,
        createdAt: transaction.createdAt,
        paymentDate: transaction.completedAt || new Date()
      }
    });
  } catch (error) {
    console.error('[VNPay Verify] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi xác nhận thanh toán',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  createPayment,
  vnpayReturn,
  verifyPayment,
  getTransactionById,
  getMyTransactions
};
