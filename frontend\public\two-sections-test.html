<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Two Sections Real-time Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .step h3 { margin: 0 0 10px 0; color: #333; }
        button { padding: 12px 24px; margin: 10px; font-size: 16px; cursor: pointer; border: none; border-radius: 4px; }
        .btn-success { background: #28a745; color: white; }
        .btn-primary { background: #007bff; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .demo-section { display: flex; gap: 20px; margin-top: 20px; }
        .demo-column { flex: 1; }
        .room-item { padding: 10px; margin: 5px 0; border-radius: 4px; border: 1px solid #ddd; position: relative; }
        .room-highlighted { background: #fff8e1; border-color: #ffc107; }
        .room-regular { background: #f8f9fa; border-color: #dee2e6; }
        .countdown { font-weight: bold; color: #dc3545; position: absolute; top: 5px; right: 10px; font-size: 12px; }
        .position-indicator { font-weight: bold; color: #007bff; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Two Sections Real-time Transition Test</h1>
        <p>Test tính năng chuyển tin từ "🌟 Phòng Trọ Nổi Bật" (3 cards) xuống "Phòng Trọ Mới Nhất" (6 cards)</p>
        
        <div class="step">
            <h3>🎯 Quick Demo - Two Sections Transition</h3>
            <button class="btn-success" onclick="startTwoSectionsDemo()">Start Demo - Tạo Highlights & Test Transition</button>
            <button class="btn-primary" onclick="openHomePage()">Mở HomePage</button>
            <button class="btn-warning" onclick="triggerTransition()">Trigger Transition Now</button>
            <div id="demo-result" class="result"></div>
        </div>

        <div class="step">
            <h3>📊 Live Two Sections Status</h3>
            <button class="btn-primary" onclick="loadTwoSectionsStatus()">Load Current Status</button>
            <div id="status-result" class="result"></div>
            <div class="demo-section">
                <div class="demo-column">
                    <h4>🌟 Highlighted Rooms (Max 3)</h4>
                    <div id="highlighted-rooms"></div>
                </div>
                <div class="demo-column">
                    <h4>📝 Regular Rooms (Max 6)</h4>
                    <div id="regular-rooms"></div>
                </div>
            </div>
        </div>

        <div class="step">
            <h3>✅ Expected Two Sections Behavior</h3>
            <div class="result info">
                <strong>Khi tin nổi bật hết hạn:</strong><br>
                ✅ <strong>Section 1</strong>: Tin biến mất khỏi "🌟 Phòng Trọ Nổi Bật" (3 cards)<br>
                ✅ <strong>Section 2</strong>: Tin xuất hiện ở vị trí đầu tiên (position 0) trong "Phòng Trọ Mới Nhất" (6 cards)<br>
                ✅ <strong>Shift Down</strong>: Các tin khác trong section 2 được đẩy xuống<br>
                ✅ <strong>Limit Control</strong>: Section 2 giữ tối đa 6 cards, card cuối bị loại bỏ nếu cần<br>
                ✅ <strong>Refill</strong>: Section 1 tự động load thêm tin nổi bật khác (nếu có) để duy trì 3 cards<br>
                ✅ <strong>Real-time</strong>: Không cần refresh trang<br>
                ✅ <strong>Smooth Animations</strong>: CSS transitions mượt mà<br>
                ✅ <strong>Toast Notification</strong>: Thông báo cho user
            </div>
        </div>
    </div>

    <script>
        let statusInterval = null;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
            console.log(`[TwoSectionsTest] ${message}`);
        }

        async function startTwoSectionsDemo() {
            try {
                log('demo-result', '🚀 Starting two sections demo...', 'info');
                
                // Get multiple rooms for demo
                const roomsResponse = await fetch('http://localhost:5000/api/rooms?limit=10');
                const roomsData = await roomsResponse.json();
                
                if (!roomsData.success || roomsData.data.rooms.length < 5) {
                    throw new Error('Cần ít nhất 5 phòng để demo');
                }
                
                const rooms = roomsData.data.rooms.slice(0, 5);
                log('demo-result', `📝 Using ${rooms.length} rooms for two sections demo`, 'info');
                
                // Create highlights with different expiry times
                const highlights = [];
                for (let i = 0; i < Math.min(3, rooms.length); i++) {
                    const room = rooms[i];
                    const minutes = 0.5 + (i * 0.3); // 30s, 48s, 66s
                    
                    const response = await fetch('http://localhost:5000/api/test/create-test-highlight', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ roomId: room._id, minutes: minutes })
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        highlights.push({
                            roomId: room._id,
                            title: room.title,
                            expiryMinutes: minutes
                        });
                    }
                }
                
                log('demo-result', `✅ Created ${highlights.length} test highlights for Section 1!<br>⏰ They will expire and move to Section 2 at different times<br>🔗 Open HomePage to see two sections transition`, 'success');
                
                // Auto-open HomePage
                window.open('http://localhost:3000', '_blank');
                
                // Start live status updates
                startStatusUpdates();
                
            } catch (error) {
                log('demo-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        function openHomePage() {
            window.open('http://localhost:3000', '_blank');
            log('demo-result', '✅ HomePage opened in new tab<br>🔍 Watch for two sections transition', 'success');
        }

        async function triggerTransition() {
            try {
                log('demo-result', '🔄 Triggering transition now...', 'warning');
                
                const response = await fetch('http://localhost:5000/api/test/run-highlight-expiry-job', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log('demo-result', `✅ Transition triggered!<br>📊 Processed: ${data.data.processed} rooms<br>🔍 Check HomePage for two sections update`, 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log('demo-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function loadTwoSectionsStatus() {
            try {
                log('status-result', '🔄 Loading two sections status...', 'info');
                
                // Load highlighted rooms
                const highlightedResponse = await fetch('http://localhost:5000/api/rooms?status=available&isHighlighted=true&limit=10&sort=-createdAt');
                const highlightedData = await highlightedResponse.json();
                
                // Load regular rooms
                const regularResponse = await fetch('http://localhost:5000/api/rooms?status=available&isHighlighted=false&limit=10&sort=-createdAt');
                const regularData = await regularResponse.json();
                
                if (highlightedData.success && regularData.success) {
                    const highlighted = highlightedData.data.rooms.slice(0, 3); // Max 3 for section 1
                    const regular = regularData.data.rooms.slice(0, 6); // Max 6 for section 2
                    
                    log('status-result', `✅ Two Sections Status:<br>🌟 Section 1: ${highlighted.length}/3 highlighted rooms<br>📝 Section 2: ${regular.length}/6 regular rooms`, 'success');
                    
                    displayTwoSections(highlighted, regular);
                } else {
                    throw new Error('Failed to load rooms data');
                }
            } catch (error) {
                log('status-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        function displayTwoSections(highlighted, regular) {
            const highlightedDiv = document.getElementById('highlighted-rooms');
            const regularDiv = document.getElementById('regular-rooms');
            
            // Display highlighted rooms (Section 1 - Max 3)
            highlightedDiv.innerHTML = '';
            highlighted.forEach((room, index) => {
                const div = document.createElement('div');
                div.className = 'room-item room-highlighted';
                
                let expiryInfo = '';
                if (room.highlightExpiry) {
                    const expiry = new Date(room.highlightExpiry);
                    const now = new Date();
                    const timeLeft = expiry - now;
                    
                    if (timeLeft > 0) {
                        const minutes = Math.floor(timeLeft / (1000 * 60));
                        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                        expiryInfo = `<div class="countdown">⏰ ${minutes}:${seconds.toString().padStart(2, '0')}</div>`;
                    } else {
                        expiryInfo = '<div class="countdown">⏰ EXPIRED</div>';
                    }
                }
                
                div.innerHTML = `
                    <span class="position-indicator">#${index + 1}</span>
                    <strong>${room.title}</strong><br>
                    <small>🌟 Highlighted • ${room.price?.toLocaleString() || 0} VNĐ/tháng</small>
                    ${expiryInfo}
                `;
                
                highlightedDiv.appendChild(div);
            });
            
            // Add empty slots if less than 3
            for (let i = highlighted.length; i < 3; i++) {
                const div = document.createElement('div');
                div.className = 'room-item';
                div.style.border = '2px dashed #ddd';
                div.style.opacity = '0.5';
                div.innerHTML = `
                    <span class="position-indicator">#${i + 1}</span>
                    <em>Empty slot - waiting for highlighted room</em>
                `;
                highlightedDiv.appendChild(div);
            }
            
            // Display regular rooms (Section 2 - Max 6)
            regularDiv.innerHTML = '';
            regular.forEach((room, index) => {
                const div = document.createElement('div');
                div.className = 'room-item room-regular';
                
                div.innerHTML = `
                    <span class="position-indicator">#${index + 1}</span>
                    <strong>${room.title}</strong><br>
                    <small>📝 Regular • ${room.price?.toLocaleString() || 0} VNĐ/tháng</small>
                `;
                
                regularDiv.appendChild(div);
            });
            
            // Add empty slots if less than 6
            for (let i = regular.length; i < 6; i++) {
                const div = document.createElement('div');
                div.className = 'room-item';
                div.style.border = '2px dashed #ddd';
                div.style.opacity = '0.5';
                div.innerHTML = `
                    <span class="position-indicator">#${i + 1}</span>
                    <em>Empty slot - waiting for regular room</em>
                `;
                regularDiv.appendChild(div);
            }
        }

        function startStatusUpdates() {
            if (statusInterval) {
                clearInterval(statusInterval);
            }
            
            statusInterval = setInterval(() => {
                loadTwoSectionsStatus();
            }, 2000); // Update every 2 seconds
        }

        function stopStatusUpdates() {
            if (statusInterval) {
                clearInterval(statusInterval);
                statusInterval = null;
            }
        }

        // Auto-load status on page load
        window.onload = () => {
            console.log('[TwoSectionsTest] 🔄 Two Sections Real-time Test Ready');
            loadTwoSectionsStatus();
        };

        // Cleanup on page unload
        window.onbeforeunload = () => {
            stopStatusUpdates();
        };
    </script>
</body>
</html>
