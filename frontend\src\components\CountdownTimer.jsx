import { useState, useEffect } from 'react';
import { FaClock } from 'react-icons/fa';

const CountdownTimer = ({ 
  expiryDate, 
  onExpired, 
  className = '',
  showIcon = true,
  compact = false 
}) => {
  const [timeLeft, setTimeLeft] = useState(null);
  const [isExpired, setIsExpired] = useState(false);

  useEffect(() => {
    if (!expiryDate) {
      setTimeLeft(null);
      setIsExpired(false);
      return;
    }

    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const expiry = new Date(expiryDate).getTime();
      const difference = expiry - now;

      if (difference <= 0) {
        setIsExpired(true);
        setTimeLeft(null);
        if (onExpired && typeof onExpired === 'function') {
          onExpired();
        }
        return null;
      }

      const minutes = Math.floor(difference / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      return { minutes, seconds };
    };

    // Tính toán lần đầu
    const initialTime = calculateTimeLeft();
    setTimeLeft(initialTime);

    // Cập nhật mỗi giây
    const timer = setInterval(() => {
      const newTime = calculateTimeLeft();
      setTimeLeft(newTime);
    }, 1000);

    return () => clearInterval(timer);
  }, [expiryDate, onExpired]);

  // Không hiển thị gì nếu không có thời gian hết hạn
  if (!expiryDate || isExpired) {
    return null;
  }

  // Không hiển thị gì nếu chưa tính được thời gian
  if (!timeLeft) {
    return null;
  }

  const formatTime = () => {
    if (compact) {
      return `${timeLeft.minutes}:${timeLeft.seconds.toString().padStart(2, '0')}`;
    }
    
    if (timeLeft.minutes > 0) {
      return `${timeLeft.minutes} phút ${timeLeft.seconds} giây`;
    } else {
      return `${timeLeft.seconds} giây`;
    }
  };

  const getColorClass = () => {
    const totalSeconds = timeLeft.minutes * 60 + timeLeft.seconds;
    
    if (totalSeconds <= 30) {
      return 'text-red-600 bg-red-50 border-red-200';
    } else if (totalSeconds <= 120) {
      return 'text-orange-600 bg-orange-50 border-orange-200';
    } else {
      return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  return (
    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getColorClass()} ${className}`}>
      {showIcon && <FaClock className="mr-1" />}
      <span>
        {compact ? formatTime() : `Còn ${formatTime()}`}
      </span>
    </div>
  );
};

export default CountdownTimer;
