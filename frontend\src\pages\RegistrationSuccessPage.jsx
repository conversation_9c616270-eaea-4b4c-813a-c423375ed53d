import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaEnvelope, FaCheckCircle, FaSpinner, FaRedo } from 'react-icons/fa';
import { authService } from '../services';

const RegistrationSuccessPage = () => {
  const location = useLocation();
  const [isResending, setIsResending] = useState(false);
  
  // Lấy email từ state được truyền từ RegisterPage
  const email = location.state?.email || '';

  const handleResendEmail = async () => {
    if (!email) {
      toast.error('Không tìm thấy địa chỉ email. Vui lòng đăng ký lại.');
      return;
    }

    setIsResending(true);
    try {
      const response = await authService.resendVerificationEmail(email);
      if (response.success) {
        toast.success('Email xác nhận đã được gửi lại thành công!');
      }
    } catch (error) {
      toast.error(error.message || '<PERSON>hông thể gửi lại email xác nhận. <PERSON>ui lòng thử lại sau.');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
            <FaCheckCircle className="h-8 w-8 text-green-600" />
          </div>
          
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Đăng ký thành công!
          </h2>
          
          <p className="mt-2 text-base text-gray-600">
            Chúng tôi đã gửi email xác nhận đến địa chỉ của bạn
          </p>
        </div>

        <div className="mt-8">
          <div className="bg-white py-8 px-6 shadow rounded-lg">
            <div className="text-center space-y-6">
              <div className="flex items-center justify-center">
                <FaEnvelope className="h-12 w-12 text-blue-500" />
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Kiểm tra email của bạn
                </h3>
                {email && (
                  <p className="text-sm text-gray-600 mb-4">
                    Email đã được gửi đến: <span className="font-medium text-gray-900">{email}</span>
                  </p>
                )}
                <p className="text-sm text-gray-600">
                  Vui lòng kiểm tra hộp thư đến (và cả thư mục spam) để tìm email xác nhận từ chúng tôi.
                  Nhấp vào liên kết trong email để kích hoạt tài khoản của bạn.
                </p>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <FaEnvelope className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Lưu ý quan trọng
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <ul className="list-disc list-inside space-y-1">
                        <li>Email xác nhận có thể mất vài phút để đến</li>
                        <li>Kiểm tra cả thư mục spam/junk mail</li>
                        <li>Liên kết xác nhận sẽ hết hạn sau 24 giờ</li>
                        <li>Bạn cần xác nhận email trước khi có thể đăng nhập</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <button
                  onClick={handleResendEmail}
                  disabled={isResending || !email}
                  className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isResending ? (
                    <>
                      <FaSpinner className="animate-spin h-4 w-4 mr-2" />
                      Đang gửi...
                    </>
                  ) : (
                    <>
                      <FaRedo className="h-4 w-4 mr-2" />
                      Gửi lại email xác nhận
                    </>
                  )}
                </button>

                <div className="text-center">
                  <Link
                    to="/login"
                    className="font-medium text-primary hover:text-primary-dark"
                  >
                    Đã xác nhận email? Đăng nhập ngay
                  </Link>
                </div>

                <div className="text-center">
                  <Link
                    to="/"
                    className="text-sm text-gray-500 hover:text-gray-700"
                  >
                    ← Quay về trang chủ
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegistrationSuccessPage;
