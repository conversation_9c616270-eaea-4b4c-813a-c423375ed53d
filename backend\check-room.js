const mongoose = require('mongoose');
const Room = require('./src/models/Room');
const User = require('./src/models/User');
require('dotenv').config();

async function checkRoom() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
    
    // Kiểm tra tất cả tin nổi bật
    const highlightedRooms = await Room.find({isHighlighted: true}).populate('user', 'fullName email');

    console.log(`Tìm thấy ${highlightedRooms.length} tin nổi bật:`);

    highlightedRooms.forEach((room, index) => {
      const now = new Date();
      const expired = room.highlightExpiry ? room.highlightExpiry < now : false;
      const timeLeft = room.highlightExpiry ? room.highlightExpiry - now : 0;

      console.log(`\n${index + 1}. Room: ${room.title}`);
      console.log(`   ID: ${room._id.toString()}`);
      console.log(`   User: ${room.user?.fullName} (${room.user?.email})`);
      console.log(`   isHighlighted: ${room.isHighlighted}`);
      console.log(`   highlightExpiry: ${room.highlightExpiry}`);
      console.log(`   Current time: ${now.toISOString()}`);
      console.log(`   Is expired: ${expired}`);

      if (expired) {
        console.log(`   -> Room should be downgraded!`);
      } else if (room.highlightExpiry) {
        console.log(`   -> Time left: ${Math.floor(timeLeft / 1000)} seconds`);
      }
    });

    const room = await Room.findOne({title: 'Phòng Test Real-time'});
    
    if (room) {
      const now = new Date();
      const expired = room.highlightExpiry ? room.highlightExpiry < now : false;
      
      console.log('Room found:');
      console.log('  ID:', room._id.toString());
      console.log('  Title:', room.title);
      console.log('  isHighlighted:', room.isHighlighted);
      console.log('  highlightExpiry:', room.highlightExpiry);
      console.log('  Current time:', now.toISOString());
      console.log('  Is expired:', expired);
      
      if (expired) {
        console.log('  -> Room should be downgraded!');
      } else {
        const timeLeft = room.highlightExpiry ? room.highlightExpiry - now : 0;
        console.log('  -> Time left:', Math.floor(timeLeft / 1000), 'seconds');
      }
    } else {
      console.log('Room not found');
    }
    
    await mongoose.connection.close();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
}

checkRoom();
