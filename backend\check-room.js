const mongoose = require('mongoose');
const Room = require('./src/models/Room');
require('dotenv').config();

async function checkRoom() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
    
    const room = await Room.findOne({title: 'Phòng Test Real-time'});
    
    if (room) {
      const now = new Date();
      const expired = room.highlightExpiry ? room.highlightExpiry < now : false;
      
      console.log('Room found:');
      console.log('  ID:', room._id.toString());
      console.log('  Title:', room.title);
      console.log('  isHighlighted:', room.isHighlighted);
      console.log('  highlightExpiry:', room.highlightExpiry);
      console.log('  Current time:', now.toISOString());
      console.log('  Is expired:', expired);
      
      if (expired) {
        console.log('  -> Room should be downgraded!');
      } else {
        const timeLeft = room.highlightExpiry ? room.highlightExpiry - now : 0;
        console.log('  -> Time left:', Math.floor(timeLeft / 1000), 'seconds');
      }
    } else {
      console.log('Room not found');
    }
    
    await mongoose.connection.close();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
}

checkRoom();
