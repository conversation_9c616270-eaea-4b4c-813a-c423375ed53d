/**
 * CSS fixes for VNPay Sandbox integration
 *
 * This file contains CSS fixes for various issues with VNPay Sandbox:
 * 1. Deprecated -ms-high-contrast CSS property
 * 2. Content Security Policy (CSP) related styling
 * 3. General styling fixes for VNPay elements
 */

/* Replace deprecated -ms-high-contrast with modern Forced Colors Mode */
@media (forced-colors: active) {
  /* General fixes for high contrast mode */
  button,
  input,
  select,
  textarea {
    forced-color-adjust: none;
    border: 1px solid currentColor;
  }

  /* Specific fixes for VNPay elements */
  [class*="vnpay-"] button,
  [class*="vnpay-"] input,
  [class*="vnpay-"] select,
  [class*="vnpay-"] textarea,
  [id*="vnpay-"] button,
  [id*="vnpay-"] input,
  [id*="vnpay-"] select,
  [id*="vnpay-"] textarea,
  [class*="vnp-"] button,
  [class*="vnp-"] input,
  [class*="vnp-"] select,
  [class*="vnp-"] textarea,
  [id*="vnp-"] button,
  [id*="vnp-"] input,
  [id*="vnp-"] select,
  [id*="vnp-"] textarea {
    forced-color-adjust: none;
    border: 1px solid currentColor;
    background-color: Canvas;
    color: CanvasText;
  }

  /* Fix for specific VNPay elements */
  .vnp-button,
  .vnpay-button,
  button[class*="vnp-"],
  button[class*="vnpay-"] {
    forced-color-adjust: none;
    border: 1px solid ButtonText;
    background-color: ButtonFace;
    color: ButtonText;
  }

  /* Original fixes */
  .vnpay-button {
    border: 2px solid currentColor;
    background-color: ButtonFace;
    color: ButtonText;
  }

  .vnpay-input {
    border: 1px solid currentColor;
    background-color: Field;
    color: FieldText;
  }
}

/* Fix for VNPay container */
.vnpay-container *,
[class*="vnpay-container"] *,
[id*="vnpay-container"] *,
.vnp-container *,
[class*="vnp-container"] *,
[id*="vnp-container"] * {
  box-sizing: border-box;
}

/* Fix for VNPay iframe */
.vnpay-iframe,
iframe[src*="vnpay"],
iframe[src*="vnp"] {
  width: 100%;
  height: 600px;
  border: none;
  max-width: 100%;
}

/* Fix for VNPay form elements */
.vnpay-form input,
.vnpay-form select,
.vnpay-form textarea,
.vnp-form input,
.vnp-form select,
.vnp-form textarea {
  box-sizing: border-box;
  max-width: 100%;
}

/* Fix for VNPay buttons */
.vnpay-button,
.vnp-button,
button[class*="vnpay-"],
button[class*="vnp-"] {
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s;
}

/* Fix for VNPay error messages */
.vnpay-error,
.vnp-error,
[class*="vnpay-error"],
[class*="vnp-error"] {
  color: #d32f2f;
  margin: 5px 0;
  font-size: 0.875rem;
}
