<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Real-time Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .step h3 { margin: 0 0 10px 0; color: #333; }
        button { padding: 12px 24px; margin: 10px; font-size: 16px; cursor: pointer; border: none; border-radius: 4px; }
        .btn-success { background: #28a745; color: white; }
        .btn-primary { background: #007bff; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Final Real-time Test</h1>
        <p>Test hoàn chỉnh tính năng real-time update cho tin nổi bật hết hạn</p>
        
        <div class="step">
            <h3>Bước 1: Tạo tin nổi bật test (30 giây)</h3>
            <button class="btn-success" onclick="createQuickTest()">Tạo Test Highlight (30s)</button>
            <div id="step1-result" class="result"></div>
        </div>

        <div class="step">
            <h3>Bước 2: Mở HomePage để quan sát</h3>
            <button class="btn-primary" onclick="openHomePage()">Mở HomePage</button>
            <div class="result info">
                Mở Developer Console để xem logs real-time
            </div>
        </div>

        <div class="step">
            <h3>Bước 3: Trigger ngay lập tức (không chờ)</h3>
            <button class="btn-warning" onclick="triggerExpiry()">Chạy Expiry Job Ngay</button>
            <div id="step3-result" class="result"></div>
        </div>

        <div class="step">
            <h3>Bước 4: Kiểm tra kết quả</h3>
            <div class="result info">
                <strong>Expected behavior:</strong><br>
                ✅ Tin biến mất khỏi "Phòng trọ nổi bật"<br>
                ✅ Tin xuất hiện ở đầu "PHÒNG TRỌ MỚI NHẤT"<br>
                ✅ Toast notification hiển thị<br>
                ✅ Không cần refresh trang
            </div>
        </div>
    </div>

    <script>
        let testRoomId = null;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
            console.log(`[FinalTest] ${message}`);
        }

        async function createQuickTest() {
            try {
                log('step1-result', '🔄 Đang tạo test highlight...', 'info');
                
                // Get first room
                const roomsResponse = await fetch('http://localhost:5000/api/rooms?limit=1');
                const roomsData = await roomsResponse.json();
                
                if (!roomsData.success || roomsData.data.rooms.length === 0) {
                    throw new Error('Không tìm thấy phòng nào');
                }
                
                testRoomId = roomsData.data.rooms[0]._id;
                const roomTitle = roomsData.data.rooms[0].title;
                
                // Create test highlight with 30 seconds
                const response = await fetch('http://localhost:5000/api/test/create-test-highlight', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ roomId: testRoomId, minutes: 0.5 }) // 30 seconds
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log('step1-result', `✅ Test highlight created!<br>📝 Room: "${roomTitle}"<br>⏰ Expires in 30 seconds<br>🆔 Room ID: ${testRoomId}`, 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log('step1-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        function openHomePage() {
            window.open('http://localhost:3000', '_blank');
            log('step2-result', '✅ HomePage opened in new tab', 'success');
        }

        async function triggerExpiry() {
            try {
                log('step3-result', '🔄 Triggering expiry job...', 'info');
                
                const response = await fetch('http://localhost:5000/api/test/run-highlight-expiry-job', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log('step3-result', `✅ Expiry job completed!<br>📊 Processed: ${data.data.processed} rooms<br>⏱️ Duration: ${data.data.duration}ms<br>🔍 Check HomePage for real-time update`, 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log('step3-result', `❌ Error: ${error.message}`, 'error');
            }
        }

        // Auto-instructions
        window.onload = () => {
            console.log('[FinalTest] 🎯 Final Real-time Test Ready');
            console.log('[FinalTest] 📋 Instructions:');
            console.log('[FinalTest] 1. Click "Tạo Test Highlight (30s)"');
            console.log('[FinalTest] 2. Click "Mở HomePage" and open Developer Console');
            console.log('[FinalTest] 3. Click "Chạy Expiry Job Ngay" to trigger immediately');
            console.log('[FinalTest] 4. Watch HomePage for real-time transition');
        };
    </script>
</body>
</html>
