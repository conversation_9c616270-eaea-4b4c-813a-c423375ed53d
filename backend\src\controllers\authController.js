const User = require('../models/User');
const { generateToken } = require('../utils/jwtUtils');
const { generateVerificationToken, sendVerificationEmail, sendPasswordResetEmail } = require('../utils/emailUtils');

// @desc    Đăng ký người dùng mới
// @route   POST /api/auth/register
// @access  Public
const register = async (req, res) => {
  try {
    const { email, password, fullName, phone } = req.body;

    // Kiểm tra email đã tồn tại chưa
    const userExists = await User.findOne({ email });
    if (userExists) {
      return res.status(400).json({
        success: false,
        message: 'Email đã được sử dụng'
      });
    }

    // Tạo token xác nhận email
    const { token, expires } = await generateVerificationToken();

    // Tạo người dùng mới
    const user = await User.create({
      email,
      password,
      fullName,
      phone,
      emailVerificationToken: token,
      emailVerificationExpires: expires,
      isEmailVerified: false
    });

    if (user) {
      try {
        // Tạo URL xác nhận email
        const verificationUrl = `${process.env.BACKEND_URL}/api/auth/verify-email/${token}`;

        // Gửi email xác nhận
        const emailResult = await sendVerificationEmail(user, verificationUrl);

        if (!emailResult.success) {
          console.error('Lỗi gửi email:', emailResult.error);

          // Vẫn trả về thành công nhưng thông báo về vấn đề gửi email
          return res.status(201).json({
            success: true,
            data: {
              _id: user._id,
              fullName: user.fullName,
              email: user.email,
              isEmailVerified: false,
              message: 'Đăng ký thành công nhưng không thể gửi email xác nhận. Vui lòng sử dụng chức năng gửi lại email xác nhận.'
            },
            emailError: process.env.NODE_ENV === 'development' ? emailResult.error : 'Không thể gửi email xác nhận'
          });
        }

        // Trả về thông tin người dùng (không bao gồm token JWT vì chưa xác nhận email)
        return res.status(201).json({
          success: true,
          data: {
            _id: user._id,
            fullName: user.fullName,
            email: user.email,
            isEmailVerified: false,
            message: 'Đăng ký thành công. Vui lòng kiểm tra email để xác nhận tài khoản.'
          },
          emailPreview: process.env.NODE_ENV === 'development' ? emailResult.previewUrl : undefined
        });
      } catch (emailError) {
        console.error('Lỗi gửi email:', emailError);

        // Vẫn trả về thành công nhưng thông báo về vấn đề gửi email
        return res.status(201).json({
          success: true,
          data: {
            _id: user._id,
            fullName: user.fullName,
            email: user.email,
            isEmailVerified: false,
            message: 'Đăng ký thành công nhưng không thể gửi email xác nhận. Vui lòng sử dụng chức năng gửi lại email xác nhận.'
          },
          emailError: process.env.NODE_ENV === 'development' ? emailError.message : 'Không thể gửi email xác nhận'
        });
      }
    }
  } catch (error) {
    console.error('Lỗi đăng ký:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Đăng nhập
// @route   POST /api/auth/login
// @access  Public
const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Kiểm tra email và password
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Vui lòng nhập email và mật khẩu'
      });
    }

    // Tìm người dùng theo email
    const user = await User.findOne({ email });

    // Kiểm tra người dùng và mật khẩu
    if (!user || !(await user.matchPassword(password))) {
      return res.status(401).json({
        success: false,
        message: 'Email hoặc mật khẩu không đúng'
      });
    }

    // Kiểm tra tài khoản có bị khóa không
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Tài khoản đã bị khóa'
      });
    }

    // Kiểm tra email đã được xác nhận chưa
    if (!user.isEmailVerified) {
      return res.status(401).json({
        success: false,
        message: 'Vui lòng xác nhận email trước khi đăng nhập',
        isEmailVerified: false
      });
    }

    // Cập nhật thời gian đăng nhập cuối
    user.lastLogin = Date.now();
    await user.save();

    res.status(200).json({
      success: true,
      data: {
        _id: user._id,
        fullName: user.fullName,
        email: user.email,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        token: generateToken(user._id)
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy thông tin người dùng hiện tại
// @route   GET /api/auth/me
// @access  Private
const getMe = async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('-password');

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Cập nhật thông tin người dùng
// @route   PUT /api/auth/me
// @access  Private
const updateMe = async (req, res) => {
  try {
    const { fullName, phone, address } = req.body;

    // Tìm và cập nhật người dùng
    const user = await User.findById(req.user._id);

    if (user) {
      user.fullName = fullName || user.fullName;
      user.phone = phone || user.phone;

      if (address) {
        user.address = {
          street: address.street || user.address?.street,
          ward: address.ward || user.address?.ward,
          district: address.district || user.address?.district,
          city: address.city || user.address?.city
        };
      }

      // Lưu thay đổi
      const updatedUser = await user.save();

      res.status(200).json({
        success: true,
        data: {
          _id: updatedUser._id,
          fullName: updatedUser.fullName,
          email: updatedUser.email,
          phone: updatedUser.phone,
          address: updatedUser.address,
          role: updatedUser.role
        }
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng'
      });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Đổi mật khẩu
// @route   PUT /api/auth/change-password
// @access  Private
const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Vui lòng nhập mật khẩu hiện tại và mật khẩu mới'
      });
    }

    // Tìm người dùng
    const user = await User.findById(req.user._id);

    // Kiểm tra mật khẩu hiện tại
    if (!(await user.matchPassword(currentPassword))) {
      return res.status(401).json({
        success: false,
        message: 'Mật khẩu hiện tại không đúng'
      });
    }

    // Cập nhật mật khẩu mới
    user.password = newPassword;
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Đổi mật khẩu thành công'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Xác nhận email
// @route   GET /api/auth/verify-email/:token
// @access  Public
const verifyEmail = async (req, res) => {
  try {
    const { token } = req.params;

    // Tìm người dùng với token xác nhận email
    const user = await User.findOne({
      emailVerificationToken: token,
      emailVerificationExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Token xác nhận không hợp lệ hoặc đã hết hạn'
      });
    }

    // Cập nhật trạng thái xác nhận email
    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    await user.save();

    // Chuyển hướng đến trang frontend
    const redirectUrl = `${process.env.FRONTEND_URL}/login?verified=true`;
    res.redirect(redirectUrl);
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Gửi lại email xác nhận
// @route   POST /api/auth/resend-verification
// @access  Public
const resendVerification = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Vui lòng cung cấp địa chỉ email'
      });
    }

    // Tìm người dùng theo email
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng với email này'
      });
    }

    // Kiểm tra nếu email đã được xác nhận
    if (user.isEmailVerified) {
      return res.status(400).json({
        success: false,
        message: 'Email này đã được xác nhận'
      });
    }

    // Tạo token xác nhận email mới
    const { token, expires } = await generateVerificationToken();

    // Cập nhật token xác nhận email
    user.emailVerificationToken = token;
    user.emailVerificationExpires = expires;
    await user.save();

    try {
      // Tạo URL xác nhận email
      const verificationUrl = `${process.env.BACKEND_URL}/api/auth/verify-email/${token}`;

      // Gửi email xác nhận
      const emailResult = await sendVerificationEmail(user, verificationUrl);

      if (!emailResult.success) {
        console.error('Lỗi gửi email xác nhận:', emailResult.error);
        return res.status(500).json({
          success: false,
          message: 'Không thể gửi email xác nhận. Vui lòng thử lại sau.',
          error: process.env.NODE_ENV === 'development' ? emailResult.error : undefined
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Email xác nhận đã được gửi lại. Vui lòng kiểm tra hộp thư của bạn.',
        emailPreview: process.env.NODE_ENV === 'development' ? emailResult.previewUrl : undefined
      });
    } catch (emailError) {
      console.error('Lỗi gửi email xác nhận:', emailError);
      return res.status(500).json({
        success: false,
        message: 'Không thể gửi email xác nhận. Vui lòng thử lại sau.',
        error: process.env.NODE_ENV === 'development' ? emailError.message : undefined
      });
    }
  } catch (error) {
    console.error('Lỗi gửi lại email xác nhận:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Đăng xuất
// @route   POST /api/auth/logout
// @access  Private
const logout = async (req, res) => {
  try {
    // Không cần xử lý gì ở phía server vì token JWT được lưu ở client
    // Client sẽ xóa token khỏi localStorage hoặc cookie

    res.status(200).json({
      success: true,
      message: 'Đăng xuất thành công'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Quên mật khẩu - Gửi email reset
// @route   POST /api/auth/forgot-password
// @access  Public
const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Vui lòng cung cấp địa chỉ email'
      });
    }

    // Tìm người dùng theo email
    const user = await User.findOne({ email });

    if (!user) {
      // Không tiết lộ thông tin email có tồn tại hay không vì lý do bảo mật
      return res.status(200).json({
        success: true,
        message: 'Nếu email này tồn tại trong hệ thống, bạn sẽ nhận được email hướng dẫn reset mật khẩu.'
      });
    }

    // Tạo reset token
    const { token, expires } = await generateVerificationToken();

    // Lưu token vào database
    user.resetPasswordToken = token;
    user.resetPasswordExpires = expires;
    await user.save();

    try {
      // Tạo URL reset mật khẩu
      const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password/${token}`;

      // Gửi email reset mật khẩu
      const emailResult = await sendPasswordResetEmail(user, resetUrl);

      if (!emailResult.success) {
        console.error('Lỗi gửi email reset mật khẩu:', emailResult.error);

        // Xóa token nếu không gửi được email
        user.resetPasswordToken = undefined;
        user.resetPasswordExpires = undefined;
        await user.save();

        return res.status(500).json({
          success: false,
          message: 'Không thể gửi email reset mật khẩu. Vui lòng thử lại sau.',
          error: process.env.NODE_ENV === 'development' ? emailResult.error : undefined
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Email hướng dẫn reset mật khẩu đã được gửi. Vui lòng kiểm tra hộp thư của bạn.',
        emailPreview: process.env.NODE_ENV === 'development' ? emailResult.previewUrl : undefined
      });
    } catch (emailError) {
      console.error('Lỗi gửi email reset mật khẩu:', emailError);

      // Xóa token nếu không gửi được email
      user.resetPasswordToken = undefined;
      user.resetPasswordExpires = undefined;
      await user.save();

      return res.status(500).json({
        success: false,
        message: 'Không thể gửi email reset mật khẩu. Vui lòng thử lại sau.',
        error: process.env.NODE_ENV === 'development' ? emailError.message : undefined
      });
    }
  } catch (error) {
    console.error('Lỗi quên mật khẩu:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Verify reset token
// @route   GET /api/auth/verify-reset-token/:token
// @access  Public
const verifyResetToken = async (req, res) => {
  try {
    const { token } = req.params;

    // Tìm người dùng với token reset mật khẩu
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Token reset mật khẩu không hợp lệ hoặc đã hết hạn'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Token hợp lệ',
      data: {
        email: user.email
      }
    });
  } catch (error) {
    console.error('Lỗi verify reset token:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Reset mật khẩu
// @route   POST /api/auth/reset-password
// @access  Public
const resetPassword = async (req, res) => {
  try {
    const { token, password } = req.body;

    if (!token || !password) {
      return res.status(400).json({
        success: false,
        message: 'Vui lòng cung cấp token và mật khẩu mới'
      });
    }

    // Validate password length
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Mật khẩu phải có ít nhất 6 ký tự'
      });
    }

    // Tìm người dùng với token reset mật khẩu
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Token reset mật khẩu không hợp lệ hoặc đã hết hạn'
      });
    }

    // Cập nhật mật khẩu mới
    user.password = password;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Mật khẩu đã được reset thành công. Bạn có thể đăng nhập với mật khẩu mới.'
    });
  } catch (error) {
    console.error('Lỗi reset mật khẩu:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  register,
  login,
  logout,
  getMe,
  updateMe,
  changePassword,
  verifyEmail,
  resendVerification,
  forgotPassword,
  verifyResetToken,
  resetPassword
};
