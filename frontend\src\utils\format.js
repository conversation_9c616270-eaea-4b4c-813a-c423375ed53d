/**
 * Định dạng số tiền thành chuỗi có dấu phân cách hàng nghìn
 * @param {number} amount - Số tiền cần định dạng
 * @returns {string} Chuỗi đã định dạng
 */
export const formatCurrency = (amount) => {
  if (!amount && amount !== 0) return '';
  
  return new Intl.NumberFormat('vi-VN', {
    style: 'decimal',
    maximumFractionDigits: 0,
  }).format(amount);
};

/**
 * Định dạng ngày tháng thành chuỗi
 * @param {string|Date} date - Ngày cần định dạng
 * @returns {string} Chuỗi đã định dạng
 */
export const formatDate = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  // Kiểm tra xem dateObj có phải là ngày hợp lệ không
  if (isNaN(dateObj.getTime())) return '';
  
  // Lấy ngày hiện tại
  const now = new Date();
  
  // T<PERSON>h khoảng cách thời gian
  const diffTime = Math.abs(now - dateObj);
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  // Nếu là hôm nay
  if (
    dateObj.getDate() === now.getDate() &&
    dateObj.getMonth() === now.getMonth() &&
    dateObj.getFullYear() === now.getFullYear()
  ) {
    return 'Hôm nay';
  }
  
  // Nếu là hôm qua
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  if (
    dateObj.getDate() === yesterday.getDate() &&
    dateObj.getMonth() === yesterday.getMonth() &&
    dateObj.getFullYear() === yesterday.getFullYear()
  ) {
    return 'Hôm qua';
  }
  
  // Nếu trong vòng 7 ngày
  if (diffDays < 7) {
    return `${diffDays} ngày trước`;
  }
  
  // Định dạng ngày tháng đầy đủ
  return new Intl.DateTimeFormat('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(dateObj);
};

/**
 * Rút gọn văn bản nếu quá dài
 * @param {string} text - Văn bản cần rút gọn
 * @param {number} maxLength - Độ dài tối đa
 * @returns {string} Văn bản đã rút gọn
 */
export const truncateText = (text, maxLength = 100) => {
  if (!text) return '';
  
  if (text.length <= maxLength) return text;
  
  return text.substring(0, maxLength) + '...';
};

/**
 * Chuyển đổi chuỗi thành slug
 * @param {string} text - Chuỗi cần chuyển đổi
 * @returns {string} Slug
 */
export const slugify = (text) => {
  if (!text) return '';
  
  // Chuyển về chữ thường và thay thế dấu tiếng Việt
  const slug = text
    .toLowerCase()
    .normalize('NFD') // Tách dấu thành các ký tự riêng biệt
    .replace(/[\u0300-\u036f]/g, '') // Loại bỏ các dấu
    .replace(/[đĐ]/g, 'd') // Thay thế đ/Đ thành d
    .replace(/[^a-z0-9\s-]/g, '') // Loại bỏ các ký tự không phải chữ cái, số, khoảng trắng hoặc dấu gạch ngang
    .replace(/\s+/g, '-') // Thay thế khoảng trắng bằng dấu gạch ngang
    .replace(/-+/g, '-') // Loại bỏ các dấu gạch ngang liên tiếp
    .trim(); // Loại bỏ khoảng trắng ở đầu và cuối
  
  return slug;
};
