# TÍNH NĂNG TỰ ĐỘNG HẠ CẤP TIN NỔI BẬT

## Tổng quan

Tính năng tự động hạ cấp tin nổi bật cho phép hệ thống tự động chuyển các tin đăng nổi bật về tin thường sau 5 phút kể từ khi thanh toán thành công. Điều này giúp đảm bảo tính công bằng và tạo động lực cho người dùng tiếp tục sử dụng dịch vụ.

## Cách thức hoạt động

### 1. Luồng thanh toán và thiết lập thời gian hết hạn

Khi người dùng thanh toán thành công gói tin nổi bật:

```javascript
// Trong transactionController.js - VNPay return handler
case 'special':
  room.isHighlighted = true; // Nâng cấp lên gói đặc biệt
  // Set thời gian hết hạn sau 5 phút
  room.highlightExpiry = new Date(Date.now() + 5 * 60 * 1000);
  break;
```

### 2. <PERSON>ron Job tự động kiểm tra và hạ cấp

Hệ thống sử dụng `node-cron` để chạy job mỗi phút:

```javascript
// Trong scheduledService.js
const job = cron.schedule('* * * * *', async () => {
  console.log('[ScheduledService] Cron job đang chạy...', new Date().toISOString());
  try {
    await this.processExpiredHighlights();
  } catch (error) {
    console.error('[ScheduledService] Lỗi khi xử lý tin nổi bật hết hạn:', error);
  }
});
```

### 3. Logic xử lý hạ cấp

```javascript
// Tìm các tin nổi bật đã hết hạn
const expiredRooms = await Room.find({
  isHighlighted: true,
  highlightExpiry: { $lte: now }
});

// Cập nhật hàng loạt
const result = await Room.updateMany(
  {
    isHighlighted: true,
    highlightExpiry: { $lte: now }
  },
  {
    $set: { isHighlighted: false },
    $unset: { highlightExpiry: 1 }
  }
);
```

## Cấu trúc dữ liệu

### Room Model

```javascript
// Thêm trường mới vào Room schema
highlightExpiry: {
  type: Date,
  default: null
}
```

### Trạng thái tin đăng

- `isHighlighted: true` + `highlightExpiry: Date` = Tin nổi bật có thời hạn
- `isHighlighted: false` + `highlightExpiry: null` = Tin thường
- Khi hết hạn: `isHighlighted` chuyển về `false`, `highlightExpiry` bị xóa

## Giao diện người dùng

### CountdownTimer Component

```jsx
<CountdownTimer 
  expiryDate={room.highlightExpiry}
  compact={true}
  showIcon={true}
  className="text-xs"
/>
```

### Hiển thị trong RoomCard

- Badge "Tin nổi bật" với icon sao
- Countdown timer hiển thị thời gian còn lại
- Tự động cập nhật mỗi giây

### Hiển thị trong RoomDetail

- Thông tin gói tin hiện tại
- Countdown timer chi tiết
- Cập nhật real-time

## API Endpoints

### Kiểm tra trạng thái scheduled jobs

```
GET /api/scheduled-jobs/status
```

Response:
```json
{
  "success": true,
  "message": "Trạng thái scheduled jobs",
  "data": {
    "jobs": [
      {
        "name": "highlightExpiry",
        "description": "Kiểm tra và hạ cấp tin nổi bật hết hạn",
        "running": true,
        "scheduled": true
      }
    ],
    "serverTime": "2025-07-20T06:51:00.020Z",
    "uptime": 123.456
  }
}
```

### Test thủ công job hạ cấp

```
GET /api/test-highlight-expiry
```

Response:
```json
{
  "success": true,
  "message": "Đã chạy thủ công job hạ cấp tin nổi bật",
  "result": {
    "processed": 2,
    "found": 2,
    "duration": 45
  },
  "timestamp": "2025-07-20T06:51:30.123Z"
}
```

## Logging và Monitoring

### Log Messages

```
[ScheduledService] Cron job đang chạy... 2025-07-20T06:51:00.020Z
[ScheduledService] Tìm thấy 2 tin nổi bật hết hạn
[ScheduledService] - "Phòng trọ cao cấp" (60d0fe4f5311236168a109cb) - Hết hạn 2 phút trước
[ScheduledService] Đã hạ cấp 2/2 tin nổi bật trong 45ms
```

### Performance Monitoring

- Thời gian xử lý mỗi lần chạy job
- Số lượng tin được xử lý
- Thống kê lỗi và cảnh báo

## Cấu hình và Triển khai

### Dependencies

```json
{
  "node-cron": "^3.0.3"
}
```

### Environment Variables

```env
# Timezone cho cron job (tùy chọn)
TZ=Asia/Ho_Chi_Minh
```

### Khởi động Service

```javascript
// Trong server.js
const scheduledService = require('./services/scheduledService');

app.listen(PORT, () => {
  console.log(`Server đang chạy trên cổng ${PORT}`);
  
  // Khởi tạo và bắt đầu scheduled services
  try {
    scheduledService.init();
    scheduledService.startAll();
    console.log('✓ Scheduled services đã được khởi động thành công');
  } catch (error) {
    console.error('✗ Lỗi khởi động scheduled services:', error);
  }
});
```

## Tối ưu hóa và Bảo trì

### Performance

- Sử dụng `updateMany()` để cập nhật hàng loạt
- Index trên trường `isHighlighted` và `highlightExpiry`
- Chỉ log khi có tin hết hạn để tránh spam

### Error Handling

- Try-catch cho tất cả operations
- Logging chi tiết lỗi database
- Graceful degradation khi có lỗi

### Monitoring

- API endpoint kiểm tra trạng thái jobs
- Thống kê performance và lỗi
- Alert khi job không chạy

## Lưu ý quan trọng

1. **Thời gian**: Hiện tại set 5 phút, có thể điều chỉnh trong `transactionController.js`
2. **Timezone**: Cron job chạy theo timezone server
3. **Database**: Cần index cho performance tốt
4. **Monitoring**: Nên theo dõi logs để đảm bảo job chạy đúng
5. **Backup**: Nên backup dữ liệu trước khi triển khai

## Troubleshooting

### Job không chạy

1. Kiểm tra logs server khi khởi động
2. Gọi API `/api/scheduled-jobs/status` để kiểm tra trạng thái
3. Test thủ công với `/api/test-highlight-expiry`

### Performance chậm

1. Kiểm tra số lượng tin nổi bật trong database
2. Tạo index cho `isHighlighted` và `highlightExpiry`
3. Tối ưu query trong `processExpiredHighlights()`

### Lỗi database

1. Kiểm tra kết nối MongoDB
2. Xem logs chi tiết lỗi
3. Kiểm tra quyền truy cập collection
