/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - email
 *         - password
 *         - fullName
 *       properties:
 *         _id:
 *           type: string
 *           description: ID của người dùng
 *         email:
 *           type: string
 *           format: email
 *           description: Email của người dùng
 *         fullName:
 *           type: string
 *           description: Họ tên đầy đủ của người dùng
 *         phone:
 *           type: string
 *           description: Số điện thoại của người dùng
 *         avatar:
 *           type: string
 *           description: URL hình đại diện
 *         role:
 *           type: string
 *           enum: [user, admin]
 *           description: <PERSON>ai trò của người dùng
 *         address:
 *           type: object
 *           properties:
 *             street:
 *               type: string
 *             ward:
 *               type: string
 *             district:
 *               type: string
 *             city:
 *               type: string
 *         isActive:
 *           type: boolean
 *           description: Trạng thái hoạt động của tài khoản
 *         isEmailVerified:
 *           type: boolean
 *           description: Tr<PERSON><PERSON> thái xá<PERSON> nhận email
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian tạo tà<PERSON>
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Thờ<PERSON> gian cập nhật tài khoản gần nhất
 *       example:
 *         _id: 60d0fe4f5311236168a109ca
 *         email: <EMAIL>
 *         fullName: Nguyễn Văn A
 *         phone: "0123456789"
 *         avatar: ""
 *         role: user
 *         address:
 *           street: 123 Đường ABC
 *           ward: Phường XYZ
 *           district: Quận 1
 *           city: TP. Hồ Chí Minh
 *         isActive: true
 *         isEmailVerified: true
 *         createdAt: "2023-01-01T00:00:00.000Z"
 *         updatedAt: "2023-01-01T00:00:00.000Z"
 *     
 *     Room:
 *       type: object
 *       required:
 *         - title
 *         - description
 *         - price
 *         - area
 *         - address
 *         - category
 *         - user
 *       properties:
 *         _id:
 *           type: string
 *           description: ID của phòng trọ
 *         title:
 *           type: string
 *           description: Tiêu đề tin đăng
 *         description:
 *           type: string
 *           description: Mô tả chi tiết
 *         price:
 *           type: number
 *           description: Giá thuê (VNĐ/tháng)
 *         area:
 *           type: number
 *           description: Diện tích (m²)
 *         deposit:
 *           type: number
 *           description: Tiền cọc (VNĐ)
 *         address:
 *           type: object
 *           properties:
 *             street:
 *               type: string
 *             ward:
 *               type: string
 *             district:
 *               type: string
 *             city:
 *               type: string
 *             location:
 *               type: object
 *               properties:
 *                 type:
 *                   type: string
 *                   enum: [Point]
 *                 coordinates:
 *                   type: array
 *                   items:
 *                     type: number
 *         images:
 *           type: array
 *           items:
 *             type: string
 *           description: Danh sách URL hình ảnh
 *         amenities:
 *           type: array
 *           items:
 *             type: string
 *           description: Danh sách ID tiện nghi
 *         category:
 *           type: string
 *           description: ID loại phòng
 *         user:
 *           type: string
 *           description: ID người đăng tin
 *         status:
 *           type: string
 *           enum: [available, rented, hidden]
 *           description: Trạng thái phòng
 *         isHighlighted:
 *           type: boolean
 *           description: Tin đăng nổi bật
 *         highlightExpiry:
 *           type: string
 *           format: date-time
 *           description: Thời gian hết hạn tin nổi bật (null nếu không có hạn)
 *         views:
 *           type: number
 *           description: Số lượt xem
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian đăng tin
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian cập nhật tin gần nhất
 *       example:
 *         _id: 60d0fe4f5311236168a109cb
 *         title: Phòng trọ cao cấp quận 7
 *         description: Phòng trọ rộng rãi, thoáng mát, đầy đủ tiện nghi
 *         price: 3500000
 *         area: 25
 *         deposit: 3500000
 *         address:
 *           street: 123 Nguyễn Văn Linh
 *           ward: Phường Tân Thuận Đông
 *           district: Quận 7
 *           city: TP. Hồ Chí Minh
 *           location:
 *             type: Point
 *             coordinates: [106.7017555, 10.7575337]
 *         images: ["image1.jpg", "image2.jpg"]
 *         amenities: ["60d0fe4f5311236168a109cc", "60d0fe4f5311236168a109cd"]
 *         category: "60d0fe4f5311236168a109ce"
 *         user: "60d0fe4f5311236168a109ca"
 *         status: available
 *         isHighlighted: true
 *         highlightExpiry: "2023-02-01T00:00:00.000Z"
 *         views: 150
 *         createdAt: "2023-01-01T00:00:00.000Z"
 *         updatedAt: "2023-01-01T00:00:00.000Z"
 *     
 *     Transaction:
 *       type: object
 *       required:
 *         - user
 *         - room
 *         - amount
 *         - packageType
 *       properties:
 *         _id:
 *           type: string
 *           description: ID của giao dịch
 *         user:
 *           type: string
 *           description: ID người dùng thực hiện giao dịch
 *         room:
 *           type: string
 *           description: ID phòng trọ được thanh toán
 *         amount:
 *           type: number
 *           description: Số tiền thanh toán (VNĐ)
 *         packageType:
 *           type: string
 *           enum: [1_week, 2_weeks, 1_month]
 *           description: Loại gói tin nổi bật
 *         vnpayTxnRef:
 *           type: string
 *           description: Mã tham chiếu giao dịch VNPay
 *         vnpayTxnNo:
 *           type: string
 *           description: Mã giao dịch VNPay
 *         vnpayResponseCode:
 *           type: string
 *           description: Mã phản hồi từ VNPay
 *         status:
 *           type: string
 *           enum: [pending, completed, failed]
 *           description: Trạng thái giao dịch
 *         completedAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian hoàn thành giao dịch
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian tạo giao dịch
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian cập nhật giao dịch gần nhất
 *       example:
 *         _id: 60d0fe4f5311236168a109cf
 *         user: "60d0fe4f5311236168a109ca"
 *         room: "60d0fe4f5311236168a109cb"
 *         amount: 150000
 *         packageType: 1_month
 *         vnpayTxnRef: "20230101000000-60d0fe4f"
 *         vnpayTxnNo: "13708044"
 *         vnpayResponseCode: "00"
 *         status: completed
 *         completedAt: "2023-01-01T00:10:00.000Z"
 *         createdAt: "2023-01-01T00:00:00.000Z"
 *         updatedAt: "2023-01-01T00:10:00.000Z"
 *     
 *     Amenity:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         _id:
 *           type: string
 *           description: ID của tiện nghi
 *         name:
 *           type: string
 *           description: Tên tiện nghi

 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian tạo
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian cập nhật gần nhất
 *       example:
 *         _id: 60d0fe4f5311236168a109cc
 *         name: Điều hòa
 *         
 *         createdAt: "2023-01-01T00:00:00.000Z"
 *         updatedAt: "2023-01-01T00:00:00.000Z"
 *     
 *     Category:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         _id:
 *           type: string
 *           description: ID của loại phòng
 *         name:
 *           type: string
 *           description: Tên loại phòng
 *         description:
 *           type: string
 *           description: Mô tả loại phòng
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian tạo
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian cập nhật gần nhất
 *       example:
 *         _id: 60d0fe4f5311236168a109ce
 *         name: Phòng trọ
 *         description: Phòng cho thuê trong nhà trọ
 *         createdAt: "2023-01-01T00:00:00.000Z"
 *         updatedAt: "2023-01-01T00:00:00.000Z"
 */
