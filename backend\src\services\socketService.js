const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const User = require('../models/User');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId mapping
  }

  // Khởi tạo Socket.IO server
  init(httpServer) {
    this.io = new Server(httpServer, {
      cors: {
        origin: [
          'http://localhost:3000',
          'http://localhost:5173',
          'http://127.0.0.1:3000',
          'https://timphongtro.vercel.app'
        ],
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupEventHandlers();
    console.log('[SocketService] Socket.IO server đã được khởi tạo');
  }

  // Thiết lập event handlers
  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`[SocketService] Client kết nối: ${socket.id}`);

      // <PERSON><PERSON> lý authentication
      socket.on('authenticate', async (token) => {
        try {
          await this.authenticateSocket(socket, token);
        } catch (error) {
          console.error('[SocketService] Lỗi authentication:', error);
          socket.emit('auth_error', { message: 'Authentication failed' });
        }
      });

      // Xử lý join room cho specific user
      socket.on('join_user_room', (userId) => {
        if (socket.userId) {
          socket.join(`user_${userId}`);
          console.log(`[SocketService] User ${userId} joined room user_${userId}`);
        }
      });

      // Xử lý disconnect
      socket.on('disconnect', () => {
        console.log(`[SocketService] Client ngắt kết nối: ${socket.id}`);
        if (socket.userId) {
          this.connectedUsers.delete(socket.userId);
          console.log(`[SocketService] Removed user ${socket.userId} from connected users`);
        }
      });

      // Ping/Pong để maintain connection
      socket.on('ping', () => {
        socket.emit('pong');
      });
    });
  }

  // Xác thực socket connection
  async authenticateSocket(socket, token) {
    try {
      if (!token) {
        throw new Error('No token provided');
      }

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id).select('-password');

      if (!user) {
        throw new Error('User not found');
      }

      // Lưu thông tin user vào socket
      socket.userId = user._id.toString();
      socket.userEmail = user.email;
      socket.userFullName = user.fullName;

      // Lưu mapping userId -> socketId
      this.connectedUsers.set(socket.userId, socket.id);

      // Join user-specific room
      socket.join(`user_${socket.userId}`);

      // Emit authentication success
      socket.emit('authenticated', {
        userId: socket.userId,
        fullName: socket.userFullName,
        email: socket.userEmail
      });

      console.log(`[SocketService] User authenticated: ${socket.userFullName} (${socket.userId})`);

    } catch (error) {
      console.error('[SocketService] Authentication error:', error);
      socket.emit('auth_error', { message: error.message });
      throw error;
    }
  }

  // Emit event khi tin nổi bật hết hạn
  emitHighlightExpired(roomData) {
    if (!this.io) {
      console.warn('[SocketService] Socket.IO chưa được khởi tạo');
      return;
    }

    try {
      const { roomId, title, userId, userEmail, userFullName } = roomData;

      // Emit đến user cụ thể (chủ trọ)
      this.io.to(`user_${userId}`).emit('highlight_expired', {
        roomId,
        title,
        message: `Tin đăng "${title}" đã hết hạn gói tin nổi bật`,
        timestamp: new Date().toISOString()
      });

      // Emit đến tất cả clients để cập nhật UI (nếu đang xem room này)
      this.io.emit('room_highlight_updated', {
        roomId,
        isHighlighted: false,
        highlightExpiry: null,
        timestamp: new Date().toISOString()
      });

      console.log(`[SocketService] Emitted highlight_expired for room ${roomId} to user ${userId}`);

    } catch (error) {
      console.error('[SocketService] Lỗi khi emit highlight_expired:', error);
    }
  }

  // Emit event khi có tin nổi bật mới
  emitHighlightActivated(roomData) {
    if (!this.io) {
      console.warn('[SocketService] Socket.IO chưa được khởi tạo');
      return;
    }

    try {
      const { roomId, title, userId, highlightExpiry } = roomData;

      // Emit đến user cụ thể (chủ trọ)
      this.io.to(`user_${userId}`).emit('highlight_activated', {
        roomId,
        title,
        highlightExpiry,
        message: `Tin đăng "${title}" đã được nâng cấp lên gói tin nổi bật`,
        timestamp: new Date().toISOString()
      });

      // Emit đến tất cả clients để cập nhật UI
      this.io.emit('room_highlight_updated', {
        roomId,
        isHighlighted: true,
        highlightExpiry,
        timestamp: new Date().toISOString()
      });

      console.log(`[SocketService] Emitted highlight_activated for room ${roomId} to user ${userId}`);

    } catch (error) {
      console.error('[SocketService] Lỗi khi emit highlight_activated:', error);
    }
  }

  // Lấy số lượng users đang kết nối
  getConnectedUsersCount() {
    return this.connectedUsers.size;
  }

  // Lấy danh sách users đang kết nối
  getConnectedUsers() {
    return Array.from(this.connectedUsers.keys());
  }

  // Kiểm tra user có đang online không
  isUserOnline(userId) {
    return this.connectedUsers.has(userId);
  }

  // Emit event đến user cụ thể
  emitToUser(userId, event, data) {
    if (!this.io) {
      console.warn('[SocketService] Socket.IO chưa được khởi tạo');
      return false;
    }

    try {
      this.io.to(`user_${userId}`).emit(event, data);
      console.log(`[SocketService] Emitted ${event} to user ${userId}`);
      return true;
    } catch (error) {
      console.error(`[SocketService] Lỗi khi emit ${event} to user ${userId}:`, error);
      return false;
    }
  }

  // Emit event đến tất cả clients
  emitToAll(event, data) {
    if (!this.io) {
      console.warn('[SocketService] Socket.IO chưa được khởi tạo');
      return false;
    }

    try {
      this.io.emit(event, data);
      console.log(`[SocketService] Emitted ${event} to all clients`);
      return true;
    } catch (error) {
      console.error(`[SocketService] Lỗi khi emit ${event} to all:`, error);
      return false;
    }
  }
}

// Export singleton instance
const socketService = new SocketService();

module.exports = socketService;
