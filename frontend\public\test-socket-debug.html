<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Socket Debug</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; width: 300px; }
        .logs { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Socket Debug</h1>
        <p>Debug socket connection và events</p>
        
        <div class="status info">
            <strong>Socket Status:</strong> <span id="socketStatus">Disconnected</span>
        </div>

        <div>
            <button class="btn-success" onclick="connectSocket()">Connect Socket</button>
            <button class="btn-danger" onclick="disconnectSocket()">Disconnect Socket</button>
            <button class="btn-primary" onclick="testEmitEvent()">Test Emit Event</button>
        </div>

        <div>
            <h3>Manual Event Test:</h3>
            <input type="text" id="roomIdInput" placeholder="Room ID" value="675a5b8b123456789abcdef0">
            <button class="btn-primary" onclick="emitHighlightExpired()">Emit Highlight Expired</button>
            <button class="btn-primary" onclick="emitRoomHighlightUpdated()">Emit Room Updated</button>
        </div>
        
        <div id="logs" class="logs">
            <strong>Socket Events Log:</strong><br>
        </div>
    </div>

    <script>
        let socket = null;

        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            logsDiv.appendChild(div);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }

        function updateSocketStatus(status) {
            document.getElementById('socketStatus').textContent = status;
        }

        function connectSocket() {
            if (socket) {
                socket.disconnect();
            }

            log('🔌 Connecting to socket...', 'info');
            socket = io('http://localhost:5000');

            socket.on('connect', () => {
                log('✅ Socket connected with ID: ' + socket.id, 'success');
                updateSocketStatus('Connected');
            });

            socket.on('disconnect', (reason) => {
                log('❌ Socket disconnected: ' + reason, 'error');
                updateSocketStatus('Disconnected');
            });

            socket.on('connect_error', (error) => {
                log('❌ Connection error: ' + error.message, 'error');
                updateSocketStatus('Error');
            });

            // Listen for highlight events
            socket.on('highlight_expired', (data) => {
                log('💥 Highlight expired event received:', 'warning');
                log('   Data: ' + JSON.stringify(data), 'warning');
            });

            socket.on('room_highlight_updated', (data) => {
                log('🔄 Room highlight updated event received:', 'info');
                log('   Data: ' + JSON.stringify(data), 'info');
            });

            socket.on('highlight_activated', (data) => {
                log('✨ Highlight activated event received:', 'success');
                log('   Data: ' + JSON.stringify(data), 'success');
            });

            // Listen for all events (debug)
            socket.onAny((eventName, ...args) => {
                log(`📡 Event received: ${eventName}`, 'info');
                if (args.length > 0) {
                    log(`   Args: ${JSON.stringify(args)}`, 'info');
                }
            });
        }

        function disconnectSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
                updateSocketStatus('Disconnected');
                log('🔌 Socket disconnected manually', 'info');
            }
        }

        function testEmitEvent() {
            if (!socket || !socket.connected) {
                log('❌ Socket not connected', 'error');
                return;
            }

            log('🧪 Testing emit event...', 'info');
            socket.emit('test_event', { message: 'Hello from client' });
        }

        function emitHighlightExpired() {
            const roomId = document.getElementById('roomIdInput').value;
            if (!roomId) {
                log('❌ Please enter Room ID', 'error');
                return;
            }

            log('🧪 Manually emitting highlight_expired event...', 'info');
            
            // Simulate the event that would come from server
            const data = {
                roomId: roomId,
                title: 'Test Room Title',
                userId: 'test-user-id',
                timestamp: new Date().toISOString()
            };

            // Emit to ourselves to test frontend handling
            if (socket) {
                socket.emit('highlight_expired', data);
                log('   Emitted data: ' + JSON.stringify(data), 'info');
            }
        }

        function emitRoomHighlightUpdated() {
            const roomId = document.getElementById('roomIdInput').value;
            if (!roomId) {
                log('❌ Please enter Room ID', 'error');
                return;
            }

            log('🧪 Manually emitting room_highlight_updated event...', 'info');
            
            // Simulate the event that would come from server
            const data = {
                roomId: roomId,
                isHighlighted: false,
                highlightExpiry: null,
                timestamp: new Date().toISOString()
            };

            // Emit to ourselves to test frontend handling
            if (socket) {
                socket.emit('room_highlight_updated', data);
                log('   Emitted data: ' + JSON.stringify(data), 'info');
            }
        }

        // Auto-connect on page load
        window.onload = () => {
            connectSocket();
        };

        // Cleanup on page unload
        window.onbeforeunload = () => {
            if (socket) {
                socket.disconnect();
            }
        };
    </script>
</body>
</html>
