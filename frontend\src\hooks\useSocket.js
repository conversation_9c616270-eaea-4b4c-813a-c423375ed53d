import { useEffect, useRef, useCallback } from 'react';
import { useAuthContext } from '../contexts';
import socketService from '../services/socketService';

const useSocket = () => {
  const { isAuthenticated, token } = useAuthContext() || {};
  const listenersRef = useRef(new Map());

  // Khởi tạo kết nối WebSocket
  useEffect(() => {
    const connectSocket = async () => {
      if (isAuthenticated && token) {
        console.log('[useSocket] Connecting to WebSocket...');
        try {
          await socketService.connect(token);
        } catch (error) {
          console.error('[useSocket] Failed to connect:', error);
        }
      } else {
        console.log('[useSocket] Disconnecting from WebSocket...');
        socketService.disconnect();
      }
    };

    connectSocket();

    // Cleanup on unmount
    return () => {
      // Don't disconnect on unmount to maintain connection across components
      // socketService.disconnect();
    };
  }, [isAuthenticated, token]);

  // Thêm event listener
  const addEventListener = useCallback((event, callback) => {
    const unsubscribe = socketService.addEventListener(event, callback);
    
    // Store unsubscribe function for cleanup
    if (!listenersRef.current.has(event)) {
      listenersRef.current.set(event, new Set());
    }
    listenersRef.current.get(event).add(unsubscribe);

    return unsubscribe;
  }, []);

  // Xóa event listener
  const removeEventListener = useCallback((event, callback) => {
    socketService.removeEventListener(event, callback);
    
    // Remove from ref
    const eventListeners = listenersRef.current.get(event);
    if (eventListeners) {
      eventListeners.forEach(unsubscribe => {
        if (unsubscribe.callback === callback) {
          eventListeners.delete(unsubscribe);
        }
      });
    }
  }, []);

  // Emit event
  const emit = useCallback((event, data) => {
    return socketService.emit(event, data);
  }, []);

  // Cleanup listeners on unmount
  useEffect(() => {
    return () => {
      listenersRef.current.forEach(eventListeners => {
        eventListeners.forEach(unsubscribe => {
          if (typeof unsubscribe === 'function') {
            unsubscribe();
          }
        });
      });
      listenersRef.current.clear();
    };
  }, []);

  return {
    isConnected: socketService.isSocketConnected(),
    isAuthenticated: socketService.isSocketAuthenticated(),
    addEventListener,
    removeEventListener,
    emit,
    socketService
  };
};

export default useSocket;
