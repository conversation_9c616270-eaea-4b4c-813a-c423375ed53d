import { useState, useEffect, useRef } from 'react';
import { FaTimes, FaHome, FaBuilding, FaUsers, FaStore, FaHotel, FaWarehouse, FaSpinner, FaMapMarkerAlt } from 'react-icons/fa';
import PropTypes from 'prop-types';
import { useLocations } from '../../hooks';

const FilterPopup = ({
  isOpen,
  onClose,
  categories = [],
  filters,
  onApplyFilters
}) => {
  const [localFilters, setLocalFilters] = useState({
    category: '',
    city: '',
    district: '',
    ward: '',
    priceRange: '',
    areaRange: '',
    ...filters
  });

  const popupRef = useRef(null);

  // Sử dụng hook useLocations để lấy dữ liệu địa chỉ hành chính
  const {
    provinces,
    districts,
    wards,
    isLoading: isLocationLoading,
    error: locationError,
    fetchProvinces,
    fetchDistrictsByProvince,
    fetchWardsByDistrict
  } = useLocations();

  // Đóng popup khi click ra ngoài
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        onClose();
      }
    };

    // Đóng popup khi nhấn phím Escape
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscKey);
      // Ngăn cuộn trang khi popup mở
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscKey);
      // Cho phép cuộn trang khi popup đóng
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  // Cập nhật localFilters khi filters thay đổi
  useEffect(() => {
    if (isOpen) {
      setLocalFilters({
        category: filters.category || '',
        city: filters.city || '',
        district: filters.district || '',
        ward: filters.ward || '',
        priceRange: getPriceRangeValue(filters.minPrice, filters.maxPrice),
        areaRange: getAreaRangeValue(filters.minArea, filters.maxArea),
      });
    }
  }, [isOpen, filters]);

  // Lấy danh sách tỉnh/thành phố khi popup mở
  useEffect(() => {
    if (isOpen) {
      fetchProvinces();
    }
  }, [isOpen, fetchProvinces]);

  // Lấy danh sách quận/huyện khi chọn tỉnh/thành phố
  useEffect(() => {
    if (localFilters.city) {
      fetchDistrictsByProvince(localFilters.city);
    } else {
      // Reset danh sách quận/huyện và phường/xã khi không chọn tỉnh/thành phố
      setLocalFilters(prev => ({
        ...prev,
        district: '',
        ward: ''
      }));
    }
  }, [localFilters.city, fetchDistrictsByProvince]);

  // Lấy danh sách phường/xã khi chọn quận/huyện
  useEffect(() => {
    if (localFilters.city && localFilters.district) {
      fetchWardsByDistrict(localFilters.city, localFilters.district);
    } else {
      // Reset danh sách phường/xã khi không chọn quận/huyện
      setLocalFilters(prev => ({
        ...prev,
        ward: ''
      }));
    }
  }, [localFilters.city, localFilters.district, fetchWardsByDistrict]);

  // Hàm chuyển đổi giá trị khoảng giá thành giá trị cho radio button
  const getPriceRangeValue = (minPrice, maxPrice) => {
    if (!minPrice && !maxPrice) return '';
    if (minPrice === '0' && maxPrice === '1000000') return 'duoi_1_trieu';
    if (minPrice === '1000000' && maxPrice === '2000000') return '1_2_trieu';
    if (minPrice === '2000000' && maxPrice === '3000000') return '2_3_trieu';
    if (minPrice === '3000000' && maxPrice === '5000000') return '3_5_trieu';
    if (minPrice === '5000000' && maxPrice === '7000000') return '5_7_trieu';
    if (minPrice === '7000000' && maxPrice === '10000000') return '7_10_trieu';
    if (minPrice === '10000000' && maxPrice === '15000000') return '10_15_trieu';
    if (minPrice === '15000000' && !maxPrice) return 'tren_15_trieu';
    return '';
  };

  // Hàm chuyển đổi giá trị khoảng diện tích thành giá trị cho radio button
  const getAreaRangeValue = (minArea, maxArea) => {
    if (!minArea && !maxArea) return '';
    if (minArea === '0' && maxArea === '20') return 'duoi_20m2';
    if (minArea === '20' && maxArea === '30') return '20m2_30m2';
    if (minArea === '30' && maxArea === '50') return '30m2_50m2';
    if (minArea === '50' && maxArea === '70') return '50m2_70m2';
    if (minArea === '70' && maxArea === '90') return '70m2_90m2';
    if (minArea === '90' && !maxArea) return 'tren_90m2';
    return '';
  };

  // Hàm chuyển đổi giá trị radio button thành khoảng giá
  const getPriceRangeFromValue = (value) => {
    switch (value) {
      case 'duoi_1_trieu':
        return { minPrice: '0', maxPrice: '1000000' };
      case '1_2_trieu':
        return { minPrice: '1000000', maxPrice: '2000000' };
      case '2_3_trieu':
        return { minPrice: '2000000', maxPrice: '3000000' };
      case '3_5_trieu':
        return { minPrice: '3000000', maxPrice: '5000000' };
      case '5_7_trieu':
        return { minPrice: '5000000', maxPrice: '7000000' };
      case '7_10_trieu':
        return { minPrice: '7000000', maxPrice: '10000000' };
      case '10_15_trieu':
        return { minPrice: '10000000', maxPrice: '15000000' };
      case 'tren_15_trieu':
        return { minPrice: '15000000', maxPrice: '' };
      default:
        return { minPrice: '', maxPrice: '' };
    }
  };

  // Hàm chuyển đổi giá trị radio button thành khoảng diện tích
  const getAreaRangeFromValue = (value) => {
    switch (value) {
      case 'duoi_20m2':
        return { minArea: '0', maxArea: '20' };
      case '20m2_30m2':
        return { minArea: '20', maxArea: '30' };
      case '30m2_50m2':
        return { minArea: '30', maxArea: '50' };
      case '50m2_70m2':
        return { minArea: '50', maxArea: '70' };
      case '70m2_90m2':
        return { minArea: '70', maxArea: '90' };
      case 'tren_90m2':
        return { minArea: '90', maxArea: '' };
      default:
        return { minArea: '', maxArea: '' };
    }
  };

  // Lấy icon cho từng loại phòng
  const getCategoryIcon = (categoryName) => {
    const name = categoryName.toLowerCase();
    if (name.includes('phòng trọ')) return <FaHome className="text-xl" />;
    if (name.includes('nhà riêng')) return <FaBuilding className="text-xl" />;
    if (name.includes('ở ghép')) return <FaUsers className="text-xl" />;
    if (name.includes('mặt bằng')) return <FaStore className="text-xl" />;
    if (name.includes('chung cư')) return <FaHotel className="text-xl" />;
    if (name.includes('mini')) return <FaWarehouse className="text-xl" />;
    return <FaHome className="text-xl" />; // Mặc định
  };

  // Xử lý khi thay đổi loại phòng
  const handleCategoryChange = (categoryId) => {
    setLocalFilters({
      ...localFilters,
      category: localFilters.category === categoryId ? '' : categoryId
    });
  };

  // Xử lý khi thay đổi khoảng giá
  const handlePriceRangeChange = (value) => {
    setLocalFilters({
      ...localFilters,
      priceRange: localFilters.priceRange === value ? '' : value
    });
  };

  // Xử lý khi thay đổi khoảng diện tích
  const handleAreaRangeChange = (value) => {
    setLocalFilters({
      ...localFilters,
      areaRange: localFilters.areaRange === value ? '' : value
    });
  };

  // Xử lý khi áp dụng bộ lọc
  const handleApplyFilters = () => {
    const priceRange = getPriceRangeFromValue(localFilters.priceRange);
    const areaRange = getAreaRangeFromValue(localFilters.areaRange);

    const updatedFilters = {
      ...filters,
      category: localFilters.category,
      city: localFilters.city,
      district: localFilters.district,
      ward: localFilters.ward,
      minPrice: priceRange.minPrice,
      maxPrice: priceRange.maxPrice,
      minArea: areaRange.minArea,
      maxArea: areaRange.maxArea,
    };

    onApplyFilters(updatedFilters);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[1000] bg-black bg-opacity-50 flex items-start justify-center overflow-y-auto">
      <div
        ref={popupRef}
        className="bg-white w-full max-w-md mx-2 sm:mx-4 my-4 sm:my-8 rounded-lg shadow-xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 bg-gray-50 rounded-t-lg">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Bộ lọc tìm kiếm</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-200 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            aria-label="Đóng bộ lọc"
          >
            <FaTimes className="w-4 h-4" />
          </button>
        </div>

        {/* Body */}
        <div className="p-4 sm:p-6 space-y-6 max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-140px)] overflow-y-auto">
          {/* Danh mục cho thuê */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Danh mục cho thuê</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
              {categories.map((category) => (
                <button
                  key={category._id}
                  className={`flex flex-col items-center justify-center p-3 sm:p-4 rounded-lg border-2 transition-all duration-200 ${
                    localFilters.category === category._id
                      ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                  }`}
                  onClick={() => handleCategoryChange(category._id)}
                >
                  <div className="text-xl sm:text-2xl mb-1">
                    {getCategoryIcon(category.name)}
                  </div>
                  <span className="text-xs sm:text-sm font-medium text-center leading-tight">
                    {category.name}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Lọc theo khu vực */}
          <div>
            <h3 className="flex items-center font-medium mb-3">
              <FaMapMarkerAlt className="mr-2 text-primary" />
              Lọc theo khu vực
            </h3>

            {/* Error message */}
            {locationError && (
              <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{locationError}</p>
              </div>
            )}

            <div className="space-y-3">
              {/* Tỉnh/Thành phố */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tỉnh/Thành phố
                </label>
                <select
                  className="form-input w-full pr-10"
                  value={localFilters.city}
                  onChange={(e) => setLocalFilters({ ...localFilters, city: e.target.value })}
                  disabled={isLocationLoading}
                >
                  <option value="">Chọn tỉnh/thành phố</option>
                  {provinces.map((province) => (
                    <option key={province.code} value={province.name}>
                      {province.name}
                    </option>
                  ))}
                </select>
                {isLocationLoading && (
                  <div className="absolute right-3 top-9 flex items-center">
                    <FaSpinner className="animate-spin text-gray-400" />
                  </div>
                )}
              </div>

              {/* Quận/Huyện */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Quận/Huyện
                </label>
                <select
                  className="form-input w-full pr-10"
                  value={localFilters.district}
                  onChange={(e) => setLocalFilters({ ...localFilters, district: e.target.value })}
                  disabled={!localFilters.city || isLocationLoading}
                >
                  <option value="">
                    {localFilters.city ? 'Chọn quận/huyện' : 'Chọn tỉnh/thành phố trước'}
                  </option>
                  {districts.map((district) => (
                    <option key={district.code} value={district.name}>
                      {district.name}
                    </option>
                  ))}
                </select>
                {isLocationLoading && localFilters.city && (
                  <div className="absolute right-3 top-9 flex items-center">
                    <FaSpinner className="animate-spin text-gray-400" />
                  </div>
                )}
              </div>

              {/* Phường/Xã */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phường/Xã
                </label>
                <select
                  className="form-input w-full pr-10"
                  value={localFilters.ward}
                  onChange={(e) => setLocalFilters({ ...localFilters, ward: e.target.value })}
                  disabled={!localFilters.district || isLocationLoading}
                >
                  <option value="">
                    {localFilters.district ? 'Chọn phường/xã' : 'Chọn quận/huyện trước'}
                  </option>
                  {wards.map((ward) => (
                    <option key={ward.code} value={ward.name}>
                      {ward.name}
                    </option>
                  ))}
                </select>
                {isLocationLoading && localFilters.district && (
                  <div className="absolute right-3 top-9 flex items-center">
                    <FaSpinner className="animate-spin text-gray-400" />
                  </div>
                )}
              </div>

              {/* Selected address display */}
              {(localFilters.city || localFilters.district || localFilters.ward) && (
                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-sm text-blue-700">
                    <strong>Địa chỉ đã chọn:</strong>{' '}
                    {[localFilters.ward, localFilters.district, localFilters.city]
                      .filter(Boolean)
                      .join(', ')}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Khoảng giá */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Khoảng giá</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.priceRange === 'duoi_1_trieu'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handlePriceRangeChange('duoi_1_trieu')}
              >
                Dưới 1 triệu
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.priceRange === '1_2_trieu'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handlePriceRangeChange('1_2_trieu')}
              >
                1 - 2 triệu
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.priceRange === '2_3_trieu'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handlePriceRangeChange('2_3_trieu')}
              >
                2 - 3 triệu
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.priceRange === '3_5_trieu'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handlePriceRangeChange('3_5_trieu')}
              >
                3 - 5 triệu
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.priceRange === '5_7_trieu'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handlePriceRangeChange('5_7_trieu')}
              >
                5 - 7 triệu
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.priceRange === '7_10_trieu'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handlePriceRangeChange('7_10_trieu')}
              >
                7 - 10 triệu
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.priceRange === '10_15_trieu'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handlePriceRangeChange('10_15_trieu')}
              >
                10 - 15 triệu
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.priceRange === 'tren_15_trieu'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handlePriceRangeChange('tren_15_trieu')}
              >
                Trên 15 triệu
              </button>
            </div>
          </div>

          {/* Khoảng diện tích */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Khoảng diện tích</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.areaRange === 'duoi_20m2'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handleAreaRangeChange('duoi_20m2')}
              >
                Dưới 20m²
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.areaRange === '20m2_30m2'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handleAreaRangeChange('20m2_30m2')}
              >
                20 - 30m²
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.areaRange === '30m2_50m2'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handleAreaRangeChange('30m2_50m2')}
              >
                30 - 50m²
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.areaRange === '50m2_70m2'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handleAreaRangeChange('50m2_70m2')}
              >
                50 - 70m²
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.areaRange === '70m2_90m2'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handleAreaRangeChange('70m2_90m2')}
              >
                70 - 90m²
              </button>
              <button
                className={`px-3 py-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                  localFilters.areaRange === 'tren_90m2'
                    ? 'border-primary bg-primary bg-opacity-10 text-primary shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
                onClick={() => handleAreaRangeChange('tren_90m2')}
              >
                Trên 90m²
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 sm:p-6 border-t border-gray-200 bg-gray-50 rounded-b-lg">
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={() => {
                setLocalFilters({
                  category: '',
                  city: '',
                  district: '',
                  ward: '',
                  priceRange: '',
                  areaRange: ''
                });
              }}
              className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Xóa bộ lọc
            </button>
            <button
              onClick={handleApplyFilters}
              disabled={isLocationLoading}
              className="flex-1 py-3 px-4 bg-primary text-white rounded-lg font-medium hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              {isLocationLoading ? (
                <span className="flex items-center justify-center">
                  <FaSpinner className="animate-spin mr-2" />
                  Đang tải...
                </span>
              ) : (
                'Áp dụng bộ lọc'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

FilterPopup.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  categories: PropTypes.array,
  filters: PropTypes.object,
  onApplyFilters: PropTypes.func.isRequired
};

export default FilterPopup;
