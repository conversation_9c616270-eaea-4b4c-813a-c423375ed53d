const multer = require('multer');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Lấy cấu hình từ biến môi trường
const UPLOAD_PATH = process.env.UPLOAD_PATH || 'uploads';
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024; // Mặc định 10MB
const MAX_FILES = parseInt(process.env.MAX_FILES) || 10; // Mặc định 10 files

// Đường dẫn đến thư mục uploads
const uploadDir = path.join(__dirname, `../${UPLOAD_PATH}`);

// Đ<PERSON><PERSON> bảo thư mục uploads tồn tại
if (!fs.existsSync(uploadDir)) {
  console.log(`Thư mục ${UPLOAD_PATH} không tồn tại, đang tạo mới...`);
  fs.mkdirSync(uploadDir, { recursive: true });
}

console.log(`<PERSON><PERSON><PERSON> hình upload:
- <PERSON><PERSON><PERSON> mục: ${uploadDir}
- Kích thước tối đa: ${MAX_FILE_SIZE / (1024 * 1024)}MB
- Số lượng file tối đa: ${MAX_FILES}`);

// Cấu hình lưu trữ file
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Giữ nguyên tên file gốc nhưng thêm timestamp để tránh trùng lặp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    const filename = file.fieldname + '-' + uniqueSuffix + ext;

    console.log(`Đang lưu file: ${filename}`);
    cb(null, filename);
  }
});

// Kiểm tra loại file
const fileFilter = (req, file, cb) => {
  const allowedFileTypes = /jpeg|jpg|png|gif/;
  const extname = allowedFileTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedFileTypes.test(file.mimetype);

  if (extname && mimetype) {
    return cb(null, true);
  } else {
    cb(new Error('Chỉ chấp nhận file hình ảnh: jpeg, jpg, png, gif'));
  }
};

// Cấu hình upload
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: MAX_FILES
  }
});

// Middleware xử lý lỗi upload
const handleUpload = (req, res, next) => {
  const uploadHandler = upload.array('images', MAX_FILES);

  uploadHandler(req, res, (err) => {
    if (err) {
      console.error('Lỗi upload file:', err);

      if (err instanceof multer.MulterError) {
        // Lỗi từ Multer
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            success: false,
            message: `Kích thước file quá lớn. Giới hạn là ${MAX_FILE_SIZE / (1024 * 1024)}MB.`
          });
        } else if (err.code === 'LIMIT_FILE_COUNT') {
          return res.status(400).json({
            success: false,
            message: `Số lượng file vượt quá giới hạn. Tối đa ${MAX_FILES} file.`
          });
        } else {
          return res.status(400).json({
            success: false,
            message: `Lỗi upload: ${err.message}`
          });
        }
      } else {
        // Lỗi khác
        return res.status(500).json({
          success: false,
          message: err.message || 'Có lỗi xảy ra khi upload file.'
        });
      }
    }

    // Nếu không có file nào được upload
    if (!req.files || req.files.length === 0) {
      console.log('Không có file nào được upload');
    } else {
      console.log(`Đã upload ${req.files.length} file thành công`);
    }

    next();
  });
};

module.exports = handleUpload;
