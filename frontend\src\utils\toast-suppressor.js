/**
 * Toast Suppressor Utility
 * Hoàn toàn disable tất cả toast errors không mong muốn
 */

import { toast } from 'react-toastify';

// <PERSON><PERSON> sách các message patterns cần suppress
const SUPPRESSED_PATTERNS = [
  '<PERSON>ất kết nối với server',
  'refresh trang',
  'Network Error',
  'Không thể kết nối đến máy chủ',
  'Vui lòng refresh',
  'connection',
  'server',
  'network'
];

// Backup original toast methods
const originalMethods = {
  error: toast.error,
  warn: toast.warn,
  info: toast.info,
  success: toast.success
};

// Function to check if message should be suppressed
const shouldSuppressMessage = (message) => {
  if (!message || typeof message !== 'string') return false;
  
  return SUPPRESSED_PATTERNS.some(pattern => 
    message.toLowerCase().includes(pattern.toLowerCase())
  );
};

// Override toast methods
export const suppressToastErrors = () => {
  console.log('🔇 Activating toast suppressor...');
  
  toast.error = (message, options) => {
    if (shouldSuppressMessage(message)) {
      console.log('🔇 Toast error suppressed:', message);
      return;
    }
    return originalMethods.error(message, options);
  };

  toast.warn = (message, options) => {
    if (shouldSuppressMessage(message)) {
      console.log('🔇 Toast warning suppressed:', message);
      return;
    }
    return originalMethods.warn(message, options);
  };

  // Keep success and info toasts
  toast.success = originalMethods.success;
  toast.info = originalMethods.info;
};

// Restore original toast methods
export const restoreToastMethods = () => {
  console.log('🔄 Restoring original toast methods...');
  
  toast.error = originalMethods.error;
  toast.warn = originalMethods.warn;
  toast.info = originalMethods.info;
  toast.success = originalMethods.success;
};

// Auto-activate suppressor
suppressToastErrors();

// Export for manual control
export default {
  suppress: suppressToastErrors,
  restore: restoreToastMethods,
  shouldSuppressMessage
};
