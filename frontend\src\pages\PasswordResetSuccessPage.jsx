import { useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';

const PasswordResetSuccessPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Lấy email từ state đượ<PERSON><PERSON> từ ResetPasswordPage
  const email = location.state?.email || '';

  useEffect(() => {
    // Auto redirect đến login sau 5 giây
    const timer = setTimeout(() => {
      navigate('/login');
    }, 5000);

    return () => clearTimeout(timer);
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Reset mật khẩu thành công!
          </h2>
          
          <p className="text-gray-600 mb-6">
            Mật khẩu của bạn đã được cập nhật thành công
          </p>

          <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
            <div className="flex">
              <svg className="w-5 h-5 text-green-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
              </svg>
              <div>
                <h3 className="text-sm font-medium text-green-800">Mật khẩu đã được cập nhật</h3>
                <div className="mt-2 text-sm text-green-700">
                  {email && (
                    <p className="mb-2">
                      Tài khoản: <span className="font-medium">{email}</span>
                    </p>
                  )}
                  <p>Bạn có thể đăng nhập với mật khẩu mới ngay bây giờ.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
            <div className="flex">
              <svg className="w-5 h-5 text-blue-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
              </svg>
              <div>
                <h3 className="text-sm font-medium text-blue-800">Lưu ý bảo mật</h3>
                <div className="mt-2 text-sm text-blue-700">
                  <ul className="list-disc list-inside space-y-1">
                    <li>Hãy ghi nhớ mật khẩu mới của bạn</li>
                    <li>Không chia sẻ mật khẩu với ai khác</li>
                    <li>Đăng xuất khỏi các thiết bị không tin cậy</li>
                    <li>Thay đổi mật khẩu định kỳ để bảo mật tài khoản</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <Link
              to="/login"
              className="block w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-primary-dark transition duration-200 text-center"
            >
              Đăng nhập ngay
            </Link>

            <Link
              to="/"
              className="block w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition duration-200 text-center"
            >
              Quay về trang chủ
            </Link>
          </div>

          <div className="text-center mt-4">
            <p className="text-sm text-gray-500">
              Bạn sẽ được chuyển hướng đến trang đăng nhập trong 5 giây...
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordResetSuccessPage;
