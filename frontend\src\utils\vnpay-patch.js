/**
 * VNPay Patch Utility
 *
 * This utility provides fixes for various issues with VNPay Sandbox integration:
 * 1. Content Security Policy (CSP) issues
 * 2. Deprecated CSS properties (-ms-high-contrast)
 * 3. JavaScript reference errors (timer is not defined)
 */

/**
 * Fix the "timer is not defined" JavaScript error
 *
 * This function defines the missing timer variable in the global scope
 * before VNPay's code attempts to use it in the updateTime() function.
 * The error occurs at position 1:1651 in custom.min.js when the document is ready.
 */
export const fixVNPayTimerIssue = () => {
  // Kiểm tra xem biến timer đã được định nghĩa chưa
  if (typeof window !== 'undefined') {
    try {
      // Định nghĩa biến timer toàn cục nếu chưa tồn tại
      if (window.timer === undefined) {
        // Sử dụng Object.defineProperty để đảm bảo timer không bị xóa
        Object.defineProperty(window, 'timer', {
          value: null,
          writable: true,
          configurable: false // Không cho phép xóa biến này
        });
        console.log('[VNPay Patch] Đã định nghĩa biến timer để khắc phục lỗi VNPay Sandbox');
      }

      // Đảm bảo hàm updateTime tồn tại và hoạt động đúng
      if (typeof window.updateTime === 'undefined') {
        // Lưu trữ hàm gốc nếu nó tồn tại
        const originalUpdateTime = window.updateTime;

        // Ghi đè hàm updateTime với phiên bản an toàn
        window.updateTime = function() {
          try {
            // Nếu hàm gốc tồn tại, gọi nó
            if (typeof originalUpdateTime === 'function') {
              return originalUpdateTime.apply(this, arguments);
            }

            // Nếu không, thực hiện phiên bản an toàn
            console.log('[VNPay Patch] Safe updateTime called');
            return null;
          } catch (e) {
            console.log('[VNPay Patch] Error in updateTime, using safe version:', e);
            // Đảm bảo timer tồn tại
            window.timer = window.timer || null;
            return null;
          }
        };

        console.log('[VNPay Patch] Đã định nghĩa hàm updateTime an toàn');
      }

      // Xử lý lỗi trong jquery.bundles.js
      if (window.jQuery) {
        const originalReady = window.jQuery.fn.ready;
        window.jQuery.fn.ready = function(fn) {
          return originalReady.call(this, function() {
            try {
              // Đảm bảo timer tồn tại trước khi gọi handler
              window.timer = window.timer || null;
              return fn.apply(this, arguments);
            } catch (e) {
              console.error('[VNPay Patch] Error in jQuery ready handler:', e);
              return null;
            }
          });
        };
        console.log('[VNPay Patch] Đã patch jQuery.fn.ready');
      }
    } catch (e) {
      console.error('[VNPay Patch] Error while fixing timer issue:', e);
    }
  }
};

/**
 * Handle VNPay's custom.min.js script
 *
 * This function specifically targets the custom.min.js script that causes
 * the "timer is not defined" error. It injects a script that runs before
 * VNPay's scripts to define the missing variables and functions.
 */
export const handleCustomMinJs = () => {
  if (typeof document === 'undefined') return;

  // Create a patch script that runs before any VNPay scripts
  const patchScript = document.createElement('script');
  patchScript.id = 'vnpay-custom-patch';
  patchScript.textContent = `
    // Define timer variable globally
    window.timer = null;

    // Create a safe version of updateTime function that will be used if VNPay's version fails
    window.originalUpdateTime = window.updateTime;
    window.updateTime = function() {
      try {
        // If originalUpdateTime exists and is a function, try to call it
        if (typeof window.originalUpdateTime === 'function') {
          window.originalUpdateTime.apply(this, arguments);
        }
      } catch (e) {
        console.log('[VNPay Patch] Error in original updateTime, using safe version');
        // Safe implementation that doesn't cause errors
        if (window.timer === null) {
          // Do nothing, timer is not active
        }
      }
    };

    // Fix for jquery.bundles.js issues
    // Ensure jQuery is properly patched when it loads
    const originalJQueryFn = window.jQuery ? window.jQuery.fn : null;
    Object.defineProperty(window, 'jQuery', {
      get: function() { return window._jQuery; },
      set: function(newJQuery) {
        // Patch jQuery when it's set
        window._jQuery = newJQuery;
        if (newJQuery && newJQuery.fn) {
          // Ensure ready event doesn't throw errors
          const originalReady = newJQuery.fn.ready;
          newJQuery.fn.ready = function(fn) {
            return originalReady.call(this, function() {
              try {
                // Ensure timer is defined before calling the handler
                window.timer = window.timer || null;
                fn.apply(this, arguments);
              } catch (e) {
                console.error('[VNPay Patch] Error in jQuery ready handler:', e);
              }
            });
          };
        }
      }
    });

    // If jQuery is already loaded, patch it
    if (window.jQuery) {
      const tempJQuery = window.jQuery;
      window.jQuery = null; // Trigger the setter
      window._jQuery = tempJQuery; // This will go through our patched setter
    }

    // Monitor script loading to ensure our patches are applied
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(function(node) {
            if (node.tagName === 'SCRIPT') {
              // If a script is added, ensure timer is defined
              window.timer = window.timer || null;

              // If it's a VNPay script, add extra protection
              if (node.src && (
                  node.src.includes('custom.min.js') ||
                  node.src.includes('jquery.bundles.js') ||
                  node.src.includes('vnpay')
                )) {
                console.log('[VNPay Patch] Detected VNPay script loading:', node.src);

                // Add a load event listener to ensure timer is defined after script loads
                node.addEventListener('load', function() {
                  window.timer = window.timer || null;
                  console.log('[VNPay Patch] Ensured timer is defined after script load');
                });
              }
            }
          });
        }
      });
    });

    // Start observing document for script additions
    observer.observe(document, { childList: true, subtree: true });

    console.log('[VNPay Patch] Initialized custom.min.js and jquery.bundles.js patches');
  `;

  // Add the patch script to the document if it doesn't exist
  if (!document.getElementById('vnpay-custom-patch')) {
    // Insert at the beginning of head to ensure it runs before any other scripts
    if (document.head.firstChild) {
      document.head.insertBefore(patchScript, document.head.firstChild);
    } else {
      document.head.appendChild(patchScript);
    }
    console.log('[VNPay Patch] Added enhanced custom.min.js patch script');
  }
};

/**
 * Khắc phục các lỗi JavaScript khác từ VNPay Sandbox
 *
 * Hàm này sẽ ghi đè một số hàm của VNPay để ngăn chặn lỗi.
 */
export const fixVNPayOtherIssues = () => {
  // Ghi đè console.error để bỏ qua các lỗi từ VNPay
  const originalConsoleError = console.error;
  console.error = (...args) => {
    // Bỏ qua các lỗi từ VNPay
    if (args[0] && typeof args[0] === 'string' &&
        (args[0].includes('timer is not defined') ||
         args[0].includes('custom.min.js'))) {
      return;
    }

    // Gọi hàm gốc cho các lỗi khác
    originalConsoleError(...args);
  };

  console.log('[VNPay Patch] Đã ghi đè console.error để bỏ qua các lỗi từ VNPay Sandbox');

  // Trả về hàm để khôi phục console.error gốc khi cần
  return () => {
    console.error = originalConsoleError;
    console.log('[VNPay Patch] Đã khôi phục console.error gốc');
  };
};

/**
 * Fix Content Security Policy (CSP) issues
 *
 * This function intercepts and corrects CSP headers in responses
 * by using a MutationObserver to detect and modify meta tags.
 * It fixes incorrect syntax in default-src directive that incorrectly
 * includes style-src or img-src as source expressions.
 */
export const fixContentSecurityPolicy = () => {
  if (typeof window === 'undefined' || !window.document) return;

  // Function to fix CSP in meta tags
  const fixCspMetaTags = () => {
    const metaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');

    metaTags.forEach(metaTag => {
      let content = metaTag.getAttribute('content');

      // Fix incorrect syntax in default-src directive
      if (content) {
        // Check for common CSP syntax errors
        if (content.includes('default-src style-src')) {
          content = content.replace('default-src style-src', 'default-src; style-src');
          metaTag.setAttribute('content', content);
          console.log('[VNPay Patch] Fixed CSP meta tag (style-src)');
        }

        if (content.includes('default-src img-src')) {
          content = content.replace('default-src img-src', 'default-src; img-src');
          metaTag.setAttribute('content', content);
          console.log('[VNPay Patch] Fixed CSP meta tag (img-src)');
        }
      }
    });
  };

  // Fix CSP meta tags on load
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    fixCspMetaTags();
  } else {
    window.addEventListener('DOMContentLoaded', fixCspMetaTags);
  }

  // Set up MutationObserver to catch dynamically added meta tags
  const observer = new MutationObserver(mutations => {
    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        fixCspMetaTags();
        break;
      }
    }
  });

  // Start observing the document
  observer.observe(document.documentElement, {
    childList: true,
    subtree: true
  });

  return () => {
    observer.disconnect();
    console.log('[VNPay Patch] Disconnected CSP observer');
  };
};

/**
 * Preload VNPay scripts
 *
 * This function preloads common VNPay scripts to ensure they're available
 * when needed, reducing the chance of reference errors.
 */
/**
 * Tải thư viện jQuery và jQuery Validate từ CDN
 *
 * Hàm này tải các thư viện cần thiết cho VNPay SDK và đảm bảo chúng được tải
 * trước khi VNPay SDK được sử dụng.
 */
export const loadJQueryLibraries = () => {
  return new Promise((resolve, reject) => {
    if (typeof document === 'undefined') {
      reject(new Error('Document is not available'));
      return;
    }

    // Kiểm tra xem jQuery đã được tải chưa
    if (window.jQuery) {
      console.log('[VNPay Patch] jQuery đã được tải');

      // Kiểm tra xem jQuery Validate đã được tải chưa
      if (window.jQuery.validator) {
        console.log('[VNPay Patch] jQuery Validate đã được tải');
        resolve();
        return;
      }
    }

    // Tải jQuery nếu chưa có
    if (!window.jQuery) {
      const jqueryScript = document.createElement('script');
      jqueryScript.src = 'https://code.jquery.com/jquery-3.6.4.min.js';
      jqueryScript.integrity = 'sha256-oP6HI9z1XaZNBrJURtCoUT5SUnxFr8s3BzRl+cbzUq8=';
      jqueryScript.crossOrigin = 'anonymous';

      jqueryScript.onload = () => {
        console.log('[VNPay Patch] jQuery đã được tải thành công');

        // Sau khi jQuery được tải, tải jQuery Validate
        const validateScript = document.createElement('script');
        validateScript.src = 'https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js';
        validateScript.crossOrigin = 'anonymous';

        validateScript.onload = () => {
          console.log('[VNPay Patch] jQuery Validate đã được tải thành công');
          resolve();
        };

        validateScript.onerror = () => {
          console.error('[VNPay Patch] Không thể tải jQuery Validate');
          reject(new Error('Failed to load jQuery Validate'));
        };

        document.head.appendChild(validateScript);
      };

      jqueryScript.onerror = () => {
        console.error('[VNPay Patch] Không thể tải jQuery');
        reject(new Error('Failed to load jQuery'));
      };

      document.head.appendChild(jqueryScript);
    } else {
      // jQuery đã được tải, chỉ cần tải jQuery Validate
      const validateScript = document.createElement('script');
      validateScript.src = 'https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js';
      validateScript.crossOrigin = 'anonymous';

      validateScript.onload = () => {
        console.log('[VNPay Patch] jQuery Validate đã được tải thành công');
        resolve();
      };

      validateScript.onerror = () => {
        console.error('[VNPay Patch] Không thể tải jQuery Validate');
        reject(new Error('Failed to load jQuery Validate'));
      };

      document.head.appendChild(validateScript);
    }
  });
};

export const preloadVNPayScripts = () => {
  if (typeof document === 'undefined') return;

  // Common VNPay script URLs - Sử dụng CDN thay thế cho jQuery và jQuery Validate
  const scriptUrls = [
    'https://sandbox.vnpayment.vn/paymentv2/lib/vnpay/vnpay.js',
    'https://code.jquery.com/jquery-3.6.4.min.js', // jQuery từ CDN chính thức
    'https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js' // jQuery Validate từ jsDelivr
  ];

  // Check if scripts are already loaded
  const loadedScripts = Array.from(document.getElementsByTagName('script'))
    .map(script => script.src);

  // Preload scripts that aren't already loaded
  scriptUrls.forEach(url => {
    if (!loadedScripts.some(loadedUrl => loadedUrl.includes(url))) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'script';
      link.href = url;
      link.crossOrigin = 'anonymous';

      document.head.appendChild(link);
      console.log(`[VNPay Patch] Preloaded script: ${url}`);
    }
  });
};

/**
 * Fix deprecated CSS properties
 *
 * This function adds a stylesheet that overrides the deprecated
 * -ms-high-contrast CSS property with the modern Forced Colors Mode standard.
 */
export const fixDeprecatedCssProperties = () => {
  if (typeof document === 'undefined') return;

  // Create a style element if it doesn't exist
  if (!document.getElementById('vnpay-css-fixes')) {
    const style = document.createElement('style');
    style.id = 'vnpay-css-fixes';
    style.textContent = `
      /* Replace deprecated -ms-high-contrast with modern Forced Colors Mode */
      @media (forced-colors: active) {
        /* General fixes for high contrast mode */
        button, input, select, textarea {
          forced-color-adjust: none;
          border: 1px solid currentColor;
        }

        /* Specific fixes for VNPay elements */
        [class*="vnpay-"] button,
        [class*="vnpay-"] input,
        [class*="vnpay-"] select,
        [class*="vnpay-"] textarea,
        [id*="vnpay-"] button,
        [id*="vnpay-"] input,
        [id*="vnpay-"] select,
        [id*="vnpay-"] textarea {
          forced-color-adjust: none;
          border: 1px solid currentColor;
          background-color: Canvas;
          color: CanvasText;
        }
      }
    `;

    document.head.appendChild(style);
    console.log('[VNPay Patch] Added CSS fixes for deprecated -ms-high-contrast property');
  }
};

/**
 * Apply all VNPay patches
 *
 * This function applies all the fixes for VNPay integration issues.
 * It should be called before redirecting to VNPay payment page and
 * when handling the payment response.
 */
export const applyVNPayPatches = async () => {
  // Tải jQuery và jQuery Validate từ CDN
  try {
    await loadJQueryLibraries();
    console.log('[VNPay Patch] Đã tải thành công jQuery và jQuery Validate');
  } catch (error) {
    console.error('[VNPay Patch] Lỗi khi tải jQuery và jQuery Validate:', error);
  }

  // Preload VNPay scripts
  preloadVNPayScripts();

  // Handle custom.min.js specifically
  handleCustomMinJs();

  // Fix JavaScript reference errors
  fixVNPayTimerIssue();

  // Fix other JavaScript issues
  const restoreConsoleError = fixVNPayOtherIssues();

  // Fix Content Security Policy issues
  const cleanupCsp = fixContentSecurityPolicy();

  // Fix deprecated CSS properties
  fixDeprecatedCssProperties();

  // Thêm script VNPay sau khi đã tải jQuery và jQuery Validate
  if (typeof document !== 'undefined') {
    const vnpayScript = document.createElement('script');
    vnpayScript.src = 'https://sandbox.vnpayment.vn/paymentv2/lib/vnpay/vnpay.js';
    vnpayScript.async = true;
    vnpayScript.crossOrigin = 'anonymous';
    document.head.appendChild(vnpayScript);
    console.log('[VNPay Patch] Đã thêm script VNPay');
  }

  // Add event listener for iframe load events to reapply fixes
  const handleIframeLoad = () => {
    const iframes = document.querySelectorAll('iframe');
    iframes.forEach(iframe => {
      try {
        if (iframe.src && iframe.src.includes('vnpay')) {
          // Try to access iframe content and apply fixes
          const iframeWindow = iframe.contentWindow;
          if (iframeWindow && iframeWindow.document) {
            if (iframeWindow.timer === undefined) {
              iframeWindow.timer = null;
              console.log('[VNPay Patch] Fixed timer in iframe');
            }
          }
        }
      } catch (e) {
        // Cross-origin restrictions may prevent access
        console.log('[VNPay Patch] Could not access iframe content due to cross-origin restrictions');
      }
    });
  };

  // Apply fixes to iframes when they load
  window.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'vnpay-iframe-loaded') {
      handleIframeLoad();
    }
  });

  // Set interval to periodically check and fix timer and related issues
  const intervalId = setInterval(() => {
    try {
      // Kiểm tra và khắc phục biến timer
      if (typeof window.timer === 'undefined') {
        Object.defineProperty(window, 'timer', {
          value: null,
          writable: true,
          configurable: false
        });
        console.log('[VNPay Patch] Reapplied timer fix');
      }

      // Kiểm tra và khắc phục hàm updateTime
      if (typeof window.updateTime !== 'function') {
        window.updateTime = function() {
          console.log('[VNPay Patch] Safe updateTime called from interval check');
          return null;
        };
        console.log('[VNPay Patch] Reapplied updateTime fix');
      }

      // Kiểm tra jQuery và khắc phục nếu cần
      if (window.jQuery && window.jQuery.fn && typeof window.jQuery.fn._vnpayPatched === 'undefined') {
        const originalReady = window.jQuery.fn.ready;
        window.jQuery.fn.ready = function(fn) {
          return originalReady.call(this, function() {
            try {
              window.timer = window.timer || null;
              return fn.apply(this, arguments);
            } catch (e) {
              console.error('[VNPay Patch] Error in jQuery ready handler (from interval):', e);
              return null;
            }
          });
        };
        // Đánh dấu jQuery đã được patch
        window.jQuery.fn._vnpayPatched = true;
        console.log('[VNPay Patch] Reapplied jQuery patch');
      }

      // Kiểm tra các iframe có thể chứa nội dung VNPay
      const iframes = document.querySelectorAll('iframe');
      iframes.forEach(iframe => {
        try {
          if (iframe.src && iframe.src.includes('vnpay')) {
            const iframeWindow = iframe.contentWindow;
            if (iframeWindow && iframeWindow.document) {
              // Khắc phục timer trong iframe
              if (typeof iframeWindow.timer === 'undefined') {
                iframeWindow.timer = null;
              }

              // Khắc phục updateTime trong iframe
              if (typeof iframeWindow.updateTime !== 'function') {
                iframeWindow.updateTime = function() { return null; };
              }

              console.log('[VNPay Patch] Applied fixes to VNPay iframe');
            }
          }
        } catch (e) {
          // Bỏ qua lỗi cross-origin
        }
      });
    } catch (e) {
      console.error('[VNPay Patch] Error in interval check:', e);
    }
  }, 500); // Kiểm tra thường xuyên hơn (500ms thay vì 1000ms)

  console.log('[VNPay Patch] Applied all patches');

  // Return cleanup function
  return () => {
    restoreConsoleError();
    if (cleanupCsp) cleanupCsp();
    clearInterval(intervalId);
    window.removeEventListener('message', handleIframeLoad);

    // Xóa các script đã thêm vào nếu cần
    if (typeof document !== 'undefined') {
      // Xóa script VNPay nếu tồn tại
      const vnpayScripts = document.querySelectorAll('script[src*="vnpay"]');
      vnpayScripts.forEach(script => {
        script.parentNode.removeChild(script);
      });

      console.log('[VNPay Patch] Đã xóa các script VNPay');
    }

    console.log('[VNPay Patch] Cleaned up all patches');
  };
};

/**
 * Create an intermediate page that applies patches and then redirects to VNPay
 * This ensures patches are applied in the VNPay domain context
 */
export const createVNPayIntermediatePage = (vnpayUrl) => {
  const patchScript = `
    <script>
      // ULTRA-CRITICAL: Apply patches immediately when page loads
      (function() {
        console.log('[VNPay Intermediate] Applying ULTRA patches...');

        // Force define timer with maximum protection
        window.timer = null;
        try {
          Object.defineProperty(window, 'timer', {
            value: null,
            writable: true,
            configurable: false,
            enumerable: false
          });
        } catch (e) {
          window.timer = null;
        }

        // Force define updateTime with maximum protection
        window.updateTime = function() {
          try {
            if (typeof window.timer === 'undefined') {
              window.timer = null;
            }
            console.log('[VNPay Intermediate] Safe updateTime called');
            return null;
          } catch (error) {
            console.error('[VNPay Intermediate] Error in updateTime:', error);
            window.timer = null;
            return null;
          }
        };

        // Protect updateTime from being overwritten
        try {
          Object.defineProperty(window, 'updateTime', {
            value: window.updateTime,
            writable: false,
            configurable: false,
            enumerable: false
          });
        } catch (e) {
          // Fallback if defineProperty fails
        }

        // Define all timer-related variables
        var timerVars = ['countdown', 'timeLeft', 'sessionTimeout', 'timeoutId'];
        timerVars.forEach(function(varName) {
          if (typeof window[varName] === 'undefined') {
            window[varName] = null;
            try {
              Object.defineProperty(window, varName, {
                value: null,
                writable: true,
                configurable: false,
                enumerable: false
              });
            } catch (e) {
              window[varName] = null;
            }
          }
        });

        // Define timer functions
        var timerFuncs = ['startTimer', 'stopTimer', 'resetTimer'];
        timerFuncs.forEach(function(funcName) {
          if (typeof window[funcName] === 'undefined') {
            window[funcName] = function() {
              if (typeof window.timer === 'undefined') window.timer = null;
              console.log('[VNPay Intermediate] Safe ' + funcName + ' called');
              return null;
            };
          }
        });

        // Override document.addEventListener to catch DOMContentLoaded
        var originalAddEventListener = document.addEventListener;
        document.addEventListener = function(type, listener, options) {
          if (type === 'DOMContentLoaded') {
            var wrappedListener = function(event) {
              // Ensure timer exists before any DOMContentLoaded handlers
              if (typeof window.timer === 'undefined') {
                window.timer = null;
                console.log('[VNPay Intermediate] Timer re-forced in DOMContentLoaded');
              }
              try {
                return listener.call(this, event);
              } catch (e) {
                console.error('[VNPay Intermediate] Error in DOMContentLoaded handler:', e);
                return null;
              }
            };
            return originalAddEventListener.call(this, type, wrappedListener, options);
          }
          return originalAddEventListener.call(this, type, listener, options);
        };

        // Monitor for script loading
        var observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              mutation.addedNodes.forEach(function(node) {
                if (node.tagName === 'SCRIPT' && node.src) {
                  var src = node.src.toLowerCase();
                  if (src.includes('custom.min.js') ||
                      src.includes('jquery.bundles.js') ||
                      src.includes('vnpay')) {

                    console.log('[VNPay Intermediate] Detected VNPay script:', node.src);

                    // Reapply patches immediately
                    if (typeof window.timer === 'undefined') {
                      window.timer = null;
                      console.log('[VNPay Intermediate] Timer reapplied for script:', node.src);
                    }

                    // Add load event listener
                    node.addEventListener('load', function() {
                      setTimeout(function() {
                        if (typeof window.timer === 'undefined') {
                          window.timer = null;
                          console.log('[VNPay Intermediate] Timer reapplied after script load');
                        }
                      }, 10);
                    });
                  }
                }
              });
            }
          });
        });

        observer.observe(document, { childList: true, subtree: true });

        // Periodic timer check
        var checkTimer = function() {
          if (typeof window.timer === 'undefined') {
            window.timer = null;
            console.log('[VNPay Intermediate] Timer restored by periodic check');
          }
        };

        var timerCheckInterval = setInterval(checkTimer, 100); // Check every 100ms

        // Stop checking after 30 seconds
        setTimeout(function() {
          clearInterval(timerCheckInterval);
          observer.disconnect();
          console.log('[VNPay Intermediate] Monitoring stopped');
        }, 30000);

        console.log('[VNPay Intermediate] ULTRA patches applied successfully');
      })();

      // Auto-redirect to VNPay after patches are applied
      setTimeout(function() {
        console.log('[VNPay Intermediate] Redirecting to VNPay...');
        window.location.href = '${vnpayUrl}';
      }, 1000);
    </script>
  `;

  const htmlContent = `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đang chuyển hướng đến VNPay...</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; connect-src 'self' https:;">
    ${patchScript}
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-top: 5px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .progress {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 20px;
        }
        .progress-bar {
            width: 0%;
            height: 100%;
            background: white;
            border-radius: 2px;
            animation: progress 3s ease-in-out forwards;
        }
        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Đang chuẩn bị thanh toán</h2>
        <p>Vui lòng đợi trong giây lát...</p>
        <p><small>Đang áp dụng các bản vá bảo mật cho VNPay</small></p>
        <div class="progress">
            <div class="progress-bar"></div>
        </div>
    </div>
</body>
</html>
  `;

  return htmlContent;
};

/**
 * Enhanced VNPay redirect with intermediate page approach
 */
export const redirectToVNPayWithPatches = (vnpayUrl) => {
  console.log('[VNPay Enhanced] Creating intermediate page for VNPay redirect...');

  try {
    // Create intermediate page with patches
    const intermediatePageContent = createVNPayIntermediatePage(vnpayUrl);

    // Create blob URL for intermediate page
    const blob = new Blob([intermediatePageContent], { type: 'text/html' });
    const blobUrl = URL.createObjectURL(blob);

    console.log('[VNPay Enhanced] Intermediate page created, redirecting...');

    // Redirect to intermediate page
    window.location.href = blobUrl;

    // Clean up blob URL after redirect
    setTimeout(() => {
      URL.revokeObjectURL(blobUrl);
    }, 5000);

  } catch (error) {
    console.error('[VNPay Enhanced] Error creating intermediate page:', error);

    // Fallback: direct redirect with basic patches
    console.warn('[VNPay Enhanced] Using fallback direct redirect...');

    // Apply basic patches first
    if (typeof window.timer === 'undefined') {
      window.timer = null;
    }
    if (typeof window.updateTime === 'undefined') {
      window.updateTime = function() { return null; };
    }

    // Direct redirect
    window.location.href = vnpayUrl;
  }
};

/**
 * Setup VNPay error filtering to ignore cosmetic timer errors
 */
export const setupVNPayErrorFiltering = () => {
  console.log('[VNPay Error Filter] Setting up VNPay error filtering...');

  // Check if already set up
  if (window.__vnpayErrorFilterSetup) {
    console.log('[VNPay Error Filter] Already set up, skipping...');
    return;
  }

  const isVNPayError = (error) => {
    if (!error) return false;

    const errorIndicators = [
      'timer is not defined',
      'vnpayment.vn',
      'custom.min.js',
      'jquery.bundles.js',
      'updateTime'
    ];

    return errorIndicators.some(indicator =>
      (error.message && error.message.includes(indicator)) ||
      (error.filename && error.filename.includes(indicator)) ||
      (error.stack && error.stack.includes(indicator))
    );
  };

  // Override window.onerror
  const originalOnError = window.onerror;
  window.onerror = function(message, filename, lineno, colno, error) {
    if (isVNPayError({ message, filename, error })) {
      console.warn('[VNPay Error Filter] Filtered VNPay cosmetic error:', message);
      return true; // Prevent default error handling
    }

    // Call original handler for non-VNPay errors
    if (originalOnError) {
      return originalOnError.call(this, message, filename, lineno, colno, error);
    }

    return false;
  };

  // Override addEventListener for 'error' events
  const originalAddEventListener = window.addEventListener;
  window.addEventListener = function(type, listener, options) {
    if (type === 'error') {
      const wrappedListener = function(event) {
        if (isVNPayError(event.error || event)) {
          console.warn('[VNPay Error Filter] Filtered VNPay error event:', event.message || event.error?.message);
          event.preventDefault();
          event.stopPropagation();
          return false;
        }

        return listener.call(this, event);
      };

      return originalAddEventListener.call(this, type, wrappedListener, options);
    }

    return originalAddEventListener.call(this, type, listener, options);
  };

  // Mark as set up
  window.__vnpayErrorFilterSetup = true;
  console.log('[VNPay Error Filter] VNPay error filtering set up successfully');
};

export default applyVNPayPatches;
