const { verifyEmailConnection, sendVerificationEmail } = require('../utils/emailUtils');

// @desc    Kiểm tra kết nối SMTP
// @route   GET /api/admin/email/test-connection
// @access  Admin
const testEmailConnection = async (req, res) => {
  try {
    const result = await verifyEmailConnection();
    
    if (result.success) {
      return res.status(200).json({
        success: true,
        message: 'Kết nối SMTP thành công',
        data: result
      });
    } else {
      return res.status(500).json({
        success: false,
        message: 'Không thể kết nối đến máy chủ SMTP',
        error: result.error,
        code: result.code,
        responseCode: result.responseCode
      });
    }
  } catch (error) {
    console.error('Lỗi kiểm tra kết nối SMTP:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Gửi email test
// @route   POST /api/admin/email/send-test
// @access  Admin
const sendTestEmail = async (req, res) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Vui lòng cung cấp địa chỉ email'
      });
    }
    
    // Tạo dữ liệu người dùng giả
    const testUser = {
      email,
      fullName: 'Admin Test'
    };
    
    // Tạo URL xác nhận giả
    const testUrl = `${process.env.BACKEND_URL}/api/auth/test-email`;
    
    // Gửi email test
    const emailResult = await sendVerificationEmail(testUser, testUrl);
    
    if (emailResult.success) {
      return res.status(200).json({
        success: true,
        message: `Email test đã được gửi thành công đến ${email}`,
        data: emailResult
      });
    } else {
      return res.status(500).json({
        success: false,
        message: 'Không thể gửi email test',
        error: emailResult.error,
        code: emailResult.code,
        responseCode: emailResult.responseCode
      });
    }
  } catch (error) {
    console.error('Lỗi gửi email test:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  testEmailConnection,
  sendTestEmail
};
