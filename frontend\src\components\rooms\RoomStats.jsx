import { FaHome, FaCheckCircle, FaTimesCircle, FaEyeSlash } from 'react-icons/fa';

const RoomStats = ({ stats }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      {/* Tổng số phòng */}
      <div className="bg-white rounded-lg shadow-md p-6 flex items-center">
        <div className="rounded-full h-12 w-12 flex items-center justify-center bg-primary-light">
          <FaHome className="h-6 w-6 text-primary" />
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-500">Tổng số phòng</h3>
          <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
        </div>
      </div>

      {/* Phòng còn trống */}
      <div className="bg-white rounded-lg shadow-md p-6 flex items-center">
        <div className="rounded-full h-12 w-12 flex items-center justify-center bg-green-100">
          <FaCheckCircle className="h-6 w-6 text-green-600" />
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-500">Còn trống</h3>
          <p className="text-2xl font-semibold text-gray-900">{stats.available}</p>
        </div>
      </div>

      {/* Phòng đã cho thuê */}
      <div className="bg-white rounded-lg shadow-md p-6 flex items-center">
        <div className="rounded-full h-12 w-12 flex items-center justify-center bg-blue-100">
          <FaTimesCircle className="h-6 w-6 text-blue-600" />
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-500">Đã cho thuê</h3>
          <p className="text-2xl font-semibold text-gray-900">{stats.rented}</p>
        </div>
      </div>

      {/* Phòng đã ẩn */}
      <div className="bg-white rounded-lg shadow-md p-6 flex items-center">
        <div className="rounded-full h-12 w-12 flex items-center justify-center bg-gray-100">
          <FaEyeSlash className="h-6 w-6 text-gray-600" />
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-500">Đã ẩn</h3>
          <p className="text-2xl font-semibold text-gray-900">{stats.hidden}</p>
        </div>
      </div>
    </div>
  );
};

export default RoomStats;
