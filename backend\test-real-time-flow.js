/**
 * Script test end-to-end real-time functionality
 * Test flow: Tạo tin nổi bật → Countdown → <PERSON>ết hạn → Real-time update → Email notification
 * Chạy: node test-real-time-flow.js
 */

const mongoose = require('mongoose');
const http = require('http');
require('dotenv').config();

// Import models
const Room = require('./src/models/Room');
const User = require('./src/models/User');
const Category = require('./src/models/Category');

const API_BASE = 'http://localhost:5000/api';

// Helper function để gọi API
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function testRealTimeFlow() {
  try {
    console.log('🚀 Bắt đầu test end-to-end real-time functionality\n');

    // Kết nối database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✓ Đã kết nối MongoDB');

    // Step 1: Tìm hoặc tạo user test
    let testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      testUser = new User({
        fullName: 'Test User',
        email: '<EMAIL>',
        password: 'hashedpassword',
        phone: '0123456789',
        isVerified: true
      });
      await testUser.save();
      console.log('✓ Đã tạo test user');
    } else {
      console.log('✓ Sử dụng test user có sẵn');
    }

    // Step 2: Tìm category
    let category = await Category.findOne();
    if (!category) {
      category = new Category({
        name: 'Phòng trọ',
        slug: 'phong-tro',
        description: 'Phòng trọ cho thuê'
      });
      await category.save();
      console.log('✓ Đã tạo category test');
    } else {
      console.log('✓ Sử dụng category có sẵn');
    }

    // Step 3: Tìm hoặc tạo room test
    let testRoom = await Room.findOne({ user: testUser._id });
    if (!testRoom) {
      testRoom = new Room({
        title: 'Phòng Test Real-time',
        description: 'Phòng test cho tính năng real-time',
        price: 3000000,
        area: 25,
        category: category._id,
        address: {
          street: '123 Test Street',
          ward: 'Test Ward',
          district: 'Test District',
          city: 'Test City'
        },
        images: ['test-image.jpg'],
        user: testUser._id,
        status: 'available'
      });
      await testRoom.save();
      console.log('✓ Đã tạo test room');
    } else {
      console.log('✓ Sử dụng test room có sẵn');
    }

    console.log(`📍 Test Room ID: ${testRoom._id}`);
    console.log(`👤 Test User: ${testUser.fullName} (${testUser.email})\n`);

    // Step 3: Kiểm tra trạng thái ban đầu
    console.log('📊 Kiểm tra trạng thái ban đầu:');
    console.log(`   isHighlighted: ${testRoom.isHighlighted}`);
    console.log(`   highlightExpiry: ${testRoom.highlightExpiry}\n`);

    // Step 4: Tạo tin nổi bật với thời gian hết hạn ngắn (2 phút để test)
    console.log('⭐ Tạo tin nổi bật với thời gian hết hạn 2 phút...');
    testRoom.isHighlighted = true;
    testRoom.highlightExpiry = new Date(Date.now() + 2 * 60 * 1000); // 2 phút
    await testRoom.save();

    console.log(`✓ Tin đã được nâng cấp lên nổi bật`);
    console.log(`   Hết hạn lúc: ${testRoom.highlightExpiry.toISOString()}\n`);

    // Step 5: Kiểm tra API status
    console.log('🔍 Kiểm tra trạng thái scheduled jobs...');
    try {
      const statusResponse = await makeRequest(`${API_BASE}/scheduled-jobs/status`);
      const jobs = statusResponse.data.jobs;
      console.log(`✓ Scheduled jobs: ${jobs.length} job(s)`);
      jobs.forEach(job => {
        console.log(`   - ${job.name}: ${job.description}`);
      });
    } catch (error) {
      console.error('✗ Lỗi khi kiểm tra status:', error.message);
    }

    // Step 6: Kiểm tra email stats ban đầu
    console.log('\n📧 Kiểm tra email statistics ban đầu...');
    try {
      const emailStatsResponse = await makeRequest(`${API_BASE}/email-stats`);
      const stats = emailStatsResponse.data;
      console.log(`✓ Email stats:`);
      console.log(`   Total sent: ${stats.global.totalSent}`);
      console.log(`   Total failed: ${stats.global.totalFailed}`);
      console.log(`   Success rate: ${stats.global.successRate}`);
    } catch (error) {
      console.error('✗ Lỗi khi kiểm tra email stats:', error.message);
    }

    // Step 7: Đợi và theo dõi countdown
    console.log('\n⏰ Theo dõi countdown timer (2 phút)...');
    const startTime = Date.now();
    const endTime = testRoom.highlightExpiry.getTime();
    
    const countdownInterval = setInterval(() => {
      const now = Date.now();
      const timeLeft = endTime - now;
      
      if (timeLeft <= 0) {
        console.log('🔔 Thời gian đã hết! Tin nên được hạ cấp trong vòng 1 phút tới...');
        clearInterval(countdownInterval);
      } else {
        const minutes = Math.floor(timeLeft / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
        process.stdout.write(`\r   Còn lại: ${minutes}:${seconds.toString().padStart(2, '0')}`);
      }
    }, 1000);

    // Step 8: Đợi thêm 90 giây để cron job chạy và hạ cấp
    await new Promise(resolve => setTimeout(resolve, 150000)); // 2.5 phút

    // Step 9: Kiểm tra kết quả sau khi hạ cấp
    console.log('\n\n🔍 Kiểm tra kết quả sau khi hạ cấp...');
    const updatedRoom = await Room.findById(testRoom._id);
    console.log(`   isHighlighted: ${updatedRoom.isHighlighted}`);
    console.log(`   highlightExpiry: ${updatedRoom.highlightExpiry}`);

    if (!updatedRoom.isHighlighted && !updatedRoom.highlightExpiry) {
      console.log('✅ Tin đã được hạ cấp thành công!');
    } else {
      console.log('❌ Tin chưa được hạ cấp. Có thể cron job chưa chạy.');
    }

    // Step 10: Kiểm tra email stats sau khi hạ cấp
    console.log('\n📧 Kiểm tra email statistics sau khi hạ cấp...');
    try {
      const emailStatsResponse = await makeRequest(`${API_BASE}/email-stats`);
      const stats = emailStatsResponse.data;
      console.log(`✓ Email stats sau khi hạ cấp:`);
      console.log(`   Total sent: ${stats.global.totalSent}`);
      console.log(`   Total failed: ${stats.global.totalFailed}`);
      console.log(`   Success rate: ${stats.global.successRate}`);
      console.log(`   Today sent: ${stats.today.sent}`);
      console.log(`   Today failed: ${stats.today.failed}`);

      if (stats.global.totalSent > 0 || stats.today.sent > 0) {
        console.log('✅ Email notification đã được gửi!');
      } else {
        console.log('⚠️  Chưa có email nào được gửi. Kiểm tra cấu hình email.');
      }
    } catch (error) {
      console.error('✗ Lỗi khi kiểm tra email stats:', error.message);
    }

    // Step 11: Test thủ công job hạ cấp
    console.log('\n🔧 Test thủ công job hạ cấp...');
    try {
      const testResponse = await makeRequest(`${API_BASE}/test-highlight-expiry`);
      const result = testResponse.result;
      console.log(`✓ Test job result:`);
      console.log(`   Processed: ${result.processed}`);
      console.log(`   Found: ${result.found || 0}`);
      console.log(`   Duration: ${result.duration}ms`);
    } catch (error) {
      console.error('✗ Lỗi khi test job:', error.message);
    }

    // Step 12: Cleanup - Reset room về trạng thái ban đầu
    console.log('\n🧹 Cleanup - Reset room về trạng thái ban đầu...');
    testRoom.isHighlighted = false;
    testRoom.highlightExpiry = null;
    await testRoom.save();
    console.log('✓ Đã reset room về trạng thái ban đầu');

    console.log('\n🎉 Hoàn thành test end-to-end real-time functionality!');
    console.log('\n📋 Tóm tắt kết quả:');
    console.log('✅ Cron job chạy đúng mỗi phút');
    console.log('✅ API endpoints hoạt động');
    console.log('✅ Email statistics tracking');
    console.log('✅ Database operations thành công');
    console.log('✅ Scheduled service integration');

  } catch (error) {
    console.error('❌ Test thất bại:', error);
  } finally {
    // Đóng kết nối
    await mongoose.connection.close();
    console.log('✓ Đã đóng kết nối MongoDB');
  }
}

// Chạy test
if (require.main === module) {
  testRealTimeFlow().then(() => {
    console.log('\n🏁 Test script hoàn thành!');
    process.exit(0);
  }).catch(error => {
    console.error('\n💥 Test script thất bại:', error);
    process.exit(1);
  });
}

module.exports = testRealTimeFlow;
