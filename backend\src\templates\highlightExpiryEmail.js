/**
 * Email template cho thông báo hết hạn tin nổi bật
 */

const createHighlightExpiryEmailTemplate = (roomData, userData) => {
  const { title, _id, address, price, images } = roomData;
  const { fullName, email } = userData;

  // Tạo địa chỉ đầy đủ
  const fullAddress = [
    address?.street,
    address?.ward,
    address?.district,
    address?.city
  ].filter(Boolean).join(', ') || 'Không có địa chỉ';

  // Format giá tiền
  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  // URL hình ảnh đầu tiên (nếu có)
  const imageUrl = images && images.length > 0 
    ? (images[0].startsWith('http') ? images[0] : `${process.env.BACKEND_URL}/uploads/${images[0]}`)
    : 'https://via.placeholder.com/300x200?text=Không+có+hình';

  const emailHTML = `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thông báo hết hạn tin nổi bật</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #e74c3c;
            margin: 0;
            font-size: 24px;
        }
        .room-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #e74c3c;
        }
        .room-image {
            width: 100%;
            max-width: 300px;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin: 15px 0;
        }
        .room-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .room-details {
            margin: 10px 0;
        }
        .room-details strong {
            color: #34495e;
        }
        .price {
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
            margin: 10px 0;
        }
        .message {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px;
        }
        .cta-button:hover {
            background-color: #2980b9;
        }
        .cta-button.primary {
            background-color: #e74c3c;
        }
        .cta-button.primary:hover {
            background-color: #c0392b;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .highlight-badge {
            background-color: #f39c12;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        .expired-badge {
            background-color: #95a5a6;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 Thông báo hết hạn tin nổi bật</h1>
        </div>

        <p>Xin chào <strong>${fullName}</strong>,</p>

        <div class="message">
            <p><strong>📢 Tin đăng phòng trọ của bạn đã hết hạn gói tin nổi bật!</strong></p>
            <p>Tin đăng đã được tự động chuyển về gói tin thường và sẽ hiển thị với độ ưu tiên thấp hơn trong kết quả tìm kiếm.</p>
        </div>

        <div class="room-info">
            <div class="expired-badge">Đã hết hạn tin nổi bật</div>
            <div class="room-title">${title}</div>
            
            <img src="${imageUrl}" alt="${title}" class="room-image" />
            
            <div class="room-details">
                <strong>📍 Địa chỉ:</strong> ${fullAddress}
            </div>
            <div class="room-details">
                <strong>🆔 Mã tin:</strong> ${_id}
            </div>
            <div class="price">
                <strong>💰 Giá thuê:</strong> ${formatPrice(price)}/tháng
            </div>
        </div>

        <div class="cta-section">
            <p><strong>Muốn tin đăng của bạn tiếp tục nổi bật?</strong></p>
            <a href="${process.env.FRONTEND_URL}/rooms/upgrade/${_id}" class="cta-button primary">
                ⭐ Nâng cấp lại tin nổi bật
            </a>
            <a href="${process.env.FRONTEND_URL}/my-rooms" class="cta-button">
                📋 Quản lý tin đăng
            </a>
        </div>

        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="color: #2c3e50; margin-top: 0;">💡 Lợi ích của tin nổi bật:</h3>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Hiển thị ưu tiên đầu tiên trong kết quả tìm kiếm</li>
                <li>Badge đặc biệt thu hút sự chú ý</li>
                <li>Tăng cơ hội được thuê nhanh chóng</li>
                <li>Countdown timer tạo cảm giác cấp bách</li>
            </ul>
        </div>

        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống <strong>Tìm Phòng Trọ</strong></p>
            <p>Nếu bạn có thắc mắc, vui lòng liên hệ: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p style="margin-top: 15px; font-size: 12px; color: #999;">
                © 2025 Hệ thống Tìm Phòng Trọ. Tất cả quyền được bảo lưu.
            </p>
        </div>
    </div>
</body>
</html>`;

  return {
    subject: `🏠 Tin đăng "${title}" đã hết hạn gói tin nổi bật`,
    html: emailHTML,
    text: `
Xin chào ${fullName},

Tin đăng phòng trọ "${title}" của bạn đã hết hạn gói tin nổi bật và đã được chuyển về gói tin thường.

Thông tin tin đăng:
- Tiêu đề: ${title}
- Địa chỉ: ${fullAddress}
- Giá thuê: ${formatPrice(price)}/tháng
- Mã tin: ${_id}

Để tiếp tục nổi bật tin đăng, vui lòng truy cập: ${process.env.FRONTEND_URL}/rooms/upgrade/${_id}

Trân trọng,
Hệ thống Tìm Phòng Trọ
    `.trim()
  };
};

module.exports = {
  createHighlightExpiryEmailTemplate
};
