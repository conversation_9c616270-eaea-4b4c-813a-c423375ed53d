const express = require('express');
const router = express.Router();
const {
  getRooms,
  getRoomById,
  createRoom,
  updateRoom,
  deleteRoom,
  getMyRooms,
  updateRoomStatus
} = require('../controllers/roomController');
const { protect } = require('../middlewares/authMiddleware');
const handleUpload = require('../middlewares/uploadMiddleware');

/**
 * @swagger
 * tags:
 *   name: Rooms
 *   description: API quản lý phòng trọ
 */

/**
 * @swagger
 * /rooms:
 *   get:
 *     summary: L<PERSON>y danh sách phòng trọ
 *     tags: [Rooms]
 *     parameters:
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: <PERSON>i<PERSON> tối thiểu
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Giá tối đa
 *       - in: query
 *         name: minArea
 *         schema:
 *           type: number
 *         description: <PERSON>ện tích tối thiểu
 *       - in: query
 *         name: maxArea
 *         schema:
 *           type: number
 *         description: <PERSON>ện tích tối đa
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: Tỉnh/Thành phố
 *       - in: query
 *         name: district
 *         schema:
 *           type: string
 *         description: Quận/Huyện
 *       - in: query
 *         name: ward
 *         schema:
 *           type: string
 *         description: Phường/Xã
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: ID loại phòng
 *       - in: query
 *         name: amenities
 *         schema:
 *           type: string
 *         description: Danh sách ID tiện nghi, phân cách bởi dấu phẩy
 *       - in: query
 *         name: highlighted
 *         schema:
 *           type: boolean
 *         description: Lọc tin nổi bật
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Số trang
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Số lượng kết quả mỗi trang
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [newest, price_asc, price_desc, area_asc, area_desc]
 *           default: newest
 *         description: Sắp xếp kết quả
 *     responses:
 *       200:
 *         description: Danh sách phòng trọ
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Room'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                     totalPages:
 *                       type: integer
 *                       example: 5
 *                     totalResults:
 *                       type: integer
 *                       example: 45
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/', getRooms);

/**
 * @swagger
 * /rooms/my-rooms:
 *   get:
 *     summary: Lấy danh sách phòng trọ của người dùng hiện tại
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [available, rented, hidden]
 *         description: Lọc theo trạng thái
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Từ khóa tìm kiếm
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Số trang
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Số lượng kết quả mỗi trang
 *     responses:
 *       200:
 *         description: Danh sách phòng trọ của người dùng
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     rooms:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Room'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         limit:
 *                           type: integer
 *                           example: 10
 *                         total:
 *                           type: integer
 *                           example: 45
 *                         totalPages:
 *                           type: integer
 *                           example: 5
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/my-rooms', protect, getMyRooms);

/**
 * @swagger
 * /rooms/{id}:
 *   get:
 *     summary: Lấy chi tiết phòng trọ
 *     tags: [Rooms]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: ID của phòng trọ
 *     responses:
 *       200:
 *         description: Chi tiết phòng trọ
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Room'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get('/:id', getRoomById);

/**
 * @swagger
 * /rooms:
 *   post:
 *     summary: Tạo phòng trọ mới
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *               - price
 *               - area
 *               - address.street
 *               - address.ward
 *               - address.district
 *               - address.city
 *               - category
 *             properties:
 *               title:
 *                 type: string
 *                 description: Tiêu đề tin đăng
 *               description:
 *                 type: string
 *                 description: Mô tả chi tiết
 *               price:
 *                 type: number
 *                 description: Giá thuê (VNĐ/tháng)
 *               area:
 *                 type: number
 *                 description: Diện tích (m²)
 *               deposit:
 *                 type: number
 *                 description: Tiền cọc (VNĐ)
 *               address.street:
 *                 type: string
 *                 description: Tên đường
 *               address.ward:
 *                 type: string
 *                 description: Phường/Xã
 *               address.district:
 *                 type: string
 *                 description: Quận/Huyện
 *               address.city:
 *                 type: string
 *                 description: Tỉnh/Thành phố
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Hình ảnh phòng trọ (tối đa 10 hình)
 *               amenities:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Danh sách ID tiện nghi
 *               category:
 *                 type: string
 *                 description: ID loại phòng
 *     responses:
 *       201:
 *         description: Tạo phòng trọ thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Room'
 *       400:
 *         description: Dữ liệu không hợp lệ
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post('/', protect, handleUpload, createRoom);

/**
 * @swagger
 * /rooms/{id}:
 *   put:
 *     summary: Cập nhật phòng trọ
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: ID của phòng trọ
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 description: Tiêu đề tin đăng
 *               description:
 *                 type: string
 *                 description: Mô tả chi tiết
 *               price:
 *                 type: number
 *                 description: Giá thuê (VNĐ/tháng)
 *               area:
 *                 type: number
 *                 description: Diện tích (m²)
 *               deposit:
 *                 type: number
 *                 description: Tiền cọc (VNĐ)
 *               address.street:
 *                 type: string
 *                 description: Tên đường
 *               address.ward:
 *                 type: string
 *                 description: Phường/Xã
 *               address.district:
 *                 type: string
 *                 description: Quận/Huyện
 *               address.city:
 *                 type: string
 *                 description: Tỉnh/Thành phố
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Hình ảnh phòng trọ (tối đa 10 hình)
 *               existingImages:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Danh sách URL hình ảnh hiện có cần giữ lại
 *               amenities:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Danh sách ID tiện nghi
 *               category:
 *                 type: string
 *                 description: ID loại phòng
 *     responses:
 *       200:
 *         description: Cập nhật phòng trọ thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Room'
 *       400:
 *         description: Dữ liệu không hợp lệ
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.put('/:id', protect, handleUpload, updateRoom);

/**
 * @swagger
 * /rooms/{id}:
 *   delete:
 *     summary: Xóa phòng trọ
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: ID của phòng trọ
 *     responses:
 *       200:
 *         description: Xóa phòng trọ thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Xóa phòng trọ thành công
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.delete('/:id', protect, deleteRoom);

/**
 * @swagger
 * /rooms/{id}/status:
 *   put:
 *     summary: Cập nhật trạng thái phòng trọ
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: ID của phòng trọ
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [available, rented, hidden]
 *                 description: Trạng thái mới của phòng trọ
 *     responses:
 *       200:
 *         description: Cập nhật trạng thái thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Room'
 *       400:
 *         description: Dữ liệu không hợp lệ
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.put('/:id/status', protect, updateRoomStatus);

module.exports = router;
