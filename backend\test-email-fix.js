/**
 * Script test email functionality sau khi fix lỗi import
 */

const mongoose = require('mongoose');
const Room = require('./src/models/Room');
const User = require('./src/models/User');
const Category = require('./src/models/Category');
const scheduledService = require('./src/services/scheduledService');
require('dotenv').config();

async function testEmailFix() {
  try {
    console.log('🔧 Test email functionality sau khi fix lỗi import\n');

    // Kết nối database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✓ Đã kết nối MongoDB');

    // Tìm user test
    let testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      testUser = new User({
        fullName: 'Test User Email',
        email: '<EMAIL>',
        password: 'hashedpassword',
        phone: '0123456789',
        isVerified: true
      });
      await testUser.save();
      console.log('✓ Đã tạo test user');
    }

    // Tìm category
    let category = await Category.findOne();
    if (!category) {
      category = new Category({
        name: 'Phòng trọ',
        slug: 'phong-tro',
        description: 'Phòng trọ cho thuê'
      });
      await category.save();
    }

    // Tạo tin test mới với thời gian hết hạn ngắn
    const testRoom = new Room({
      title: 'Phòng Test Email Fix',
      description: 'Test email notification sau khi fix lỗi',
      price: 3000000,
      area: 25,
      category: category._id,
      address: {
        street: '123 Email Test Street',
        ward: 'Test Ward',
        district: 'Test District',
        city: 'Test City'
      },
      images: ['test-image.jpg'],
      user: testUser._id,
      status: 'available',
      isHighlighted: true,
      highlightExpiry: new Date(Date.now() - 1000) // Đã hết hạn 1 giây trước
    });

    await testRoom.save();
    console.log('✓ Đã tạo tin test với thời gian hết hạn');
    console.log(`   Room ID: ${testRoom._id}`);
    console.log(`   Hết hạn lúc: ${testRoom.highlightExpiry.toISOString()}`);
    console.log(`   Hiện tại: ${new Date().toISOString()}`);

    // Test thủ công job hạ cấp để trigger email
    console.log('\n📧 Test email notification...');
    
    try {
      const result = await scheduledService.runHighlightExpiryJobManually();
      console.log('✓ Job result:', result);
      
      if (result.processed > 0) {
        console.log('✅ Tin đã được hạ cấp và email đã được gửi!');
      } else {
        console.log('⚠️ Không có tin nào được hạ cấp');
      }
    } catch (error) {
      console.error('✗ Lỗi khi chạy job:', error.message);
    }

    // Kiểm tra email stats
    console.log('\n📊 Kiểm tra email statistics...');
    const emailStats = scheduledService.getEmailStats();
    console.log('Email stats:');
    console.log(`  Total sent: ${emailStats.global.totalSent}`);
    console.log(`  Total failed: ${emailStats.global.totalFailed}`);
    console.log(`  Success rate: ${emailStats.global.successRate}`);
    console.log(`  Today sent: ${emailStats.today.sent}`);
    console.log(`  Today failed: ${emailStats.today.failed}`);

    // Cleanup - xóa tin test
    await Room.findByIdAndDelete(testRoom._id);
    console.log('\n🧹 Đã xóa tin test');

    console.log('\n🎉 Test email fix hoàn thành!');

  } catch (error) {
    console.error('❌ Test thất bại:', error);
  } finally {
    await mongoose.connection.close();
    console.log('✓ Đã đóng kết nối MongoDB');
  }
}

// Chạy test
if (require.main === module) {
  testEmailFix().then(() => {
    console.log('\n🏁 Test script hoàn thành!');
    process.exit(0);
  }).catch(error => {
    console.error('\n💥 Test script thất bại:', error);
    process.exit(1);
  });
}

module.exports = testEmailFix;
