import Button from '../components/Button'

const AboutPage = () => {
  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary to-primary-dark text-white py-16">
        <div className="container-custom text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">V<PERSON> Chúng Tôi</h1>
          <p className="text-xl opacity-90 max-w-3xl mx-auto">
            Nền tảng kết nối người thuê và chủ nhà một cách nhanh chóng, tiện lợi và an toàn.
          </p>
        </div>
      </section>
      
      {/* Giới thiệu */}
      <section className="py-16">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6"><PERSON><PERSON><PERSON>ủa Chúng Tôi</h2>
              <p className="text-gray-600 mb-4">
                Tì<PERSON>rọ được thành lập vào năm 2023 với sứ mệnh giải quyết bài toán tìm kiếm chỗ ở cho hàng triệu người Việt Nam, đặc biệt là sinh viên và người lao động tại các thành phố lớn.
              </p>
              <p className="text-gray-600 mb-4">
                Chúng tôi nhận thấy rằng việc tìm kiếm một nơi ở phù hợp luôn là một thách thức lớn đối với nhiều người. Thông tin không đầy đủ, giá cả không minh bạch, và quá nhiều trung gian khiến cho quá trình này trở nên phức tạp và tốn thời gian.
              </p>
              <p className="text-gray-600">
                Với nền tảng công nghệ hiện đại và đội ngũ nhân viên tận tâm, chúng tôi cam kết mang đến trải nghiệm tìm kiếm chỗ ở đơn giản, minh bạch và hiệu quả cho người dùng.
              </p>
            </div>
            <div>
              <img 
                src="../assets/doingu.jpg" 
                alt="Về chúng tôi" 
                className="rounded-lg shadow-lg w-full"
              />
            </div>
          </div>
        </div>
      </section>
      
      {/* Sứ mệnh & Tầm nhìn */}
      <section className="py-16 bg-gray-50">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div className="bg-white p-8 rounded-lg shadow-md">
              <div className="w-16 h-16 bg-primary-light rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <h2 className="text-2xl font-bold mb-4">Sứ Mệnh</h2>
              <p className="text-gray-600 mb-4">
                Sứ mệnh của chúng tôi là kết nối người thuê và chủ nhà một cách hiệu quả, minh bạch và an toàn, góp phần giải quyết bài toán nhà ở tại Việt Nam.
              </p>
              <p className="text-gray-600">
                Chúng tôi cam kết mang đến những thông tin chính xác, đầy đủ và cập nhật nhất về thị trường nhà trọ, giúp người dùng đưa ra quyết định thuê nhà một cách thông minh và phù hợp với nhu cầu.
              </p>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-md">
              <div className="w-16 h-16 bg-primary-light rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
              </div>
              <h2 className="text-2xl font-bold mb-4">Tầm Nhìn</h2>
              <p className="text-gray-600 mb-4">
                Chúng tôi hướng đến việc trở thành nền tảng tìm kiếm nhà trọ hàng đầu tại Việt Nam, được tin dùng bởi hàng triệu người dùng.
              </p>
              <p className="text-gray-600">
                Trong tương lai, chúng tôi mong muốn mở rộng dịch vụ sang các lĩnh vực liên quan đến bất động sản, cung cấp giải pháp toàn diện cho nhu cầu về nhà ở của người Việt Nam.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Giá trị cốt lõi */}
      <section className="py-16">
        <div className="container-custom">
          <h2 className="text-3xl font-bold mb-12 text-center">Giá Trị Cốt Lõi</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Uy Tín & Minh Bạch</h3>
              <p className="text-gray-600">
                Chúng tôi cam kết mang đến những thông tin chính xác, minh bạch và đáng tin cậy cho người dùng.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Hiệu Quả & Tiện Lợi</h3>
              <p className="text-gray-600">
                Chúng tôi không ngừng cải tiến nền tảng để mang đến trải nghiệm tìm kiếm nhà trọ nhanh chóng và tiện lợi nhất.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Khách Hàng Là Trọng Tâm</h3>
              <p className="text-gray-600">
                Chúng tôi luôn đặt nhu cầu và lợi ích của khách hàng lên hàng đầu trong mọi quyết định.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Đội ngũ */}
      <section className="py-16 bg-gray-50">
        <div className="container-custom">
          <h2 className="text-3xl font-bold mb-12 text-center">Đội Ngũ Của Chúng Tôi</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { name: 'Nguyễn Văn A', position: 'Giám Đốc Điều Hành', image: '../assets/chandung1.jpg' },
              { name: 'Trần Thị B', position: 'Giám Đốc Sản Phẩm', image: '../assets/chandung2.jpg' },
              { name: 'Lê Văn C', position: 'Giám Đốc Kỹ Thuật', image: '../assets/chandung3.jpg' },
              { name: 'Phạm Thị D', position: 'Giám Đốc Marketing', image: '../assets/chandung4.jpg' },
            ].map((member) => (
              <div key={member.name} className="bg-white rounded-lg shadow-md overflow-hidden text-center">
                <img 
                  src={member.image} 
                  alt={member.name} 
                  className="w-full h-64 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-1">{member.name}</h3>
                  <p className="text-gray-600 mb-4">{member.position}</p>
                  <div className="flex justify-center space-x-4">
                    <a href="#" className="text-gray-400 hover:text-primary">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                      </svg>
                    </a>
                    <a href="#" className="text-gray-400 hover:text-primary">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                      </svg>
                    </a>
                    <a href="#" className="text-gray-400 hover:text-primary">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path fillRule="evenodd" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c5.51 0 10-4.48 10-10S17.51 2 12 2zm6.605 4.61a8.502 8.502 0 011.93 5.314c-.281-.054-3.101-.629-5.943-.271-.065-.141-.12-.293-.184-.445a25.416 25.416 0 00-.564-1.236c3.145-1.28 4.577-3.124 4.761-3.362zM12 3.475c2.17 0 4.154.813 5.662 2.148-.152.216-1.443 1.941-4.48 3.08-1.399-2.57-2.95-4.675-3.189-5A8.687 8.687 0 0112 3.475zm-3.633.803a53.896 53.896 0 013.167 4.935c-3.992 1.063-7.517 1.04-7.896 1.04a8.581 8.581 0 014.729-5.975zM3.453 12.01v-.26c.37.01 4.512.065 8.775-1.215.25.477.477.965.694 1.453-.109.033-.228.065-.336.098-4.404 1.42-6.747 5.303-6.942 5.629a8.522 8.522 0 01-2.19-5.705zM12 20.547a8.482 8.482 0 01-5.239-1.8c.152-.315 1.888-3.656 6.703-5.337.022-.01.033-.01.054-.022a35.318 35.318 0 011.823 6.475 8.4 8.4 0 01-3.341.684zm4.761-1.465c-.086-.52-.542-3.015-1.659-6.084 2.679-.423 5.022.271 5.314.369a8.468 8.468 0 01-3.655 5.715z" clipRule="evenodd" />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* CTA */}
      <section className="py-16 bg-primary text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl font-bold mb-4">Bạn muốn tham gia cùng chúng tôi?</h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Chúng tôi luôn tìm kiếm những người tài năng và đam mê để cùng xây dựng nền tảng tìm kiếm nhà trọ tốt nhất Việt Nam.
          </p>
          <Button variant="secondary" size="lg">
            Xem Cơ Hội Nghề Nghiệp
          </Button>
        </div>
      </section>
    </div>
  )
}

export default AboutPage
