import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FaHome } from 'react-icons/fa';

const NotFound404 = () => {
  // Set page title for SEO
  useEffect(() => {
    document.title = '404 - Trang không tìm thấy | Room Finder';
    
    // Set meta description for SEO
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 'Trang bạn đang tìm kiếm không tồn tại. Quay lại trang chủ để tìm phòng trọ phù hợp.');
    } else {
      const meta = document.createElement('meta');
      meta.name = 'description';
      meta.content = 'Trang bạn đang tìm kiếm không tồn tại. Quay lại trang chủ để tìm phòng trọ phù hợp.';
      document.getElementsByTagName('head')[0].appendChild(meta);
    }

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'Room Finder - Tìm phòng trọ';
    };
  }, []);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full text-center space-y-8">
        {/* 404 Number */}
        <div className="space-y-4">
          <h1 className="text-6xl sm:text-8xl lg:text-9xl font-bold text-gray-900 tracking-tight">
            4<span className="text-blue-600">0</span>4
          </h1>
          
          {/* Error Message */}
          <div className="space-y-2">
            <h2 className="text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900">
              Trang không tìm thấy
            </h2>
            <p className="text-base sm:text-lg text-gray-600 max-w-md mx-auto">
              Xin lỗi, trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển.
            </p>
          </div>
        </div>

        {/* Home Button */}
        <div className="pt-4">
          <Link
            to="/"
            className="inline-flex items-center justify-center px-6 py-3 text-base font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200 ease-in-out"
          >
            <FaHome className="mr-2 h-5 w-5" />
            Về trang chủ
          </Link>
        </div>

        {/* Additional Help Text */}
        <div className="pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Nếu bạn cho rằng đây là lỗi, vui lòng{' '}
            <Link 
              to="/about" 
              className="text-blue-600 hover:text-blue-500 font-medium underline"
            >
              liên hệ với chúng tôi
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound404;
