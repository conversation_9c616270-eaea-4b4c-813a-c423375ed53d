import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { FaUpload, FaTrash, FaSpinner } from 'react-icons/fa';
import { useRooms, useCategories, useAmenities, useLocations } from '../hooks';
import { useAuthContext } from '../contexts';
import {
  Stepper,
  StepperContent,
  StepperNavigation,
  RoomPreview
} from '../components';

const CreateRoomPage = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthContext();
  const { createRoom, isLoading } = useRooms();
  const { fetchCategories, categories } = useCategories();
  const { fetchAmenities, amenities } = useAmenities();

  // Sử dụng hook useLocations để lấy dữ liệu địa chỉ hành chính
  const {
    provinces,
    districts,
    wards,
    isLoading: isLocationLoading,
    error: locationError,
    fetchProvinces,
    fetchDistrictsByProvince,
    fetchWardsByDistrict
  } = useLocations();

  // State cho stepper
  const [currentStep, setCurrentStep] = useState(0);
  const steps = ['Thông tin cơ bản', 'Địa chỉ', 'Tiện nghi', 'Hình ảnh', 'Xem trước'];

  const [images, setImages] = useState([]);
  const [previewImages, setPreviewImages] = useState([]);
  const [stepValidated, setStepValidated] = useState([false, false, false, false, true]);

  const { register, handleSubmit, formState: { errors }, setValue, watch, trigger, getValues } = useForm({
    defaultValues: {
      title: '',
      description: '',
      price: '',
      area: '',
      deposit: '',
      address: {
        street: '',
        ward: '',
        district: '',
        city: '',
      },
      category: '',
      amenities: [],
    },
    mode: 'onChange',
  });

  // Kiểm tra xác thực
  useEffect(() => {
    if (!isAuthenticated) {
      toast.error('Bạn cần đăng nhập để đăng tin phòng trọ');
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // Lấy danh sách loại phòng, tiện nghi và tỉnh/thành phố
  useEffect(() => {
    const loadData = async () => {
      try {
        await Promise.all([
          fetchCategories(),
          fetchAmenities(),
          fetchProvinces(),
        ]);
      } catch (err) {
        toast.error('Có lỗi xảy ra khi tải dữ liệu');
      }
    };

    loadData();
  }, [fetchCategories, fetchAmenities, fetchProvinces]);

  // Xử lý khi chọn tỉnh/thành phố
  const handleProvinceChange = async (e) => {
    const provinceName = e.target.value;
    setValue('address.city', provinceName);
    setValue('address.district', '');
    setValue('address.ward', '');

    if (provinceName) {
      try {
        await fetchDistrictsByProvince(provinceName);
      } catch (err) {
        console.error('Lỗi khi lấy danh sách quận/huyện:', err);
      }
    }
  };

  // Xử lý khi chọn quận/huyện
  const handleDistrictChange = async (e) => {
    const districtName = e.target.value;
    setValue('address.district', districtName);
    setValue('address.ward', '');

    if (districtName && watch('address.city')) {
      try {
        await fetchWardsByDistrict(watch('address.city'), districtName);
      } catch (err) {
        console.error('Lỗi khi lấy danh sách phường/xã:', err);
      }
    }
  };

  // Xử lý chuyển bước
  const handleNext = () => {
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  };

  const handlePrev = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const handleStepClick = (stepIndex) => {
    // Chỉ cho phép chuyển đến các bước đã được xác thực
    if (stepValidated.slice(0, stepIndex).every(Boolean)) {
      setCurrentStep(stepIndex);
    } else {
      toast.warning('Vui lòng hoàn thành các bước trước đó');
    }
  };

  // Xác thực từng bước
  const validateStep = async (step) => {
    let isValid = false;

    switch (step) {
      case 0: // Thông tin cơ bản
        isValid = await trigger(['title', 'description', 'price', 'area', 'category']);
        break;
      case 1: // Địa chỉ
        isValid = await trigger(['address.street', 'address.ward', 'address.district', 'address.city']);
        break;
      case 2: // Tiện nghi
        isValid = true; // Tiện nghi không bắt buộc
        break;
      case 3: // Hình ảnh
        isValid = images.length > 0;
        if (!isValid) {
          toast.error('Vui lòng tải lên ít nhất 1 hình ảnh');
        }
        break;
      case 4: // Xem trước
        isValid = true; // Bước xem trước luôn hợp lệ
        break;
      default:
        isValid = false;
    }

    // Cập nhật trạng thái xác thực của bước
    if (isValid) {
      const newStepValidated = [...stepValidated];
      newStepValidated[step] = true;
      setStepValidated(newStepValidated);
    }

    return isValid;
  };

  // Xử lý khi chọn hình ảnh
  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);

    if (files.length + images.length > 10) {
      toast.error('Bạn chỉ có thể tải lên tối đa 10 hình ảnh');
      return;
    }

    // Thêm hình ảnh mới vào state
    setImages([...images, ...files]);

    // Tạo URL cho hình ảnh xem trước
    const newPreviewImages = files.map(file => URL.createObjectURL(file));
    setPreviewImages([...previewImages, ...newPreviewImages]);
  };

  // Xóa hình ảnh
  const removeImage = (index) => {
    const updatedImages = [...images];
    const updatedPreviews = [...previewImages];

    // Giải phóng URL đối tượng để tránh rò rỉ bộ nhớ
    URL.revokeObjectURL(updatedPreviews[index]);

    updatedImages.splice(index, 1);
    updatedPreviews.splice(index, 1);

    setImages(updatedImages);
    setPreviewImages(updatedPreviews);
  };

  // Xử lý khi submit form
  const onSubmit = async (data) => {
    if (images.length === 0) {
      toast.error('Vui lòng tải lên ít nhất 1 hình ảnh');
      return;
    }

    try {
      // Chuyển đổi giá trị số từ chuỗi sang số
      const numericData = {
        ...data,
        price: parseFloat(data.price),
        area: parseFloat(data.area),
      };

      if (data.deposit) {
        numericData.deposit = parseFloat(data.deposit);
      }

      // Thêm hình ảnh vào dữ liệu gửi đi
      const formData = {
        ...numericData,
        images,
      };

      console.log('Submitting form data:', formData);

      // Hiển thị toast thông báo đang xử lý
      const loadingToast = toast.loading('Đang xử lý...');

      try {
        const response = await createRoom(formData);

        // Cập nhật toast thành công
        toast.update(loadingToast, {
          render: 'Đăng tin thành công!',
          type: 'success',
          isLoading: false,
          autoClose: 3000
        });

        if (response.success) {
          // Chuyển hướng sau khi toast hiển thị
          setTimeout(() => {
            navigate(`/rooms/${response.data._id}`);
          }, 1000);
        }
      } catch (error) {
        console.error('Error submitting form:', error);

        // Xác định thông báo lỗi cụ thể
        let errorMessage = 'Đăng tin thất bại. Vui lòng thử lại.';

        if (error.message) {
          errorMessage = error.message;
        }

        // Kiểm tra lỗi liên quan đến upload file
        if (error.message && error.message.includes('file')) {
          errorMessage = 'Lỗi khi tải lên hình ảnh. Vui lòng kiểm tra kích thước và định dạng file.';
        }

        // Kiểm tra lỗi liên quan đến thư mục uploads
        if (error.message && error.message.includes('uploads')) {
          errorMessage = 'Lỗi khi lưu hình ảnh. Vui lòng thử lại sau.';
        }

        // Kiểm tra lỗi validation
        if (error.errors && Array.isArray(error.errors) && error.errors.length > 0) {
          errorMessage = `Lỗi dữ liệu: ${error.errors.join(', ')}`;
        }

        // Cập nhật toast thất bại
        toast.update(loadingToast, {
          render: errorMessage,
          type: 'error',
          isLoading: false,
          autoClose: 5000
        });
      }
    } catch (error) {
      toast.error('Có lỗi xảy ra khi xử lý dữ liệu form. Vui lòng thử lại.');
      console.error('Error processing form data:', error);
    }
  };

  // Giải phóng URL đối tượng khi component unmount
  useEffect(() => {
    return () => {
      previewImages.forEach(url => URL.revokeObjectURL(url));
    };
  }, [previewImages]);

  // Render nội dung của từng bước
  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Thông tin cơ bản
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="title" className="form-label">
                Tiêu đề <span className="text-red-500">*</span>
              </label>
              <input
                id="title"
                type="text"
                className={`form-input ${errors.title ? 'border-red-500' : ''}`}
                placeholder="Nhập tiêu đề tin đăng"
                {...register('title', {
                  required: 'Tiêu đề là bắt buộc',
                  minLength: {
                    value: 10,
                    message: 'Tiêu đề phải có ít nhất 10 ký tự',
                  },
                  maxLength: {
                    value: 100,
                    message: 'Tiêu đề không được vượt quá 100 ký tự',
                  },
                })}
              />
              {errors.title && (
                <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="description" className="form-label">
                Mô tả chi tiết <span className="text-red-500">*</span>
              </label>
              <textarea
                id="description"
                rows="5"
                className={`form-input ${errors.description ? 'border-red-500' : ''}`}
                placeholder="Mô tả chi tiết về phòng trọ"
                {...register('description', {
                  required: 'Mô tả chi tiết là bắt buộc',
                  minLength: {
                    value: 30,
                    message: 'Mô tả phải có ít nhất 30 ký tự',
                  },
                })}
              ></textarea>
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="price" className="form-label">
                  Giá thuê (VNĐ/tháng) <span className="text-red-500">*</span>
                </label>
                <input
                  id="price"
                  type="number"
                  className={`form-input ${errors.price ? 'border-red-500' : ''}`}
                  placeholder="Nhập giá thuê"
                  {...register('price', {
                    required: 'Giá thuê là bắt buộc',
                    min: {
                      value: 100000,
                      message: 'Giá thuê phải từ 100,000 VNĐ trở lên',
                    },
                  })}
                />
                {errors.price && (
                  <p className="mt-1 text-sm text-red-600">{errors.price.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="area" className="form-label">
                  Diện tích (m²) <span className="text-red-500">*</span>
                </label>
                <input
                  id="area"
                  type="number"
                  className={`form-input ${errors.area ? 'border-red-500' : ''}`}
                  placeholder="Nhập diện tích"
                  {...register('area', {
                    required: 'Diện tích là bắt buộc',
                    min: {
                      value: 1,
                      message: 'Diện tích phải lớn hơn 0',
                    },
                  })}
                />
                {errors.area && (
                  <p className="mt-1 text-sm text-red-600">{errors.area.message}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="deposit" className="form-label">
                Tiền cọc (VNĐ)
              </label>
              <input
                id="deposit"
                type="number"
                className="form-input"
                placeholder="Nhập tiền cọc (nếu có)"
                {...register('deposit')}
              />
            </div>

            <div>
              <label htmlFor="category" className="form-label">
                Loại phòng <span className="text-red-500">*</span>
              </label>
              <select
                id="category"
                className={`form-input ${errors.category ? 'border-red-500' : ''}`}
                {...register('category', {
                  required: 'Vui lòng chọn loại phòng',
                })}
              >
                <option value="">-- Chọn loại phòng --</option>
                {categories.map((category) => (
                  <option key={category._id} value={category._id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.category && (
                <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
              )}
            </div>
          </div>
        );

      case 1: // Địa chỉ
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="address.street" className="form-label">
                Tên đường, số nhà <span className="text-red-500">*</span>
              </label>
              <input
                id="address.street"
                type="text"
                className={`form-input ${errors.address?.street ? 'border-red-500' : ''}`}
                placeholder="Nhập tên đường, số nhà"
                {...register('address.street', {
                  required: 'Tên đường, số nhà là bắt buộc',
                })}
              />
              {errors.address?.street && (
                <p className="mt-1 text-sm text-red-600">{errors.address.street.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="address.city" className="form-label">
                  Tỉnh/Thành phố <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <select
                    id="address.city"
                    className={`form-input ${errors.address?.city ? 'border-red-500' : ''}`}
                    {...register('address.city', {
                      required: 'Tỉnh/Thành phố là bắt buộc',
                    })}
                    onChange={handleProvinceChange}
                  >
                    <option value="">-- Chọn Tỉnh/Thành phố --</option>
                    {provinces.map((province) => (
                      <option key={province.code} value={province.name}>
                        {province.name}
                      </option>
                    ))}
                  </select>
                  {isLocationLoading && (
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                      <FaSpinner className="animate-spin text-gray-400" />
                    </div>
                  )}
                </div>
                {errors.address?.city && (
                  <p className="mt-1 text-sm text-red-600">{errors.address.city.message}</p>
                )}
                {locationError && (
                  <p className="mt-1 text-sm text-red-600">Lỗi tải dữ liệu: {locationError}</p>
                )}
              </div>

              <div>
                <label htmlFor="address.district" className="form-label">
                  Quận/Huyện <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <select
                    id="address.district"
                    className={`form-input ${errors.address?.district ? 'border-red-500' : ''}`}
                    {...register('address.district', {
                      required: 'Quận/Huyện là bắt buộc',
                    })}
                    onChange={handleDistrictChange}
                    disabled={!watch('address.city')}
                  >
                    <option value="">-- Chọn Quận/Huyện --</option>
                    {districts.map((district) => (
                      <option key={district.code} value={district.name}>
                        {district.name}
                      </option>
                    ))}
                  </select>
                  {isLocationLoading && (
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                      <FaSpinner className="animate-spin text-gray-400" />
                    </div>
                  )}
                </div>
                {errors.address?.district && (
                  <p className="mt-1 text-sm text-red-600">{errors.address.district.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="address.ward" className="form-label">
                  Phường/Xã <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <select
                    id="address.ward"
                    className={`form-input ${errors.address?.ward ? 'border-red-500' : ''}`}
                    {...register('address.ward', {
                      required: 'Phường/Xã là bắt buộc',
                    })}
                    disabled={!watch('address.district')}
                  >
                    <option value="">-- Chọn Phường/Xã --</option>
                    {wards.map((ward) => (
                      <option key={ward.code} value={ward.name}>
                        {ward.name}
                      </option>
                    ))}
                  </select>
                  {isLocationLoading && (
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                      <FaSpinner className="animate-spin text-gray-400" />
                    </div>
                  )}
                </div>
                {errors.address?.ward && (
                  <p className="mt-1 text-sm text-red-600">{errors.address.ward.message}</p>
                )}
              </div>
            </div>
          </div>
        );

      case 2: // Tiện nghi
        return (
          <div className="space-y-4">
            <p className="text-gray-600 mb-4">Chọn các tiện nghi có sẵn tại phòng trọ của bạn:</p>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {amenities.map((amenity) => (
                <div key={amenity._id} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`amenity-${amenity._id}`}
                    value={amenity._id}
                    className="h-4 w-4 text-primary focus:ring-primary border-dark-300 rounded dark:border-dark-600"
                    {...register('amenities')}
                  />
                  <label
                    htmlFor={`amenity-${amenity._id}`}
                    className="ml-2 block text-sm text-text dark:text-text-dark"
                  >
                    {amenity.name}
                  </label>
                </div>
              ))}
            </div>
          </div>
        );

      case 3: // Hình ảnh
        return (
          <div className="space-y-4">
            <p className="text-gray-600 mb-4">
              Tải lên hình ảnh phòng trọ của bạn. Hình ảnh chất lượng sẽ giúp thu hút người thuê hiệu quả hơn.
            </p>
            <div className="border-2 border-dashed border-dark-300 dark:border-dark-600 rounded-lg p-4">
              <div className="text-center mb-4">
                <label
                  htmlFor="images"
                  className="cursor-pointer inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                >
                  <FaUpload className="mr-2" />
                  Tải lên hình ảnh
                </label>
                <input
                  id="images"
                  type="file"
                  multiple
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageChange}
                />
                <p className="mt-2 text-sm text-dark-500 dark:text-dark-400">
                  Tối đa 10 hình ảnh, định dạng JPG, PNG
                </p>
              </div>

              {previewImages.length > 0 ? (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {previewImages.map((url, index) => (
                    <div key={index} className="relative">
                      <img
                        src={url}
                        alt={`Hình ${index + 1}`}
                        className="w-full h-32 object-cover rounded-md"
                      />
                      <button
                        type="button"
                        className="absolute top-1 right-1 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                        onClick={() => removeImage(index)}
                      >
                        <FaTrash size={12} />
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  Chưa có hình ảnh nào được tải lên
                </div>
              )}
            </div>
          </div>
        );

      case 4: // Xem trước
        return (
          <div className="space-y-4">
            <p className="text-gray-600 mb-4">
              Xem trước tin đăng của bạn. Kiểm tra lại thông tin trước khi đăng tin.
            </p>
            <RoomPreview
              formData={getValues()}
              categories={categories}
              amenities={amenities}
              previewImages={previewImages}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="bg-background dark:bg-background-dark py-8">
      <div className="container-custom">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold mb-6 text-text dark:text-text-dark">Đăng tin phòng trọ</h1>

          <div className="bg-card dark:bg-card-dark rounded-lg shadow-md p-6">
            <form onSubmit={handleSubmit(onSubmit)}>
              {/* Stepper */}
              <Stepper
                steps={steps}
                currentStep={currentStep}
                onStepClick={handleStepClick}
              />

              {/* Nội dung của bước hiện tại */}
              <div className="mb-8">
                <StepperContent currentStep={currentStep}>
                  {renderStepContent()}
                </StepperContent>
              </div>

              {/* Điều hướng giữa các bước */}
              <StepperNavigation
                currentStep={currentStep}
                totalSteps={steps.length}
                onNext={handleNext}
                onPrev={handlePrev}
                onSubmit={() => handleSubmit(onSubmit)()}
                isLoading={isLoading}
                validateStep={validateStep}
              />
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateRoomPage;
