/**
 * Script test tính năng tự động hạ cấp tin nổi bật
 * Chạy: node test-highlight-expiry.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Room = require('./src/models/Room');

async function testHighlightExpiry() {
  try {
    // Kết nối database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✓ Đã kết nối MongoDB');

    // Tìm một phòng để test
    const room = await Room.findOne({ status: 'available' });
    
    if (!room) {
      console.log('❌ Không tìm thấy phòng nào để test');
      return;
    }

    console.log(`📍 Sử dụng phòng: "${room.title}" (${room._id})`);
    console.log(`   Trạng thái hiện tại: isHighlighted = ${room.isHighlighted}`);

    // Test case 1: Set tin nổi bật với thời gian hết hạn 30 giây
    console.log('\n🔄 Test Case 1: Set tin nổi bật với thời gian hết hạn 30 giây');
    
    room.isHighlighted = true;
    room.highlightExpiry = new Date(Date.now() + 30 * 1000); // 30 giây
    await room.save();

    console.log(`✓ Đã set tin nổi bật, hết hạn lúc: ${room.highlightExpiry.toISOString()}`);

    // Kiểm tra trạng thái
    const updatedRoom = await Room.findById(room._id);
    console.log(`✓ Xác nhận: isHighlighted = ${updatedRoom.isHighlighted}, highlightExpiry = ${updatedRoom.highlightExpiry}`);

    // Test case 2: Kiểm tra logic hạ cấp thủ công
    console.log('\n🔄 Test Case 2: Đợi 35 giây và kiểm tra logic hạ cấp');
    console.log('⏳ Đang đợi 35 giây...');
    
    // Đợi 35 giây
    await new Promise(resolve => setTimeout(resolve, 35000));

    // Chạy logic hạ cấp thủ công
    const now = new Date();
    const expiredRooms = await Room.find({
      isHighlighted: true,
      highlightExpiry: { $lte: now }
    });

    console.log(`📊 Tìm thấy ${expiredRooms.length} tin nổi bật hết hạn`);

    if (expiredRooms.length > 0) {
      // Cập nhật hàng loạt
      const result = await Room.updateMany(
        {
          isHighlighted: true,
          highlightExpiry: { $lte: now }
        },
        {
          $set: { isHighlighted: false },
          $unset: { highlightExpiry: 1 }
        }
      );

      console.log(`✓ Đã hạ cấp ${result.modifiedCount} tin nổi bật`);

      // Kiểm tra kết quả
      const finalRoom = await Room.findById(room._id);
      console.log(`✓ Kết quả cuối: isHighlighted = ${finalRoom.isHighlighted}, highlightExpiry = ${finalRoom.highlightExpiry}`);
    }

    // Test case 3: Test với nhiều phòng
    console.log('\n🔄 Test Case 3: Test với nhiều phòng cùng lúc');
    
    const rooms = await Room.find({ status: 'available' }).limit(3);
    console.log(`📍 Tìm thấy ${rooms.length} phòng để test`);

    // Set tất cả thành tin nổi bật với thời gian hết hạn khác nhau
    for (let i = 0; i < rooms.length; i++) {
      const room = rooms[i];
      room.isHighlighted = true;
      room.highlightExpiry = new Date(Date.now() + (10 + i * 5) * 1000); // 10s, 15s, 20s
      await room.save();
      console.log(`✓ Set phòng ${i + 1}: "${room.title}" - hết hạn sau ${10 + i * 5} giây`);
    }

    // Đợi 25 giây và kiểm tra
    console.log('\n⏳ Đợi 25 giây để test hạ cấp từng phần...');
    await new Promise(resolve => setTimeout(resolve, 25000));

    // Kiểm tra kết quả
    const finalRooms = await Room.find({ _id: { $in: rooms.map(r => r._id) } });
    console.log('\n📊 Kết quả cuối cùng:');
    
    finalRooms.forEach((room, index) => {
      console.log(`   Phòng ${index + 1}: isHighlighted = ${room.isHighlighted}, highlightExpiry = ${room.highlightExpiry}`);
    });

    console.log('\n✅ Hoàn thành tất cả test cases!');

  } catch (error) {
    console.error('❌ Lỗi khi test:', error);
  } finally {
    // Đóng kết nối
    await mongoose.connection.close();
    console.log('✓ Đã đóng kết nối MongoDB');
  }
}

// Chạy test
if (require.main === module) {
  console.log('🚀 Bắt đầu test tính năng tự động hạ cấp tin nổi bật\n');
  testHighlightExpiry().then(() => {
    console.log('\n🎉 Test hoàn thành!');
    process.exit(0);
  }).catch(error => {
    console.error('\n💥 Test thất bại:', error);
    process.exit(1);
  });
}

module.exports = testHighlightExpiry;
