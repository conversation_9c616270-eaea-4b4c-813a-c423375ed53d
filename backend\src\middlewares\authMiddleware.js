const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware bảo vệ route, yêu c<PERSON>u đăng nhập
const protect = async (req, res, next) => {
  let token;

  // Ki<PERSON>m tra token trong header
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    try {
      // Lấy token từ header
      token = req.headers.authorization.split(' ')[1];

      // Xác minh token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Tìm người dùng từ id trong token và không trả về mật khẩu
      req.user = await User.findById(decoded.id).select('-password');

      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Không tìm thấy người dùng với token này'
        });
      }

      if (!req.user.isActive) {
        return res.status(401).json({
          success: false,
          message: '<PERSON><PERSON><PERSON> khoản đã bị khóa'
        });
      }

      // Kiểm tra email đã được xác nhận chưa
      if (!req.user.isEmailVerified) {
        return res.status(401).json({
          success: false,
          message: 'Vui lòng xác nhận email trước khi sử dụng tính năng này',
          isEmailVerified: false
        });
      }

      next();
    } catch (error) {
      console.error(error);
      res.status(401).json({
        success: false,
        message: 'Không được phép, token không hợp lệ'
      });
    }
  }

  if (!token) {
    res.status(401).json({
      success: false,
      message: 'Không được phép, không có token'
    });
  }
};

// Middleware kiểm tra quyền admin
const admin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: 'Không được phép, yêu cầu quyền admin'
    });
  }
};

module.exports = { protect, admin };
