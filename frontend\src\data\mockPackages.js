/**
 * <PERSON><PERSON> liệu mẫu cho gói tin nổi bật
 */
const mockPackages = [
  {
    _id: 'pkg_normal',
    name: 'Tin Thường',
    description: '<PERSON><PERSON>i tin cơ bản với các tính năng tiêu chuẩn',
    price: 0,
    duration: 30,
    type: 'normal',
    features: [
      'Hiển thị trong danh sách tìm kiếm',
      'Thời hạn 30 ngày',
      'Không giới hạn lượt xem',
      'Hỗ trợ qua email'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    _id: 'pkg_vip',
    name: 'Tin VIP',
    description: 'Gói tin nổi bật với vị trí ưu tiên trong kết quả tìm kiếm',
    price: 50000,
    duration: 15,
    type: 'vip',
    features: [
      'Hiển thị ưu tiên trong danh sách tìm kiếm',
      'Nhãn VIP nổi bật',
      'Thời hạn 15 ngày',
      'Không giới hạn lượt xem',
      'Hỗ trợ qua email và điện thoại'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    _id: 'pkg_super_vip',
    name: 'Tin Super VIP',
    description: 'Gói tin cao cấp nhất với vị trí hàng đầu và nhiều tính năng đặc biệt',
    price: 100000,
    duration: 30,
    type: 'super_vip',
    features: [
      'Hiển thị ở vị trí đầu tiên trong danh sách tìm kiếm',
      'Nhãn Super VIP nổi bật',
      'Thời hạn 30 ngày',
      'Không giới hạn lượt xem',
      'Hỗ trợ ưu tiên 24/7',
      'Tự động làm mới tin mỗi 3 ngày',
      'Thống kê chi tiết lượt xem và tương tác'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

export default mockPackages;
