/**
 * <PERSON><PERSON> hình dữ liệu cho gói tin nổi bật
 */
class PackageModel {
  constructor(data = {}) {
    this._id = data._id || '';
    this.name = data.name || '';
    this.description = data.description || '';
    this.price = data.price || 0;
    this.duration = data.duration || 0; // Thời hạn tính theo ngày
    this.type = data.type || 'normal'; // normal, vip, super_vip
    this.features = data.features || [];
    this.createdAt = data.createdAt ? new Date(data.createdAt) : new Date();
    this.updatedAt = data.updatedAt ? new Date(data.updatedAt) : new Date();
  }

  /**
   * Chuyển đổi đối tượng thành dữ liệu JSON để gửi lên server
   * @returns {Object} Dữ liệu JSON
   */
  toJSON() {
    return {
      _id: this._id,
      name: this.name,
      description: this.description,
      price: this.price,
      duration: this.duration,
      type: this.type,
      features: this.features,
    };
  }

  /**
   * Tạo đối tượng PackageModel từ dữ liệu JSON
   * @param {Object} json Dữ liệu JSON
   * @returns {PackageModel} Đối tượng PackageModel
   */
  static fromJSON(json) {
    return new PackageModel(json);
  }
}

export default PackageModel;
