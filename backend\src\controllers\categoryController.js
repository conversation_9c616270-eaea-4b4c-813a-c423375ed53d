const Category = require('../models/Category');

// @desc    Lấy danh sách loại phòng
// @route   GET /api/categories
// @access  Public
const getCategories = async (req, res) => {
  try {
    const categories = await Category.find().sort({ name: 1 });

    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy chi tiết loại phòng
// @route   GET /api/categories/:id
// @access  Public
const getCategoryById = async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: '<PERSON>hông tìm thấy loại phòng'
      });
    }

    res.status(200).json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Tạo loại phòng mới
// @route   POST /api/categories
// @access  Private (Admin)
const createCategory = async (req, res) => {
  try {
    const { name, description } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Tên loại phòng là bắt buộc'
      });
    }

    // Kiểm tra xem loại phòng đã tồn tại chưa
    const existingCategory = await Category.findOne({ name: { $regex: new RegExp(`^${name}$`, 'i') } });
    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: `Loại phòng "${name}" đã tồn tại`,
        data: existingCategory
      });
    }

    // Tạo loại phòng mới
    const category = await Category.create({
      name,
      description: description || ''
    });

    res.status(201).json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error(error);

    // Xử lý lỗi trùng lặp khóa
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Loại phòng này đã tồn tại',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Cập nhật loại phòng
// @route   PUT /api/categories/:id
// @access  Private (Admin)
const updateCategory = async (req, res) => {
  try {
    const { name, description } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Tên loại phòng là bắt buộc'
      });
    }

    // Kiểm tra xem loại phòng cần cập nhật có tồn tại không
    const currentCategory = await Category.findById(req.params.id);
    if (!currentCategory) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy loại phòng'
      });
    }

    // Nếu tên mới khác tên cũ, kiểm tra xem tên mới đã tồn tại chưa
    if (name !== currentCategory.name) {
      const existingCategory = await Category.findOne({
        name: { $regex: new RegExp(`^${name}$`, 'i') },
        _id: { $ne: req.params.id } // Loại trừ loại phòng hiện tại
      });

      if (existingCategory) {
        return res.status(400).json({
          success: false,
          message: `Loại phòng "${name}" đã tồn tại`,
          data: existingCategory
        });
      }
    }

    // Tìm và cập nhật loại phòng
    const category = await Category.findByIdAndUpdate(
      req.params.id,
      { name, description },
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error(error);

    // Xử lý lỗi trùng lặp khóa
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Loại phòng này đã tồn tại',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Xóa loại phòng
// @route   DELETE /api/categories/:id
// @access  Private (Admin)
const deleteCategory = async (req, res) => {
  try {
    // Kiểm tra xem loại phòng có tồn tại không
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy loại phòng'
      });
    }

    // Sử dụng deleteOne thay vì remove (đã bị loại bỏ trong Mongoose mới)
    await Category.deleteOne({ _id: req.params.id });

    res.status(200).json({
      success: true,
      message: 'Xóa loại phòng thành công'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory
};
