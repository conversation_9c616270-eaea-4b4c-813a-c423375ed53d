import axios from 'axios';

// URL của API địa chỉ hành chính Việt Nam từ GitHub
const API_URL = 'https://raw.githubusercontent.com/kenzouno1/DiaGioiHanhChinhVN/master/data.json';

// Cache dữ liệu để tránh gọi API nhiều lần
let cachedData = null;

const locationService = {
  // Lấy tất cả dữ liệu địa chỉ hành chính
  getAllLocations: async () => {
    try {
      // Nếu đã có dữ liệu trong cache, trả về luôn
      if (cachedData) {
        return cachedData;
      }

      // Gọi API để lấy dữ liệu
      const response = await axios.get(API_URL);
      cachedData = response.data;
      return cachedData;
    } catch (error) {
      console.error('Error fetching location data:', error);
      throw error;
    }
  },

  // Lấy danh sách tỉnh/thành phố
  getProvinces: async () => {
    try {
      const data = await locationService.getAllLocations();
      return data.map(province => ({
        code: province.Id,
        name: province.Name,
        codename: province.Slug
      }));
    } catch (error) {
      console.error('Error getting provinces:', error);
      throw error;
    }
  },

  // Lấy danh sách quận/huyện theo tỉnh/thành phố
  getDistrictsByProvince: async (provinceCode) => {
    try {
      const data = await locationService.getAllLocations();
      const province = data.find(p => p.Id === provinceCode || p.Name === provinceCode);
      
      if (!province) {
        return [];
      }

      return province.Districts.map(district => ({
        code: district.Id,
        name: district.Name,
        codename: district.Slug
      }));
    } catch (error) {
      console.error('Error getting districts:', error);
      throw error;
    }
  },

  // Lấy danh sách phường/xã theo quận/huyện
  getWardsByDistrict: async (provinceCode, districtCode) => {
    try {
      const data = await locationService.getAllLocations();
      const province = data.find(p => p.Id === provinceCode || p.Name === provinceCode);
      
      if (!province) {
        return [];
      }

      const district = province.Districts.find(d => d.Id === districtCode || d.Name === districtCode);
      
      if (!district) {
        return [];
      }

      return district.Wards.map(ward => ({
        code: ward.Id,
        name: ward.Name,
        codename: ward.Slug
      }));
    } catch (error) {
      console.error('Error getting wards:', error);
      throw error;
    }
  }
};

export default locationService;
