const User = require('../models/User');
const Room = require('../models/Room');
const Transaction = require('../models/Transaction');
const Category = require('../models/Category');
const Amenity = require('../models/Amenity');

// @desc    Lấy danh sách người dùng
// @route   GET /api/admin/users
// @access  Private (Admin)
const getUsers = async (req, res) => {
  try {
    // Xây dựng query
    const query = {};

    // Tìm kiếm theo email hoặc tên
    if (req.query.search) {
      query.$or = [
        { email: { $regex: req.query.search, $options: 'i' } },
        { fullName: { $regex: req.query.search, $options: 'i' } }
      ];
    }

    // Lọc theo vai trò
    if (req.query.role) {
      query.role = req.query.role;
    }

    // Lọc theo trạng thái
    if (req.query.isActive !== undefined) {
      query.isActive = req.query.isActive === 'true';
    }

    // Phân trang
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Thực hiện truy vấn
    const users = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);

    // Đếm tổng số người dùng
    const total = await User.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy chi tiết người dùng
// @route   GET /api/admin/users/:id
// @access  Private (Admin)
const getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Cập nhật trạng thái người dùng (khóa/mở khóa)
// @route   PUT /api/admin/users/:id/status
// @access  Private (Admin)
const updateUserStatus = async (req, res) => {
  try {
    const { isActive } = req.body;

    if (isActive === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Trạng thái người dùng là bắt buộc'
      });
    }

    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng'
      });
    }

    // Không cho phép khóa tài khoản admin
    if (user.role === 'admin' && !isActive) {
      return res.status(400).json({
        success: false,
        message: 'Không thể khóa tài khoản admin'
      });
    }

    user.isActive = isActive;
    await user.save();

    res.status(200).json({
      success: true,
      data: {
        _id: user._id,
        fullName: user.fullName,
        email: user.email,
        role: user.role,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Xóa người dùng
// @route   DELETE /api/admin/users/:id
// @access  Private (Admin)
const deleteUser = async (req, res) => {
  try {
    // Kiểm tra xem người dùng có tồn tại không
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng'
      });
    }

    // Không cho phép xóa tài khoản admin
    if (user.role === 'admin') {
      return res.status(400).json({
        success: false,
        message: 'Không thể xóa tài khoản admin'
      });
    }

    // Kiểm tra tùy chọn xóa dữ liệu liên quan
    const cascadeDelete = req.query.cascade === 'true';

    // Kiểm tra xem người dùng có phòng trọ không
    const roomCount = await Room.countDocuments({ user: req.params.id });
    if (roomCount > 0) {
      if (cascadeDelete) {
        // Xóa tất cả phòng trọ của người dùng
        await Room.deleteMany({ user: req.params.id });
        console.log(`Đã xóa ${roomCount} phòng trọ của người dùng ${user.email}`);
      } else {
        return res.status(400).json({
          success: false,
          message: `Không thể xóa người dùng này vì họ đang có ${roomCount} phòng trọ. Vui lòng xóa các phòng trọ trước hoặc sử dụng tùy chọn cascade=true.`
        });
      }
    }

    // Kiểm tra xem người dùng có giao dịch không
    const transactionCount = await Transaction.countDocuments({ user: req.params.id });
    if (transactionCount > 0) {
      if (cascadeDelete) {
        // Xóa tất cả giao dịch của người dùng
        await Transaction.deleteMany({ user: req.params.id });
        console.log(`Đã xóa ${transactionCount} giao dịch của người dùng ${user.email}`);
      } else {
        return res.status(400).json({
          success: false,
          message: `Không thể xóa người dùng này vì họ đang có ${transactionCount} giao dịch. Vui lòng xóa các giao dịch trước hoặc sử dụng tùy chọn cascade=true.`
        });
      }
    }

    // Sử dụng deleteOne thay vì remove (đã bị loại bỏ trong Mongoose mới)
    await User.deleteOne({ _id: req.params.id });

    res.status(200).json({
      success: true,
      message: cascadeDelete
        ? `Xóa người dùng và tất cả dữ liệu liên quan thành công`
        : 'Xóa người dùng thành công'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy thống kê hệ thống
// @route   GET /api/admin/statistics
// @access  Private (Admin)
const getStatistics = async (req, res) => {
  try {
    // Tổng số người dùng
    const totalUsers = await User.countDocuments();

    // Tổng số phòng trọ
    const totalRooms = await Room.countDocuments();

    // Số phòng trọ đã cho thuê
    const rentedRooms = await Room.countDocuments({ status: 'rented' });

    // Số phòng trọ còn trống
    const availableRooms = await Room.countDocuments({ status: 'available' });

    // Số phòng trọ nổi bật
    const highlightedRooms = await Room.countDocuments({ isHighlighted: true });

    // Tổng doanh thu
    const totalRevenue = await Transaction.aggregate([
      { $match: { status: 'completed' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    // Số giao dịch thành công
    const completedTransactions = await Transaction.countDocuments({ status: 'completed' });

    // Thống kê theo thời gian (7 ngày gần nhất)
    const last7Days = new Date();
    last7Days.setDate(last7Days.getDate() - 7);

    // Số người dùng mới trong 7 ngày
    const newUsers = await User.countDocuments({ createdAt: { $gte: last7Days } });

    // Số phòng trọ mới trong 7 ngày
    const newRooms = await Room.countDocuments({ createdAt: { $gte: last7Days } });

    // Doanh thu trong 7 ngày
    const recentRevenue = await Transaction.aggregate([
      { $match: { status: 'completed', completedAt: { $gte: last7Days } } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    res.status(200).json({
      success: true,
      data: {
        users: {
          total: totalUsers,
          new: newUsers
        },
        rooms: {
          total: totalRooms,
          rented: rentedRooms,
          available: availableRooms,
          highlighted: highlightedRooms,
          new: newRooms
        },
        transactions: {
          completed: completedTransactions,
          totalRevenue: totalRevenue.length > 0 ? totalRevenue[0].total : 0,
          recentRevenue: recentRevenue.length > 0 ? recentRevenue[0].total : 0
        }
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy danh sách phòng trọ (Admin)
// @route   GET /api/admin/rooms
// @access  Private (Admin)
const getRooms = async (req, res) => {
  try {
    // Xây dựng query
    const query = {};

    // Tìm kiếm theo tiêu đề hoặc mô tả
    if (req.query.search) {
      query.$or = [
        { title: { $regex: req.query.search, $options: 'i' } },
        { description: { $regex: req.query.search, $options: 'i' } },
        { 'address.city': { $regex: req.query.search, $options: 'i' } },
        { 'address.district': { $regex: req.query.search, $options: 'i' } }
      ];
    }

    // Lọc theo trạng thái
    if (req.query.status) {
      query.status = req.query.status;
    }

    // Lọc theo tin nổi bật
    if (req.query.isHighlighted !== undefined) {
      query.isHighlighted = req.query.isHighlighted === 'true';
    }

    // Lọc theo giá
    if (req.query.minPrice) {
      query.price = { ...query.price, $gte: Number(req.query.minPrice) };
    }
    if (req.query.maxPrice) {
      query.price = { ...query.price, $lte: Number(req.query.maxPrice) };
    }

    // Lọc theo diện tích
    if (req.query.minArea) {
      query.area = { ...query.area, $gte: Number(req.query.minArea) };
    }
    if (req.query.maxArea) {
      query.area = { ...query.area, $lte: Number(req.query.maxArea) };
    }

    // Lọc theo thành phố
    if (req.query.city) {
      query['address.city'] = { $regex: req.query.city, $options: 'i' };
    }

    // Phân trang
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Sắp xếp
    let sort = {};
    if (req.query.sort) {
      const sortFields = req.query.sort.split(',');
      for (const field of sortFields) {
        if (field.startsWith('-')) {
          sort[field.substring(1)] = -1;
        } else {
          sort[field] = 1;
        }
      }
    } else {
      // Mặc định sắp xếp theo tin nổi bật và ngày tạo mới nhất
      sort = { isHighlighted: -1, createdAt: -1 };
    }

    // Thực hiện truy vấn
    const rooms = await Room.find(query)
      .populate('category', 'name')
      .populate('amenities', 'name icon')
      .populate('user', 'fullName email phone')
      .sort(sort)
      .skip(startIndex)
      .limit(limit);

    // Đếm tổng số phòng trọ
    const total = await Room.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        rooms,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Xóa phòng trọ (Admin)
// @route   DELETE /api/admin/rooms/:id
// @access  Private (Admin)
const deleteRoomAdmin = async (req, res) => {
  try {
    const room = await Room.findById(req.params.id);

    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy phòng trọ'
      });
    }

    await Room.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy danh sách giao dịch (Admin)
// @route   GET /api/admin/transactions
// @access  Private (Admin)
const getTransactions = async (req, res) => {
  try {
    // Xây dựng query
    const query = {};

    // Tìm kiếm theo mã giao dịch
    if (req.query.search) {
      query.$or = [
        { vnpayTxnRef: { $regex: req.query.search, $options: 'i' } },
        { _id: req.query.search.match(/^[0-9a-fA-F]{24}$/) ? req.query.search : null }
      ];
    }

    // Lọc theo trạng thái
    if (req.query.status) {
      query.status = req.query.status;
    }

    // Lọc theo thời gian
    if (req.query.startDate) {
      const startDate = new Date(req.query.startDate);
      startDate.setHours(0, 0, 0, 0);
      query.createdAt = { ...query.createdAt, $gte: startDate };
    }
    if (req.query.endDate) {
      const endDate = new Date(req.query.endDate);
      endDate.setHours(23, 59, 59, 999);
      query.createdAt = { ...query.createdAt, $lte: endDate };
    }

    // Phân trang
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Thực hiện truy vấn
    const transactions = await Transaction.find(query)
      .populate('user', 'fullName email')
      .populate('room', 'title')
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);

    // Đếm tổng số giao dịch
    const total = await Transaction.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy danh sách loại phòng (Admin)
// @route   GET /api/admin/categories
// @access  Private (Admin)
const getCategories = async (req, res) => {
  try {
    // Xây dựng query
    const query = {};

    // Tìm kiếm theo tên
    if (req.query.search) {
      query.name = { $regex: req.query.search, $options: 'i' };
    }

    // Phân trang
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Thực hiện truy vấn
    const categories = await Category.find(query)
      .sort({ name: 1 })
      .skip(startIndex)
      .limit(limit);

    // Đếm tổng số loại phòng
    const total = await Category.countDocuments(query);

    // Đếm số lượng phòng thuộc mỗi loại
    const categoriesWithRoomCount = await Promise.all(
      categories.map(async (category) => {
        const roomCount = await Room.countDocuments({ category: category._id });
        return {
          ...category.toObject(),
          roomCount
        };
      })
    );

    res.status(200).json({
      success: true,
      data: {
        categories: categoriesWithRoomCount,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Tạo loại phòng mới (Admin)
// @route   POST /api/admin/categories
// @access  Private (Admin)
const createCategory = async (req, res) => {
  try {
    const { name, description } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Tên loại phòng là bắt buộc'
      });
    }

    // Kiểm tra xem loại phòng đã tồn tại chưa
    const existingCategory = await Category.findOne({ name: { $regex: new RegExp(`^${name}$`, 'i') } });
    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: `Loại phòng "${name}" đã tồn tại`,
        data: existingCategory
      });
    }

    // Tạo loại phòng mới
    const category = await Category.create({
      name,
      description: description || ''
    });

    res.status(201).json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error(error);

    // Xử lý lỗi trùng lặp khóa
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Loại phòng này đã tồn tại',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Cập nhật loại phòng (Admin)
// @route   PUT /api/admin/categories/:id
// @access  Private (Admin)
const updateCategory = async (req, res) => {
  try {
    const { name, description } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Tên loại phòng là bắt buộc'
      });
    }

    // Kiểm tra xem loại phòng có tồn tại không
    const currentCategory = await Category.findById(req.params.id);
    if (!currentCategory) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy loại phòng'
      });
    }

    // Nếu tên mới khác tên cũ, kiểm tra xem tên mới đã tồn tại chưa
    if (name !== currentCategory.name) {
      const existingCategory = await Category.findOne({
        name: { $regex: new RegExp(`^${name}$`, 'i') },
        _id: { $ne: req.params.id } // Loại trừ loại phòng hiện tại
      });

      if (existingCategory) {
        return res.status(400).json({
          success: false,
          message: `Loại phòng "${name}" đã tồn tại`,
          data: existingCategory
        });
      }
    }

    // Tìm và cập nhật loại phòng
    const category = await Category.findByIdAndUpdate(
      req.params.id,
      { name, description },
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error(error);

    // Xử lý lỗi trùng lặp khóa
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Loại phòng này đã tồn tại',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Xóa loại phòng (Admin)
// @route   DELETE /api/admin/categories/:id
// @access  Private (Admin)
const deleteCategory = async (req, res) => {
  try {
    // Kiểm tra xem loại phòng có tồn tại không
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy loại phòng'
      });
    }

    // Kiểm tra xem có phòng nào thuộc loại này không
    const roomCount = await Room.countDocuments({ category: req.params.id });
    if (roomCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Không thể xóa loại phòng này vì có ${roomCount} phòng đang sử dụng`
      });
    }

    // Xóa loại phòng
    await Category.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Xóa loại phòng thành công'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy danh sách tiện nghi (Admin)
// @route   GET /api/admin/amenities
// @access  Private (Admin)
const getAmenities = async (req, res) => {
  try {
    // Xây dựng query
    const query = {};

    // Tìm kiếm theo tên
    if (req.query.search) {
      query.name = { $regex: req.query.search, $options: 'i' };
    }

    // Phân trang
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Thực hiện truy vấn
    const amenities = await Amenity.find(query)
      .sort({ name: 1 })
      .skip(startIndex)
      .limit(limit);

    // Đếm tổng số tiện nghi
    const total = await Amenity.countDocuments(query);

    // Đếm số lượng phòng có mỗi tiện nghi
    const amenitiesWithRoomCount = await Promise.all(
      amenities.map(async (amenity) => {
        const roomCount = await Room.countDocuments({ amenities: amenity._id });
        return {
          ...amenity.toObject(),
          roomCount
        };
      })
    );

    res.status(200).json({
      success: true,
      data: {
        amenities: amenitiesWithRoomCount,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Tạo tiện nghi mới (Admin)
// @route   POST /api/admin/amenities
// @access  Private (Admin)
const createAmenity = async (req, res) => {
  try {
    const { name, icon } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Tên tiện nghi là bắt buộc'
      });
    }

    // Kiểm tra xem tiện nghi đã tồn tại chưa
    const existingAmenity = await Amenity.findOne({ name: { $regex: new RegExp(`^${name}$`, 'i') } });
    if (existingAmenity) {
      return res.status(400).json({
        success: false,
        message: `Tiện nghi "${name}" đã tồn tại`,
        data: existingAmenity
      });
    }

    // Tạo tiện nghi mới
    const amenity = await Amenity.create({
      name,
      icon: icon || ''
    });

    res.status(201).json({
      success: true,
      data: amenity
    });
  } catch (error) {
    console.error(error);

    // Xử lý lỗi trùng lặp khóa
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Tiện nghi này đã tồn tại',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Cập nhật tiện nghi (Admin)
// @route   PUT /api/admin/amenities/:id
// @access  Private (Admin)
const updateAmenity = async (req, res) => {
  try {
    const { name, icon } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Tên tiện nghi là bắt buộc'
      });
    }

    // Kiểm tra xem tiện nghi có tồn tại không
    const currentAmenity = await Amenity.findById(req.params.id);
    if (!currentAmenity) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy tiện nghi'
      });
    }

    // Nếu tên mới khác tên cũ, kiểm tra xem tên mới đã tồn tại chưa
    if (name !== currentAmenity.name) {
      const existingAmenity = await Amenity.findOne({
        name: { $regex: new RegExp(`^${name}$`, 'i') },
        _id: { $ne: req.params.id } // Loại trừ tiện nghi hiện tại
      });

      if (existingAmenity) {
        return res.status(400).json({
          success: false,
          message: `Tiện nghi "${name}" đã tồn tại`,
          data: existingAmenity
        });
      }
    }

    // Tìm và cập nhật tiện nghi
    const amenity = await Amenity.findByIdAndUpdate(
      req.params.id,
      { name, icon },
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      data: amenity
    });
  } catch (error) {
    console.error(error);

    // Xử lý lỗi trùng lặp khóa
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Tiện nghi này đã tồn tại',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Xóa tiện nghi (Admin)
// @route   DELETE /api/admin/amenities/:id
// @access  Private (Admin)
const deleteAmenity = async (req, res) => {
  try {
    // Kiểm tra xem tiện nghi có tồn tại không
    const amenity = await Amenity.findById(req.params.id);

    if (!amenity) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy tiện nghi'
      });
    }

    // Kiểm tra xem có phòng nào sử dụng tiện nghi này không
    const roomCount = await Room.countDocuments({ amenities: req.params.id });
    if (roomCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Không thể xóa tiện nghi này vì có ${roomCount} phòng đang sử dụng`
      });
    }

    // Xóa tiện nghi
    await Amenity.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Xóa tiện nghi thành công'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getUsers,
  getUserById,
  updateUserStatus,
  deleteUser,
  getStatistics,
  getRooms,
  deleteRoomAdmin,
  getTransactions,
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  getAmenities,
  createAmenity,
  updateAmenity,
  deleteAmenity
};
