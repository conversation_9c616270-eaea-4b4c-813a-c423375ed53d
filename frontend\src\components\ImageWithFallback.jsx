import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

/**
 * Component hiển thị hình ảnh với xử lý lỗi và fallback
 *
 * @param {Object} props - Props của component
 * @param {string} props.src - Đường dẫn hình ảnh
 * @param {string} props.alt - Mô tả hình ảnh
 * @param {string} props.fallbackSrc - Đường dẫn hình ảnh dự phòng khi lỗi
 * @param {string} props.className - Class CSS
 * @param {Function} props.onLoad - Callback khi hình ảnh tải thành công
 * @param {Function} props.onError - Callback khi hình ảnh tải thất bại
 * @param {Object} props.rest - Các props khác
 */
const ImageWithFallback = ({
  src,
  alt,
  fallbackSrc = 'https://via.placeholder.com/300x200?text=Lỗi+hình+ảnh',
  className = '',
  onLoad,
  onError,
  ...rest
}) => {
  const [imgSrc, setImgSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Cập nhật imgSrc khi src thay đổi
  useEffect(() => {
    if (src) {
      // Tạo một đối tượng Image để kiểm tra xem hình ảnh đã được cache chưa
      const img = new Image();
      img.src = processImageUrl(src);

      // Nếu hình ảnh đã được cache, không cần hiển thị loading
      if (img.complete) {
        setImgSrc(src);
        setIsLoading(false);
        setHasError(false);
      } else {
        setImgSrc(src);
        setIsLoading(true);
        setHasError(false);

        // Thêm event listener để xử lý khi hình ảnh tải xong
        img.onload = () => {
          setIsLoading(false);
        };

        img.onerror = () => {
          setImgSrc(fallbackSrc);
          setIsLoading(false);
          setHasError(true);
        };
      }
    } else {
      setImgSrc(fallbackSrc);
      setIsLoading(false);
      setHasError(true);
    }

    // Cleanup function
    return () => {
      // Xóa các event listener nếu cần
    };
  }, [src, fallbackSrc]);

  // Xử lý khi hình ảnh tải thành công
  const handleLoad = (e) => {
    setIsLoading(false);
    if (onLoad) onLoad(e);
  };

  // Xử lý khi hình ảnh tải thất bại
  const handleError = (e) => {
    console.error('Lỗi tải hình ảnh:', src);
    setImgSrc(fallbackSrc);
    setIsLoading(false);
    setHasError(true);
    if (onError) onError(e);
  };

  // Xử lý đường dẫn hình ảnh
  const processImageUrl = (url) => {
    // Kiểm tra nếu url là null, undefined hoặc chuỗi rỗng
    if (!url || url === '') {
      console.log('URL hình ảnh không hợp lệ:', url);
      return fallbackSrc;
    }

    try {
      // Kiểm tra xem đường dẫn có phải là URL đầy đủ không (bao gồm blob URLs)
      if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:') || url.startsWith('blob:')) {
        return url;
      }

      // Nếu là đường dẫn tương đối, thêm URL của backend
      const backendUrl = import.meta.env.VITE_API_URL
        ? import.meta.env.VITE_API_URL.replace('/api', '')
        : 'http://localhost:5000';

      // Đảm bảo đường dẫn bắt đầu bằng /
      const imagePath = url.startsWith('/') ? url : `/${url}`;

      // Thêm timestamp để tránh cache (chỉ cho server URLs)
      const timestamp = new Date().getTime();
      return `${backendUrl}${imagePath}?t=${timestamp}`;
    } catch (error) {
      console.error('Lỗi khi xử lý URL hình ảnh:', error, 'URL:', url);
      return fallbackSrc;
    }
  };

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      )}
      <img
        src={processImageUrl(imgSrc)}
        alt={alt}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300 w-full h-full object-cover`}
        onLoad={handleLoad}
        onError={handleError}
        loading="eager" // Thêm thuộc tính loading="eager" để ưu tiên tải hình ảnh
        decoding="async" // Thêm thuộc tính decoding="async" để giải mã hình ảnh bất đồng bộ
        {...rest}
      />
      {hasError && (
        <div className="absolute bottom-0 left-0 bg-red-500 text-white text-xs px-2 py-1 rounded-tr-md">
          Lỗi hình ảnh
        </div>
      )}
    </div>
  );
};

ImageWithFallback.propTypes = {
  src: PropTypes.string.isRequired,
  alt: PropTypes.string.isRequired,
  fallbackSrc: PropTypes.string,
  className: PropTypes.string,
  onLoad: PropTypes.func,
  onError: PropTypes.func
};

export default ImageWithFallback;
