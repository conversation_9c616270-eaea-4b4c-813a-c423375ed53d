import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const Pagination = ({
  currentPage,
  totalPages,
  onPageChange
}) => {
  // Không hiển thị phân trang nếu chỉ có 1 trang
  if (totalPages <= 1) {
    return null;
  }

  // Xử lý khi click Previous
  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  // Xử lý khi click Next
  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  // Tạo mảng các trang để hiển thị
  const getPageNumbers = () => {
    const pageNumbers = [];

    // Luôn hiển thị trang đầu tiên
    pageNumbers.push(1);

    // Tính toán phạm vi trang hiển thị
    let startPage = Math.max(2, currentPage - 1);
    let endPage = Math.min(totalPages - 1, currentPage + 1);

    // Thêm dấu ... nếu cần
    if (startPage > 2) {
      pageNumbers.push('...');
    }

    // Thêm các trang ở giữa
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    // Thêm dấu ... nếu cần
    if (endPage < totalPages - 1) {
      pageNumbers.push('...');
    }

    // Luôn hiển thị trang cuối cùng nếu có nhiều hơn 1 trang
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className="mt-8 bg-card dark:bg-card-dark rounded-lg shadow-md p-4">
      {/* Controls phân trang */}
      <div className="flex flex-wrap justify-center items-center gap-2">
        {/* Nút Previous */}
        <button
          onClick={handlePrevious}
          disabled={currentPage === 1}
          className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
            currentPage === 1
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800 dark:text-gray-600'
              : 'bg-white text-primary hover:bg-gray-50 border border-gray-300 dark:bg-gray-700 dark:text-primary dark:hover:bg-gray-600 dark:border-gray-600 hover:shadow-md'
          }`}
          aria-label="Previous page"
        >
          <FaChevronLeft className="h-3 w-3 mr-1" />
          Trước
        </button>

        {/* Numbered pagination */}
        {pageNumbers.map((page, index) => {
          if (page === '...') {
            return (
              <span
                key={`ellipsis-${index}`}
                className="px-2 py-2 text-gray-500 dark:text-gray-400 text-sm"
                aria-hidden="true"
              >
                ...
              </span>
            );
          }

          return (
            <button
              key={page}
              onClick={() => onPageChange(page)}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                currentPage === page
                  ? 'bg-primary text-white shadow-md transform scale-105'
                  : 'bg-white text-primary hover:bg-gray-50 border border-gray-300 dark:bg-gray-700 dark:text-primary dark:hover:bg-gray-600 dark:border-gray-600 hover:shadow-md'
              }`}
              aria-current={currentPage === page ? 'page' : undefined}
              aria-label={`Đi đến trang ${page}`}
            >
              {page}
            </button>
          );
        })}

        {/* Nút Next */}
        <button
          onClick={handleNext}
          disabled={currentPage === totalPages}
          className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
            currentPage === totalPages
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800 dark:text-gray-600'
              : 'bg-white text-primary hover:bg-gray-50 border border-gray-300 dark:bg-gray-700 dark:text-primary dark:hover:bg-gray-600 dark:border-gray-600 hover:shadow-md'
          }`}
          aria-label="Next page"
        >
          Tiếp
          <FaChevronRight className="h-3 w-3 ml-1" />
        </button>
      </div>
    </div>
  );
};

export default Pagination;
