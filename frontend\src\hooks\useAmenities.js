import { useState, useCallback } from 'react';
import { amenityService } from '../services';

// Hook quản lý các thao tác với tiện nghi
const useAmenities = () => {
  const [amenities, setAmenities] = useState([]);
  const [amenity, setAmenity] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // L<PERSON>y danh sách tất cả các tiện nghi
  const fetchAmenities = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await amenityService.getAmenities();
      if (response.success) {
        setAmenities(response.data);
      }
      return response;
    } catch (err) {
      setError(err.message || 'Không thể lấy danh sách tiện nghi');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // <PERSON><PERSON><PERSON> thông tin chi tiết của một tiện nghi
  const fetchAmenityById = useCallback(async (id) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await amenityService.getAmenityById(id);
      if (response.success) {
        setAmenity(response.data);
      }
      return response;
    } catch (err) {
      setError(err.message || 'Không thể lấy thông tin tiện nghi');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Tạo tiện nghi mới (chỉ admin)
  const createAmenity = async (amenityData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await amenityService.createAmenity(amenityData);
      return response;
    } catch (err) {
      setError(err.message || 'Không thể tạo tiện nghi mới');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Cập nhật thông tin tiện nghi (chỉ admin)
  const updateAmenity = async (id, amenityData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await amenityService.updateAmenity(id, amenityData);
      return response;
    } catch (err) {
      setError(err.message || 'Không thể cập nhật thông tin tiện nghi');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Xóa tiện nghi (chỉ admin)
  const deleteAmenity = async (id) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await amenityService.deleteAmenity(id);
      return response;
    } catch (err) {
      setError(err.message || 'Không thể xóa tiện nghi');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    amenities,
    amenity,
    isLoading,
    error,
    fetchAmenities,
    fetchAmenityById,
    createAmenity,
    updateAmenity,
    deleteAmenity,
  };
};

export default useAmenities;
