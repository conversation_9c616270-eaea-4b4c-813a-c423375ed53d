import { Link } from 'react-router-dom';
import { FaMapMarkerAlt, FaRuler, FaRegClock } from 'react-icons/fa';
import { formatCurrency, formatDate } from '../utils/format';
import ImageWithFallback from './ImageWithFallback';
import CountdownTimer from './CountdownTimer';

const Card = (props) => {
  try {
    // Kiểm tra xem có dữ liệu room hay không
    const { room, ...restProps } = props || {};

    // Nếu có dữ liệu room từ API
    if (room && typeof room === 'object') {
      // Sử dụng destructuring với giá trị mặc định để tránh lỗi undefined
      const {
        _id = '',
        title = 'Không có tiêu đề',
        description = '',
        price = 0,
        area = 0,
        address = {},
        images = [],
        createdAt,
        isHighlighted = false,
        packageType = 'normal',
        highlightExpiry = null,
        status = 'available'
      } = room;

      // <PERSON>ử lý địa chỉ an toàn
      const street = address?.street || '';
      const ward = address?.ward || '';
      const district = address?.district || '';
      const city = address?.city || '';

      // Tạo địa chỉ đầy đủ, lọc bỏ các phần tử rỗng
      const addressParts = [street, ward, district, city].filter(Boolean);
      const fullAddress = addressParts.length > 0
        ? addressParts.join(', ')
        : 'Không có địa chỉ';

      // Xử lý hình ảnh an toàn
      let imageUrl = 'https://via.placeholder.com/300x200?text=Không+có+hình';

      if (Array.isArray(images) && images.length > 0 && images[0]) {
        // Kiểm tra xem đường dẫn có phải là URL đầy đủ không
        const image = images[0];
        if (image.startsWith('http://') || image.startsWith('https://')) {
          imageUrl = image;
        } else {
          // Nếu là đường dẫn tương đối, thêm URL của backend
          const backendUrl = import.meta.env.VITE_API_URL
            ? import.meta.env.VITE_API_URL.replace('/api', '')
            : 'http://localhost:5000';

          // Đảm bảo đường dẫn bắt đầu bằng /
          const imagePath = image.startsWith('/') ? image : `/${image}`;
          imageUrl = `${backendUrl}${imagePath}`;
        }

        console.log('Đường dẫn hình ảnh:', imageUrl);
      }

      // Xác định kiểu gói tin nổi bật
      const getPackageStyle = () => {
        if (!isHighlighted) return {};

        switch (packageType) {
          case 'super_vip':
            return {
              borderColor: 'border-purple-500',
              ribbonBg: 'bg-purple-600',
              ribbonText: 'SUPER VIP',
              badgeColor: 'bg-purple-100 text-purple-800',
              badgeText: 'SUPER VIP',
              gradientBg: 'from-white to-purple-50'
            };
          case 'vip':
            return {
              borderColor: 'border-yellow-500',
              ribbonBg: 'bg-yellow-500',
              ribbonText: 'VIP',
              badgeColor: 'bg-yellow-100 text-yellow-800',
              badgeText: 'VIP',
              gradientBg: 'from-white to-yellow-50'
            };
          default:
            return {
              borderColor: 'border-primary',
              ribbonBg: 'bg-secondary',
              ribbonText: 'PREMIUM',
              badgeColor: 'bg-blue-100 text-blue-800',
              badgeText: 'Nổi bật',
              gradientBg: 'from-white to-blue-50'
            };
        }
      };

      const packageStyle = getPackageStyle();

      return (
        <div
          className={`card hover:shadow-lg transition-all duration-300
            ${isHighlighted
              ? `border-2 ${packageStyle.borderColor} shadow-lg relative`
              : ''
            }`}
        >
          {/* Ribbon cho tin nổi bật */}
          {isHighlighted && (
            <div className="absolute -top-3 -right-3 z-20">

            </div>
          )}

          <div className="relative overflow-hidden">
            <ImageWithFallback
              src={imageUrl}
              alt={title}
              className="w-full h-48 object-cover rounded-t-lg transition-transform duration-700 hover:scale-110"
              fallbackSrc="https://via.placeholder.com/300x200?text=Lỗi+hình+ảnh"
            />

            <div className="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded-md text-sm font-medium">
              {formatCurrency(price)}/tháng
            </div>

            {isHighlighted && (
              <div className="absolute bottom-2 left-2 flex flex-col gap-1">
                <div className={`${packageStyle.ribbonBg} text-white px-3 py-1 rounded-full text-xs font-bold flex items-center`}>
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                  </svg>
                  {packageType === 'super_vip' ? 'Super VIP' :
                   packageType === 'vip' ? 'VIP' : 'Tin nổi bật'}
                </div>
                {highlightExpiry && (
                  <CountdownTimer
                    expiryDate={highlightExpiry}
                    compact={true}
                    showIcon={true}
                    className="text-xs"
                  />
                )}
              </div>
            )}

            {status === 'rented' && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-t-lg">
                <span className="text-white font-bold text-lg">Đã cho thuê</span>
              </div>
            )}
          </div>

          <div className={`p-4 ${isHighlighted ? `bg-gradient-to-br ${packageStyle.gradientBg}` : ''}`}>
            <h3 className="text-lg font-semibold mb-2 text-red-600 line-clamp-1">
              {title}
              {isHighlighted && (
                <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${packageStyle.badgeColor}`}>
                  {packageStyle.badgeText}
                </span>
              )}
            </h3>

            <div className="flex items-center text-gray-600 mb-2">
              <FaMapMarkerAlt className="w-4 h-4 mr-1 flex-shrink-0" />
              <span className="text-sm line-clamp-1">{fullAddress}</span>
            </div>

            <div className="flex justify-between text-gray-600 mb-3">
              <div className="flex items-center">
                <FaRuler className="w-4 h-4 mr-1" />
                <span className="text-sm">{area} m²</span>
              </div>

              <div className="flex items-center">
                <FaRegClock className="w-4 h-4 mr-1" />
                <span className="text-sm">
                  {createdAt ? formatDate(createdAt) : 'Hôm nay'}
                </span>
              </div>
            </div>

            <p className="text-sm text-gray-600 line-clamp-2 mb-4">
              {description || 'Không có mô tả'}
            </p>

            <div className="flex justify-center items-center">
              <Link
                to={`/rooms/${_id}`}
                className={`btn ${isHighlighted ? 'btn-secondary' : 'btn-primary'} w-full`}
              >
                Xem Chi Tiết
              </Link>
            </div>
          </div>
        </div>
      );
    }

    // Xử lý trường hợp dữ liệu cũ (props riêng lẻ)
    const {
      title,
      description,
      imageUrl,
      price,
      area,
      address
    } = restProps;

    if (title || description || imageUrl || price || area || address) {
      return (
        <div className="card hover:shadow-lg transition-shadow duration-300">
          <div className="relative">
            <ImageWithFallback
              src={imageUrl || 'https://via.placeholder.com/300x200?text=Không+có+hình'}
              alt={title || 'Phòng trọ'}
              className="w-full h-48 object-cover rounded-t-lg"
              fallbackSrc="https://via.placeholder.com/300x200?text=Lỗi+hình+ảnh"
            />
            <div className="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded-md text-sm font-medium">
              {formatCurrency(price || 0)}/tháng
            </div>
          </div>

          <div className="p-4">
            <h3 className="text-lg font-semibold mb-2 text-red-600 line-clamp-1">
              {title || 'Không có tiêu đề'}
            </h3>

            <div className="flex items-center text-gray-600 mb-2">
              <FaMapMarkerAlt className="w-4 h-4 mr-1" />
              <span className="text-sm line-clamp-1">{address || 'Không có địa chỉ'}</span>
            </div>

            <div className="flex justify-between text-sm text-gray-600 mb-3">
              <div className="flex items-center">
                <FaRuler className="w-4 h-4 mr-1" />
                <span>{area || 0} m²</span>
              </div>

              <div className="flex items-center">
                <FaRegClock className="w-4 h-4 mr-1" />
                <span>Hôm nay</span>
              </div>
            </div>

            <p className="text-gray-600 text-sm line-clamp-2 mb-4">
              {description || 'Không có mô tả'}
            </p>

            <div className="flex justify-center">
              <button className="btn btn-primary w-full">Xem Chi Tiết</button>
            </div>
          </div>
        </div>
      );
    }

    // Fallback khi không có dữ liệu
    return (
      <div className="card hover:shadow-lg transition-shadow duration-300">
        <div className="p-6 text-center">
          <p className="text-gray-500">Không có dữ liệu phòng trọ</p>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Lỗi trong component Card:", error);

    // Fallback khi có lỗi
    return (
      <div className="card hover:shadow-lg transition-shadow duration-300 border border-red-300">
        <div className="p-6 text-center">
          <p className="text-red-500">Đã xảy ra lỗi khi hiển thị thông tin phòng trọ</p>
        </div>
      </div>
    );
  }
};

export default Card;


