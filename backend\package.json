{"name": "backend", "version": "1.0.0", "description": "", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.14.3", "multer": "^1.4.5-lts.2", "node-cron": "^4.2.1", "nodemailer": "^7.0.3", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}