const cron = require('node-cron');
const Room = require('../models/Room');
const { createTransporter } = require('../utils/emailUtils');
const { createHighlightExpiryEmailTemplate } = require('../templates/highlightExpiryEmail');
const socketService = require('./socketService');

class ScheduledService {
  constructor() {
    this.jobs = [];
  }

  // Khởi tạo tất cả scheduled jobs
  init() {
    console.log('[ScheduledService] Đang khởi tạo scheduled jobs...');
    
    // Job hạ cấp tin nổi bật hết hạn - chạy mỗi phút
    this.startHighlightExpiryJob();
    
    console.log(`[ScheduledService] Đã khởi tạo ${this.jobs.length} scheduled jobs`);
  }

  // Job kiểm tra và hạ cấp tin nổi bật hết hạn
  startHighlightExpiryJob() {
    console.log('[ScheduledService] Đang tạo cron job...');

    const job = cron.schedule('* * * * *', async () => {
      console.log('[ScheduledService] Cron job đang chạy...', new Date().toISOString());
      try {
        await this.processExpiredHighlights();
      } catch (error) {
        console.error('[ScheduledService] Lỗi khi xử lý tin nổi bật hết hạn:', error);
      }
    });

    // Start job ngay lập tức
    job.start();

    this.jobs.push({
      name: 'highlightExpiry',
      job: job,
      description: 'Kiểm tra và hạ cấp tin nổi bật hết hạn'
    });

    console.log('[ScheduledService] Đã tạo và khởi động job hạ cấp tin nổi bật (chạy mỗi phút)');
  }

  // Xử lý logic hạ cấp tin nổi bật hết hạn
  async processExpiredHighlights() {
    const now = new Date();
    const startTime = Date.now();

    try {
      // Tìm các tin nổi bật đã hết hạn với thông tin chi tiết
      const expiredRooms = await Room.find({
        isHighlighted: true,
        highlightExpiry: { $lte: now }
      }).select('_id title highlightExpiry user address price images').populate('user', 'fullName email');

      if (expiredRooms.length === 0) {
        // Không log khi không có tin hết hạn để tránh spam log
        return { processed: 0, duration: Date.now() - startTime };
      }

      console.log(`[ScheduledService] Tìm thấy ${expiredRooms.length} tin nổi bật hết hạn`);

      // Log chi tiết các tin sẽ hạ cấp
      expiredRooms.forEach(room => {
        const expiredMinutes = Math.floor((now - new Date(room.highlightExpiry)) / (1000 * 60));
        console.log(`[ScheduledService] - "${room.title}" (${room._id}) - Hết hạn ${expiredMinutes} phút trước`);
      });

      // Cập nhật hàng loạt để tối ưu performance
      const result = await Room.updateMany(
        {
          isHighlighted: true,
          highlightExpiry: { $lte: now }
        },
        {
          $set: {
            isHighlighted: false
          },
          $unset: {
            highlightExpiry: 1
          }
        }
      );

      const duration = Date.now() - startTime;
      console.log(`[ScheduledService] Đã hạ cấp ${result.modifiedCount}/${expiredRooms.length} tin nổi bật trong ${duration}ms`);

      // Gửi email notification và WebSocket events cho từng tin đã hạ cấp
      if (result.modifiedCount > 0) {
        await this.sendNotificationsForExpiredRooms(expiredRooms.slice(0, result.modifiedCount));
      }

      // Thống kê kết quả
      if (result.modifiedCount !== expiredRooms.length) {
        console.warn(`[ScheduledService] Cảnh báo: Chỉ cập nhật được ${result.modifiedCount}/${expiredRooms.length} tin`);
      }

      return {
        processed: result.modifiedCount,
        found: expiredRooms.length,
        emailsSent: result.modifiedCount,
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[ScheduledService] Lỗi khi xử lý tin nổi bật hết hạn sau ${duration}ms:`, error);

      // Log thêm thông tin debug
      if (error.name === 'MongoError' || error.name === 'MongooseError') {
        console.error('[ScheduledService] Database error details:', {
          name: error.name,
          code: error.code,
          message: error.message
        });
      }

      throw error;
    }
  }

  // Gửi notifications (email + WebSocket) cho các tin đã hết hạn
  async sendNotificationsForExpiredRooms(expiredRooms) {
    const emailResults = {
      sent: 0,
      failed: 0,
      errors: []
    };

    for (const room of expiredRooms) {
      try {
        // Gửi WebSocket notification
        if (room.user) {
          socketService.emitHighlightExpired({
            roomId: room._id.toString(),
            title: room.title,
            userId: room.user._id.toString(),
            userEmail: room.user.email,
            userFullName: room.user.fullName
          });
        }

        // Gửi email notification
        if (room.user && room.user.email) {
          await this.sendHighlightExpiryEmail(room, room.user);
          emailResults.sent++;
          console.log(`[ScheduledService] ✓ Đã gửi email cho tin "${room.title}" đến ${room.user.email}`);
        } else {
          console.warn(`[ScheduledService] ⚠ Không có email cho tin "${room.title}"`);
        }

      } catch (error) {
        emailResults.failed++;
        emailResults.errors.push({
          roomId: room._id,
          title: room.title,
          error: error.message
        });
        console.error(`[ScheduledService] ✗ Lỗi gửi notification cho tin "${room.title}":`, error);
      }
    }

    console.log(`[ScheduledService] Email notifications: ${emailResults.sent} sent, ${emailResults.failed} failed`);
    return emailResults;
  }

  // Gửi email thông báo hết hạn tin nổi bật
  async sendHighlightExpiryEmail(roomData, userData) {
    try {
      // Tạo email content từ template
      const emailTemplate = createHighlightExpiryEmailTemplate(roomData, userData);

      // Lấy transporter
      const transporter = await createTransporter();

      // Cấu hình email
      const mailOptions = {
        from: `"Hệ thống Tìm Phòng Trọ" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
        to: userData.email,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
        text: emailTemplate.text
      };

      // Gửi email
      const result = await transporter.sendMail(mailOptions);

      console.log(`[ScheduledService] Email sent successfully to ${userData.email}, messageId: ${result.messageId}`);
      return { success: true, messageId: result.messageId };

    } catch (error) {
      console.error(`[ScheduledService] Failed to send email to ${userData.email}:`, error);
      throw error;
    }
  }

  // Bắt đầu tất cả jobs
  startAll() {
    console.log('[ScheduledService] Đang khởi động tất cả scheduled jobs...');

    this.jobs.forEach(({ name, job, description }) => {
      try {
        if (!job.running) {
          job.start();
          console.log(`[ScheduledService] ✓ Đã khởi động job: ${name} - ${description}`);
        } else {
          console.log(`[ScheduledService] ✓ Job đã chạy: ${name} - ${description}`);
        }
      } catch (error) {
        console.error(`[ScheduledService] ✗ Lỗi khởi động job ${name}:`, error);
      }
    });

    console.log(`[ScheduledService] Đã khởi động ${this.jobs.length} scheduled jobs`);
  }

  // Dừng tất cả jobs
  stopAll() {
    console.log('[ScheduledService] Đang dừng tất cả scheduled jobs...');
    
    this.jobs.forEach(({ name, job }) => {
      try {
        job.stop();
        console.log(`[ScheduledService] ✓ Đã dừng job: ${name}`);
      } catch (error) {
        console.error(`[ScheduledService] ✗ Lỗi dừng job ${name}:`, error);
      }
    });

    this.jobs = [];
    console.log('[ScheduledService] Đã dừng tất cả scheduled jobs');
  }

  // Lấy trạng thái của tất cả jobs
  getStatus() {
    return this.jobs.map(({ name, job, description }) => {
      // Kiểm tra trạng thái job từ node-cron
      const isRunning = job && typeof job.running === 'boolean' ? job.running : false;
      const isScheduled = job && typeof job.scheduled === 'boolean' ? job.scheduled : false;

      return {
        name,
        description,
        running: isRunning,
        scheduled: isScheduled,
        // Thêm thông tin debug
        jobExists: !!job,
        jobType: typeof job
      };
    });
  }

  // Chạy thủ công job hạ cấp tin nổi bật (để test)
  async runHighlightExpiryJobManually() {
    console.log('[ScheduledService] Chạy thủ công job hạ cấp tin nổi bật...');
    try {
      const result = await this.processExpiredHighlights();
      console.log('[ScheduledService] Hoàn thành chạy thủ công job hạ cấp tin nổi bật');
      return result;
    } catch (error) {
      console.error('[ScheduledService] Lỗi khi chạy thủ công job hạ cấp tin nổi bật:', error);
      throw error;
    }
  }
}

// Export singleton instance
const scheduledService = new ScheduledService();

module.exports = scheduledService;
