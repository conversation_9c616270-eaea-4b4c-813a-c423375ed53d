const cron = require('node-cron');
const Room = require('../models/Room');
const { createTransporter } = require('../utils/emailUtils');
const { createHighlightExpiryEmailTemplate } = require('../templates/highlightExpiryEmail');
const socketService = require('./socketService');

class ScheduledService {
  constructor() {
    this.jobs = [];
    this.emailStats = {
      totalSent: 0,
      totalFailed: 0,
      totalRetries: 0,
      lastEmailSent: null,
      lastError: null,
      dailyStats: new Map() // Date -> {sent, failed, retries}
    };
  }

  // Khởi tạo tất cả scheduled jobs
  init() {
    console.log('[ScheduledService] Đang khởi tạo scheduled jobs...');
    
    // Job hạ cấp tin nổi bật hết hạn - chạy mỗi phút
    this.startHighlightExpiryJob();
    
    console.log(`[ScheduledService] Đã khởi tạo ${this.jobs.length} scheduled jobs`);
  }

  // Job kiểm tra và hạ cấp tin nổi bật hết hạn
  startHighlightExpiryJob() {
    console.log('[ScheduledService] Đang tạo cron job...');

    const job = cron.schedule('*/30 * * * * *', async () => {
      console.log('[ScheduledService] Cron job đang chạy...', new Date().toISOString());
      try {
        await this.processExpiredHighlights();
      } catch (error) {
        console.error('[ScheduledService] Lỗi khi xử lý tin nổi bật hết hạn:', error);
      }
    });

    // Start job ngay lập tức
    job.start();

    this.jobs.push({
      name: 'highlightExpiry',
      job: job,
      description: 'Kiểm tra và hạ cấp tin nổi bật hết hạn'
    });

    console.log('[ScheduledService] Đã tạo và khởi động job hạ cấp tin nổi bật (chạy mỗi phút)');
  }

  // Xử lý logic hạ cấp tin nổi bật hết hạn
  async processExpiredHighlights() {
    const now = new Date();
    const startTime = Date.now();

    try {
      // Tìm các tin nổi bật đã hết hạn với thông tin chi tiết
      const expiredRooms = await Room.find({
        isHighlighted: true,
        highlightExpiry: { $lte: now }
      }).select('_id title highlightExpiry user address price images').populate('user', 'fullName email');

      if (expiredRooms.length === 0) {
        // Không log khi không có tin hết hạn để tránh spam log
        return { processed: 0, duration: Date.now() - startTime };
      }

      console.log(`[ScheduledService] Tìm thấy ${expiredRooms.length} tin nổi bật hết hạn`);

      // Log chi tiết các tin sẽ hạ cấp
      expiredRooms.forEach(room => {
        const expiredMinutes = Math.floor((now - new Date(room.highlightExpiry)) / (1000 * 60));
        console.log(`[ScheduledService] - "${room.title}" (${room._id}) - Hết hạn ${expiredMinutes} phút trước`);
      });

      // Cập nhật hàng loạt để tối ưu performance
      const result = await Room.updateMany(
        {
          isHighlighted: true,
          highlightExpiry: { $lte: now }
        },
        {
          $set: {
            isHighlighted: false
          },
          $unset: {
            highlightExpiry: 1
          }
        }
      );

      const duration = Date.now() - startTime;
      console.log(`[ScheduledService] Đã hạ cấp ${result.modifiedCount}/${expiredRooms.length} tin nổi bật trong ${duration}ms`);

      // Gửi email notification và WebSocket events cho từng tin đã hạ cấp
      if (result.modifiedCount > 0) {
        await this.sendNotificationsForExpiredRooms(expiredRooms.slice(0, result.modifiedCount));
      }

      // Thống kê kết quả
      if (result.modifiedCount !== expiredRooms.length) {
        console.warn(`[ScheduledService] Cảnh báo: Chỉ cập nhật được ${result.modifiedCount}/${expiredRooms.length} tin`);
      }

      return {
        processed: result.modifiedCount,
        found: expiredRooms.length,
        emailsSent: result.modifiedCount,
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[ScheduledService] Lỗi khi xử lý tin nổi bật hết hạn sau ${duration}ms:`, error);

      // Log thêm thông tin debug
      if (error.name === 'MongoError' || error.name === 'MongooseError') {
        console.error('[ScheduledService] Database error details:', {
          name: error.name,
          code: error.code,
          message: error.message
        });
      }

      throw error;
    }
  }

  // Gửi notifications (email + WebSocket) cho các tin đã hết hạn
  async sendNotificationsForExpiredRooms(expiredRooms) {
    const emailResults = {
      sent: 0,
      failed: 0,
      errors: []
    };

    for (const room of expiredRooms) {
      try {
        // Gửi WebSocket notification
        if (room.user) {
          socketService.emitHighlightExpired({
            roomId: room._id.toString(),
            title: room.title,
            userId: room.user._id.toString(),
            userEmail: room.user.email,
            userFullName: room.user.fullName
          });
        }

        // Gửi email notification
        if (room.user && room.user.email) {
          const emailResult = await this.sendHighlightExpiryEmail(room, room.user);

          if (emailResult.success) {
            emailResults.sent++;
            this.updateEmailStats('sent', emailResult.retryCount);
            console.log(`[ScheduledService] ✓ Đã gửi email cho tin "${room.title}" đến ${room.user.email}`);
          } else {
            emailResults.failed++;
            emailResults.errors.push({
              roomId: room._id,
              title: room.title,
              email: room.user.email,
              error: emailResult.error,
              retryCount: emailResult.retryCount
            });
            this.updateEmailStats('failed', emailResult.retryCount);
            console.error(`[ScheduledService] ✗ Không thể gửi email cho tin "${room.title}": ${emailResult.error}`);
          }
        } else {
          console.warn(`[ScheduledService] ⚠ Không có email cho tin "${room.title}"`);
        }

      } catch (error) {
        emailResults.failed++;
        emailResults.errors.push({
          roomId: room._id,
          title: room.title,
          error: error.message
        });
        console.error(`[ScheduledService] ✗ Lỗi gửi notification cho tin "${room.title}":`, error);
      }
    }

    console.log(`[ScheduledService] Email notifications: ${emailResults.sent} sent, ${emailResults.failed} failed`);
    return emailResults;
  }

  // Gửi email thông báo hết hạn tin nổi bật với retry mechanism
  async sendHighlightExpiryEmail(roomData, userData, retryCount = 0) {
    const maxRetries = 3;
    const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff

    try {
      console.log(`[ScheduledService] Attempting to send email to ${userData.email} (attempt ${retryCount + 1}/${maxRetries + 1})`);

      // Validate email address
      if (!userData.email || !this.isValidEmail(userData.email)) {
        throw new Error(`Invalid email address: ${userData.email}`);
      }

      // Tạo email content từ template
      const emailTemplate = createHighlightExpiryEmailTemplate(roomData, userData);

      // Lấy transporter với timeout
      const transporter = await this.createTransporterWithTimeout();

      // Cấu hình email
      const mailOptions = {
        from: `"Hệ thống Tìm Phòng Trọ" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
        to: userData.email,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
        text: emailTemplate.text,
        // Thêm headers để tracking
        headers: {
          'X-Room-ID': roomData._id.toString(),
          'X-User-ID': userData._id ? userData._id.toString() : 'unknown',
          'X-Email-Type': 'highlight-expiry',
          'X-Retry-Count': retryCount.toString()
        }
      };

      // Gửi email với timeout
      const result = await Promise.race([
        transporter.sendMail(mailOptions),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Email timeout after 30 seconds')), 30000)
        )
      ]);

      console.log(`[ScheduledService] ✓ Email sent successfully to ${userData.email}`);
      console.log(`[ScheduledService] Email details: messageId=${result.messageId}, response=${result.response}`);

      return {
        success: true,
        messageId: result.messageId,
        response: result.response,
        retryCount,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorDetails = {
        email: userData.email,
        roomId: roomData._id,
        roomTitle: roomData.title,
        error: error.message,
        errorCode: error.code,
        errorStack: error.stack,
        retryCount,
        timestamp: new Date().toISOString()
      };

      console.error(`[ScheduledService] ✗ Email sending failed (attempt ${retryCount + 1}):`, errorDetails);

      // Retry logic
      if (retryCount < maxRetries && this.shouldRetry(error)) {
        console.log(`[ScheduledService] Retrying email send in ${retryDelay}ms...`);

        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return await this.sendHighlightExpiryEmail(roomData, userData, retryCount + 1);
      }

      // Log final failure
      console.error(`[ScheduledService] ✗ Email sending failed permanently after ${retryCount + 1} attempts:`, errorDetails);

      // Don't throw error to prevent breaking the entire cron job
      return {
        success: false,
        error: error.message,
        errorCode: error.code,
        retryCount,
        finalFailure: true,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Validate email address format
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Create transporter with timeout and error handling
  async createTransporterWithTimeout() {
    try {
      const transporter = await Promise.race([
        createTransporter(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Transporter creation timeout')), 10000)
        )
      ]);

      // Test connection
      await Promise.race([
        transporter.verify(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('SMTP connection test timeout')), 10000)
        )
      ]);

      return transporter;
    } catch (error) {
      console.error('[ScheduledService] Transporter creation/verification failed:', error);
      throw new Error(`Email service unavailable: ${error.message}`);
    }
  }

  // Determine if error should trigger retry
  shouldRetry(error) {
    const retryableErrors = [
      'ETIMEDOUT',
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
      'timeout',
      'network',
      'temporary'
    ];

    const errorMessage = error.message.toLowerCase();
    const errorCode = error.code;

    return retryableErrors.some(retryableError =>
      errorMessage.includes(retryableError) || errorCode === retryableError
    );
  }

  // Bắt đầu tất cả jobs
  startAll() {
    console.log('[ScheduledService] Đang khởi động tất cả scheduled jobs...');

    this.jobs.forEach(({ name, job, description }) => {
      try {
        if (!job.running) {
          job.start();
          console.log(`[ScheduledService] ✓ Đã khởi động job: ${name} - ${description}`);
        } else {
          console.log(`[ScheduledService] ✓ Job đã chạy: ${name} - ${description}`);
        }
      } catch (error) {
        console.error(`[ScheduledService] ✗ Lỗi khởi động job ${name}:`, error);
      }
    });

    console.log(`[ScheduledService] Đã khởi động ${this.jobs.length} scheduled jobs`);
  }

  // Dừng tất cả jobs
  stopAll() {
    console.log('[ScheduledService] Đang dừng tất cả scheduled jobs...');
    
    this.jobs.forEach(({ name, job }) => {
      try {
        job.stop();
        console.log(`[ScheduledService] ✓ Đã dừng job: ${name}`);
      } catch (error) {
        console.error(`[ScheduledService] ✗ Lỗi dừng job ${name}:`, error);
      }
    });

    this.jobs = [];
    console.log('[ScheduledService] Đã dừng tất cả scheduled jobs');
  }

  // Lấy trạng thái của tất cả jobs
  getStatus() {
    return this.jobs.map(({ name, job, description }) => {
      // Kiểm tra trạng thái job từ node-cron
      const isRunning = job && typeof job.running === 'boolean' ? job.running : false;
      const isScheduled = job && typeof job.scheduled === 'boolean' ? job.scheduled : false;

      return {
        name,
        description,
        running: isRunning,
        scheduled: isScheduled,
        // Thêm thông tin debug
        jobExists: !!job,
        jobType: typeof job
      };
    });
  }

  // Chạy thủ công job hạ cấp tin nổi bật (để test)
  async runHighlightExpiryJobManually() {
    console.log('[ScheduledService] Chạy thủ công job hạ cấp tin nổi bật...');
    try {
      const result = await this.processExpiredHighlights();
      console.log('[ScheduledService] Hoàn thành chạy thủ công job hạ cấp tin nổi bật');
      return result;
    } catch (error) {
      console.error('[ScheduledService] Lỗi khi chạy thủ công job hạ cấp tin nổi bật:', error);
      throw error;
    }
  }

  // Update email statistics
  updateEmailStats(type, retryCount = 0) {
    const today = new Date().toISOString().split('T')[0];

    // Update global stats
    if (type === 'sent') {
      this.emailStats.totalSent++;
      this.emailStats.lastEmailSent = new Date().toISOString();
    } else if (type === 'failed') {
      this.emailStats.totalFailed++;
      this.emailStats.lastError = new Date().toISOString();
    }

    if (retryCount > 0) {
      this.emailStats.totalRetries += retryCount;
    }

    // Update daily stats
    if (!this.emailStats.dailyStats.has(today)) {
      this.emailStats.dailyStats.set(today, { sent: 0, failed: 0, retries: 0 });
    }

    const dailyStats = this.emailStats.dailyStats.get(today);
    if (type === 'sent') {
      dailyStats.sent++;
    } else if (type === 'failed') {
      dailyStats.failed++;
    }
    dailyStats.retries += retryCount;

    // Keep only last 30 days
    if (this.emailStats.dailyStats.size > 30) {
      const oldestDate = Array.from(this.emailStats.dailyStats.keys()).sort()[0];
      this.emailStats.dailyStats.delete(oldestDate);
    }
  }

  // Get email statistics
  getEmailStats() {
    const today = new Date().toISOString().split('T')[0];
    const todayStats = this.emailStats.dailyStats.get(today) || { sent: 0, failed: 0, retries: 0 };

    return {
      global: {
        totalSent: this.emailStats.totalSent,
        totalFailed: this.emailStats.totalFailed,
        totalRetries: this.emailStats.totalRetries,
        successRate: this.emailStats.totalSent + this.emailStats.totalFailed > 0
          ? ((this.emailStats.totalSent / (this.emailStats.totalSent + this.emailStats.totalFailed)) * 100).toFixed(2) + '%'
          : 'N/A',
        lastEmailSent: this.emailStats.lastEmailSent,
        lastError: this.emailStats.lastError
      },
      today: {
        date: today,
        sent: todayStats.sent,
        failed: todayStats.failed,
        retries: todayStats.retries,
        successRate: todayStats.sent + todayStats.failed > 0
          ? ((todayStats.sent / (todayStats.sent + todayStats.failed)) * 100).toFixed(2) + '%'
          : 'N/A'
      },
      last7Days: this.getLast7DaysStats()
    };
  }

  // Get last 7 days statistics
  getLast7DaysStats() {
    const last7Days = [];
    const today = new Date();

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const stats = this.emailStats.dailyStats.get(dateStr) || { sent: 0, failed: 0, retries: 0 };
      last7Days.push({
        date: dateStr,
        ...stats
      });
    }

    return last7Days;
  }

  // Log email statistics summary
  logEmailStatsSummary() {
    const stats = this.getEmailStats();
    console.log(`[ScheduledService] Email Stats Summary:`);
    console.log(`  Global: ${stats.global.totalSent} sent, ${stats.global.totalFailed} failed, ${stats.global.successRate} success rate`);
    console.log(`  Today: ${stats.today.sent} sent, ${stats.today.failed} failed, ${stats.today.retries} retries`);

    if (stats.global.lastError) {
      console.log(`  Last error: ${stats.global.lastError}`);
    }
  }
}

// Export singleton instance
const scheduledService = new ScheduledService();

module.exports = scheduledService;
