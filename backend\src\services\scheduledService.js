const cron = require('node-cron');
const Room = require('../models/Room');

class ScheduledService {
  constructor() {
    this.jobs = [];
  }

  // Khởi tạo tất cả scheduled jobs
  init() {
    console.log('[ScheduledService] Đang khởi tạo scheduled jobs...');
    
    // Job hạ cấp tin nổi bật hết hạn - chạy mỗi phút
    this.startHighlightExpiryJob();
    
    console.log(`[ScheduledService] Đã khởi tạo ${this.jobs.length} scheduled jobs`);
  }

  // Job kiểm tra và hạ cấp tin nổi bật hết hạn
  startHighlightExpiryJob() {
    const job = cron.schedule('* * * * *', async () => {
      try {
        await this.processExpiredHighlights();
      } catch (error) {
        console.error('[ScheduledService] Lỗi khi xử lý tin nổi bật hết hạn:', error);
      }
    }, {
      scheduled: false, // Không tự động start
      timezone: 'Asia/<PERSON>_Chi_<PERSON>'
    });

    this.jobs.push({
      name: 'highlightExpiry',
      job: job,
      description: 'Kiểm tra và hạ cấp tin nổi bật hết hạn'
    });

    console.log('[ScheduledService] Đã tạo job hạ cấp tin nổi bật (chạy mỗi phút)');
  }

  // Xử lý logic hạ cấp tin nổi bật hết hạn
  async processExpiredHighlights() {
    const now = new Date();
    const startTime = Date.now();

    try {
      // Tìm các tin nổi bật đã hết hạn với thông tin chi tiết
      const expiredRooms = await Room.find({
        isHighlighted: true,
        highlightExpiry: { $lte: now }
      }).select('_id title highlightExpiry user').populate('user', 'fullName email');

      if (expiredRooms.length === 0) {
        // Không log khi không có tin hết hạn để tránh spam log
        return { processed: 0, duration: Date.now() - startTime };
      }

      console.log(`[ScheduledService] Tìm thấy ${expiredRooms.length} tin nổi bật hết hạn`);

      // Log chi tiết các tin sẽ hạ cấp
      expiredRooms.forEach(room => {
        const expiredMinutes = Math.floor((now - new Date(room.highlightExpiry)) / (1000 * 60));
        console.log(`[ScheduledService] - "${room.title}" (${room._id}) - Hết hạn ${expiredMinutes} phút trước`);
      });

      // Cập nhật hàng loạt để tối ưu performance
      const result = await Room.updateMany(
        {
          isHighlighted: true,
          highlightExpiry: { $lte: now }
        },
        {
          $set: {
            isHighlighted: false
          },
          $unset: {
            highlightExpiry: 1
          }
        }
      );

      const duration = Date.now() - startTime;
      console.log(`[ScheduledService] Đã hạ cấp ${result.modifiedCount}/${expiredRooms.length} tin nổi bật trong ${duration}ms`);

      // Thống kê kết quả
      if (result.modifiedCount !== expiredRooms.length) {
        console.warn(`[ScheduledService] Cảnh báo: Chỉ cập nhật được ${result.modifiedCount}/${expiredRooms.length} tin`);
      }

      return {
        processed: result.modifiedCount,
        found: expiredRooms.length,
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[ScheduledService] Lỗi khi xử lý tin nổi bật hết hạn sau ${duration}ms:`, error);

      // Log thêm thông tin debug
      if (error.name === 'MongoError' || error.name === 'MongooseError') {
        console.error('[ScheduledService] Database error details:', {
          name: error.name,
          code: error.code,
          message: error.message
        });
      }

      throw error;
    }
  }

  // Bắt đầu tất cả jobs
  startAll() {
    console.log('[ScheduledService] Đang khởi động tất cả scheduled jobs...');
    
    this.jobs.forEach(({ name, job, description }) => {
      try {
        job.start();
        console.log(`[ScheduledService] ✓ Đã khởi động job: ${name} - ${description}`);
      } catch (error) {
        console.error(`[ScheduledService] ✗ Lỗi khởi động job ${name}:`, error);
      }
    });

    console.log(`[ScheduledService] Đã khởi động ${this.jobs.length} scheduled jobs`);
  }

  // Dừng tất cả jobs
  stopAll() {
    console.log('[ScheduledService] Đang dừng tất cả scheduled jobs...');
    
    this.jobs.forEach(({ name, job }) => {
      try {
        job.stop();
        console.log(`[ScheduledService] ✓ Đã dừng job: ${name}`);
      } catch (error) {
        console.error(`[ScheduledService] ✗ Lỗi dừng job ${name}:`, error);
      }
    });

    this.jobs = [];
    console.log('[ScheduledService] Đã dừng tất cả scheduled jobs');
  }

  // Lấy trạng thái của tất cả jobs
  getStatus() {
    return this.jobs.map(({ name, job, description }) => ({
      name,
      description,
      running: job.running || false
    }));
  }

  // Chạy thủ công job hạ cấp tin nổi bật (để test)
  async runHighlightExpiryJobManually() {
    console.log('[ScheduledService] Chạy thủ công job hạ cấp tin nổi bật...');
    try {
      await this.processExpiredHighlights();
      console.log('[ScheduledService] Hoàn thành chạy thủ công job hạ cấp tin nổi bật');
    } catch (error) {
      console.error('[ScheduledService] Lỗi khi chạy thủ công job hạ cấp tin nổi bật:', error);
      throw error;
    }
  }
}

// Export singleton instance
const scheduledService = new ScheduledService();

module.exports = scheduledService;
