import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { toast } from 'react-toastify'
import Card from '../components/Card'
import Button from '../components/Button'
import useRooms from '../hooks/useRooms'
import useSocket from '../hooks/useSocket'

const HomePage = () => {
  const { rooms, isLoading, error, fetchRooms } = useRooms();
  const socketHook = useSocket();
  const { addEventListener } = socketHook || {};
  const [highlightedRooms, setHighlightedRooms] = useState([]);
  const [regularRooms, setRegularRooms] = useState([]);

  // State cho image slider
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isSliderPaused, setIsSliderPaused] = useState(false);

  // Debug socket connection status
  useEffect(() => {
    const checkSocketStatus = () => {
      console.log('[HomePage] 🔍 Socket Status Check:', {
        socketService: !!window.socketService,
        isConnected: window.socketService?.isConnected(),
        socketId: window.socketService?.socket?.id,
        addEventListener: !!addEventListener,
        socketHook: !!socketHook
      });
    };

    checkSocketStatus();

    // Check again after 2 seconds
    const timer = setTimeout(checkSocketStatus, 2000);
    return () => clearTimeout(timer);
  }, [addEventListener, socketHook]);

  // Dữ liệu cho slider với 3 ảnh banner
  const sliderData = [
    {
      id: 1,
      image: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1920&h=800&fit=crop&crop=center',
      title: 'Tìm Phòng Trọ Nhanh Chóng & Dễ Dàng',
      description: 'Kết nối bạn với hàng ngàn phòng trọ chất lượng trên khắp Việt Nam',
      fallbackImage: 'https://via.placeholder.com/1920x800/3B82F6/FFFFFF?text=Phòng+Trọ+Hiện+Đại'
    },
    {
      id: 2,
      image: 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=1920&h=800&fit=crop&crop=center',
      title: 'Phòng Trọ Chất Lượng Cao',
      description: 'Đa dạng lựa chọn từ phòng trọ sinh viên đến căn hộ mini cao cấp',
      fallbackImage: 'https://via.placeholder.com/1920x800/059669/FFFFFF?text=Phòng+Trọ+Chất+Lượng'
    },
    {
      id: 3,
      image: 'https://images.unsplash.com/photo-1484154218962-a197022b5858?w=1920&h=800&fit=crop&crop=center',
      title: 'Giá Cả Hợp Lý - Tiện Nghi Đầy Đủ',
      description: 'Tìm kiếm phòng trọ phù hợp với ngân sách và nhu cầu của bạn',
      fallbackImage: 'https://via.placeholder.com/1920x800/DC2626/FFFFFF?text=Giá+Cả+Hợp+Lý'
    }
  ];

  // Dữ liệu tĩnh cho khu vực phổ biến - 3 thành phố chính của Việt Nam
  const popularAreas = [
    {
      name: 'Thành phố Hà Nội',
      description: 'Thủ đô - Trung tâm chính trị, văn hóa',
      image: '../assets/hanoi.jpg'
    },
    {
      name: 'Thành phố Hồ Chí Minh',
      description: 'Thành phố lớn nhất - Trung tâm kinh tế',
      image: '../assets/hochiminh.jpg'
    },
    {
      name: 'Thành phố Đà Nẵng',
      description: 'Thành phố đáng sống - Miền Trung',
      image: '../assets/danang.jpg'
    }
  ];

  // Auto-play slider effect với pause functionality
  useEffect(() => {
    if (isSliderPaused) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % sliderData.length);
    }, 5000); // Chuyển slide sau 5 giây

    return () => clearInterval(interval);
  }, [sliderData.length, isSliderPaused]);

  // Preload images để cải thiện performance
  useEffect(() => {
    sliderData.forEach((slide) => {
      const img = new Image();
      img.src = slide.image;
      // Preload fallback image cũng
      const fallbackImg = new Image();
      fallbackImg.src = slide.fallbackImage;
    });
  }, [sliderData]);

  // Lấy danh sách phòng trọ khi component được mount
  useEffect(() => {
    const loadRooms = async () => {
      try {
        // Lấy phòng trọ nổi bật trước
        const highlightedResponse = await fetchRooms({
          status: 'available',
          isHighlighted: true,
          limit: 10,
          sort: '-createdAt'
        });

        // Lấy phòng trọ thường (không nổi bật)
        const regularResponse = await fetchRooms({
          status: 'available',
          isHighlighted: false,
          limit: 15, // Tăng limit để đảm bảo có đủ phòng thường hiển thị
          sort: '-createdAt'
        });

        if (highlightedResponse.success && highlightedResponse.data.rooms) {
          setHighlightedRooms(highlightedResponse.data.rooms);
          console.log('Phòng trọ nổi bật:', highlightedResponse.data.rooms.length);
        }

        if (regularResponse.success && regularResponse.data.rooms) {
          let allRegularRooms = regularResponse.data.rooms;

          // Nếu không đủ 6 phòng thường, bổ sung từ tất cả phòng có sẵn
          if (allRegularRooms.length < 6) {
            const allRoomsResponse = await fetchRooms({
              status: 'available',
              limit: 10,
              sort: '-createdAt'
            });

            if (allRoomsResponse.success && allRoomsResponse.data.rooms) {
              // Lọc bỏ các phòng đã có trong regularRooms để tránh trùng lặp
              const existingIds = allRegularRooms.map(room => room._id);
              const additionalRooms = allRoomsResponse.data.rooms
                .filter(room => !existingIds.includes(room._id))
                .slice(0, 6 - allRegularRooms.length);

              allRegularRooms = [...allRegularRooms, ...additionalRooms];
            }
          }

          setRegularRooms(allRegularRooms);
          console.log('Phòng trọ thường:', regularResponse.data.rooms.length);
          console.log('Tổng phòng hiển thị:', allRegularRooms.length);
        }

      } catch (err) {
        console.error('Lỗi khi lấy danh sách phòng trọ:', err);
        toast.error('Không thể tải danh sách phòng trọ. Vui lòng thử lại sau.');
      }
    };

    loadRooms();
  }, [fetchRooms]);

  // Logic xử lý khi tin nổi bật hết hạn
  const handleRoomHighlightUpdatedLogic = (data) => {
    console.log('[HomePage] Processing room highlight update:', data);

    if (data.isHighlighted) {
      // Phòng được nâng cấp lên nổi bật
      console.log('[HomePage] Room upgraded to highlighted:', data.roomId);

      // Xóa khỏi danh sách phòng thường
      setRegularRooms(prev => prev.filter(room => room._id !== data.roomId));

      // Reload highlighted rooms để lấy phòng mới nâng cấp
      fetchRooms({
        status: 'available',
        isHighlighted: true,
        limit: 10,
        sort: '-createdAt'
      }).then(response => {
        if (response.success) {
          setHighlightedRooms(response.data.rooms);
          console.log('[HomePage] Highlighted rooms reloaded');
        }
      }).catch(err => {
        console.error('[HomePage] Error reloading highlighted rooms:', err);
      });

    } else {
      // Phòng hết hạn nổi bật -> chuyển thành thường
      console.log('[HomePage] Room expired from highlighted:', data.roomId);

      setHighlightedRooms(prev => {
        const expiredRoom = prev.find(room => room._id === data.roomId);
        if (expiredRoom) {
          console.log('[HomePage] Found expired room, moving to regular:', expiredRoom.title);

          // Thêm vào đầu danh sách phòng thường
          setRegularRooms(prevRegular => {
            // Kiểm tra xem phòng đã có trong danh sách chưa
            const exists = prevRegular.some(room => room._id === data.roomId);
            if (!exists) {
              // Tạo room object với trạng thái mới
              const updatedRoom = {
                ...expiredRoom,
                isHighlighted: false,
                highlightExpiry: null
              };

              // Thêm phòng vào đầu danh sách và giới hạn 6 phòng
              const newRegularRooms = [updatedRoom, ...prevRegular].slice(0, 6);
              console.log('[HomePage] ✅ Added expired room to regular list, total:', newRegularRooms.length);
              return newRegularRooms;
            }
            return prevRegular;
          });

          // Xóa khỏi danh sách nổi bật
          const newHighlighted = prev.filter(room => room._id !== data.roomId);
          console.log('[HomePage] ✅ Removed from highlighted list, remaining:', newHighlighted.length);
          return newHighlighted;
        }
        return prev;
      });

      // Hiển thị thông báo
      toast.info(`Tin đăng đã hết hạn gói nổi bật và được chuyển xuống danh sách tin thường`, {
        position: 'top-right',
        autoClose: 5000
      });
    }
  };

  const handleHighlightExpiredLogic = (data) => {
    console.log('[HomePage] Processing highlight expired:', data);
    // Trigger room highlight updated với isHighlighted: false
    handleRoomHighlightUpdatedLogic({
      roomId: data.roomId,
      isHighlighted: false,
      highlightExpiry: null,
      timestamp: data.timestamp
    });
  };

  // Lắng nghe WebSocket events cho real-time updates
  useEffect(() => {
    console.log('[HomePage] Setting up socket listeners...', {
      addEventListener: !!addEventListener,
      socketHook: !!socketHook,
      socketService: !!window.socketService,
      isConnected: window.socketService?.isConnected()
    });

    if (!addEventListener) {
      console.warn('[HomePage] ⚠️ addEventListener not available, trying direct socketService...');

      // Fallback: Sử dụng trực tiếp socketService
      if (window.socketService && window.socketService.isConnected()) {
        console.log('[HomePage] 🔄 Using direct socketService fallback');

        const handleRoomHighlightUpdatedDirect = (data) => {
          console.log('[HomePage] 🔄 Direct socket event - Room highlight updated:', data);
          handleRoomHighlightUpdatedLogic(data);
        };

        const handleHighlightExpiredDirect = (data) => {
          console.log('[HomePage] 💥 Direct socket event - Highlight expired:', data);
          handleHighlightExpiredLogic(data);
        };

        window.socketService.addEventListener('room_highlight_updated', handleRoomHighlightUpdatedDirect);
        window.socketService.addEventListener('highlight_expired', handleHighlightExpiredDirect);

        return () => {
          window.socketService?.removeEventListener('room_highlight_updated', handleRoomHighlightUpdatedDirect);
          window.socketService?.removeEventListener('highlight_expired', handleHighlightExpiredDirect);
        };
      }
      return;
    }

    const handleRoomHighlightUpdated = (data) => {
      console.log('[HomePage] 🔄 Room highlight updated event received:', data);
      handleRoomHighlightUpdatedLogic(data);
    };

    const handleHighlightExpired = (data) => {
      console.log('[HomePage] 💥 Highlight expired event received:', data);
      handleHighlightExpiredLogic(data);
    };

    const unsubscribeUpdated = addEventListener('room_highlight_updated', handleRoomHighlightUpdated);
    const unsubscribeExpired = addEventListener('highlight_expired', handleHighlightExpired);

    console.log('[HomePage] Socket listeners registered:', {
      updated: !!unsubscribeUpdated,
      expired: !!unsubscribeExpired
    });

    return () => {
      console.log('[HomePage] Cleaning up socket listeners...');
      if (unsubscribeUpdated && typeof unsubscribeUpdated === 'function') {
        unsubscribeUpdated();
      }
      if (unsubscribeExpired && typeof unsubscribeExpired === 'function') {
        unsubscribeExpired();
      }
    };
  }, [addEventListener, fetchRooms]);

  const handleSearch = (searchParams) => {
    console.log('Tìm kiếm với các tham số:', searchParams)
    // Xử lý tìm kiếm ở đây
  }

  // Manual test function để trigger events
  const testRealTimeUpdate = () => {
    console.log('[HomePage] 🧪 Testing real-time update manually...');

    if (highlightedRooms.length > 0) {
      const testRoom = highlightedRooms[0];
      console.log('[HomePage] Using test room:', testRoom.title);

      // Simulate room highlight updated event
      handleRoomHighlightUpdatedLogic({
        roomId: testRoom._id,
        isHighlighted: false,
        highlightExpiry: null,
        timestamp: new Date().toISOString()
      });
    } else {
      console.log('[HomePage] No highlighted rooms to test with');
      toast.info('Không có tin nổi bật để test. Hãy tạo tin nổi bật trước.', {
        position: 'top-right',
        autoClose: 3000
      });
    }
  };

  return (
    <div>
      {/* Development Test Button */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ position: 'fixed', top: '10px', right: '10px', zIndex: 9999 }}>
          <button
            onClick={testRealTimeUpdate}
            style={{
              padding: '8px 16px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            🧪 Test Real-time
          </button>
        </div>
      )}

      {/* Hero Slider Section */}
      <section
        className="relative h-[400px] md:h-[500px] overflow-hidden"
        onMouseEnter={() => setIsSliderPaused(true)}
        onMouseLeave={() => setIsSliderPaused(false)}
      >
        {/* Slider Container */}
        <div className="relative w-full h-full">
          {sliderData.map((slide, index) => (
            <div
              key={slide.id}
              className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
                index === currentSlide
                  ? 'opacity-100 transform translate-x-0'
                  : index < currentSlide
                    ? 'opacity-0 transform -translate-x-full'
                    : 'opacity-0 transform translate-x-full'
              }`}
            >
              {/* Background Image */}
              <div className="absolute inset-0">
                <img
                  src={slide.image}
                  alt={slide.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.src = slide.fallbackImage;
                  }}
                />
                {/* Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-40"></div>
              </div>

              {/* Content */}
              <div className="relative z-10 h-full flex items-center">
                <div className="container-custom">
                  <div className="max-w-4xl mx-auto text-center text-white px-4">
                    <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 md:mb-4 leading-tight drop-shadow-lg">
                      {slide.title}
                    </h1>
                    <p className="text-base sm:text-lg md:text-xl opacity-90 mb-6 md:mb-8 max-w-2xl mx-auto drop-shadow-md">
                      {slide.description}
                    </p>




                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Dots */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20">
          <div className="flex space-x-3">
            {sliderData.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? 'bg-white scale-125'
                    : 'bg-white bg-opacity-50 hover:bg-opacity-75'
                }`}
                aria-label={`Chuyển đến slide ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* Navigation Arrows - Hidden on mobile, visible on tablet+ */}
        <button
          onClick={() => setCurrentSlide((prev) => (prev - 1 + sliderData.length) % sliderData.length)}
          className="hidden md:block absolute left-4 top-1/2 transform -translate-y-1/2 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
          aria-label="Slide trước"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <button
          onClick={() => setCurrentSlide((prev) => (prev + 1) % sliderData.length)}
          className="hidden md:block absolute right-4 top-1/2 transform -translate-y-1/2 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
          aria-label="Slide tiếp theo"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>

        {/* Progress Bar */}
        <div className="absolute bottom-14 left-1/2 transform -translate-x-1/2 z-20 w-48 md:w-64 bg-white bg-opacity-20 rounded-full h-1 overflow-hidden">
          <div
            className="h-full bg-white transition-all duration-300 ease-out"
            style={{
              width: `${((currentSlide + 1) / sliderData.length) * 100}%`
            }}
          />
        </div>
      </section>

      {/* Phòng trọ nổi bật */}
      <section className="py-16 bg-gray-50">
        <div className="container-custom">
          <div className="flex justify-between items-center mb-10">
            <h2 className="text-3xl font-bold">Phòng Trọ Nổi Bật</h2>
            <Link to="/search?isHighlighted=true">
              <Button variant="outline">Xem tất cả</Button>
            </Link>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-500">Không thể tải danh sách phòng trọ. Vui lòng thử lại sau.</p>
            </div>
          ) : (
            <>
              {/* Hiển thị phòng trọ nổi bật */}
              {highlightedRooms.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                  {highlightedRooms.slice(0, 3).map((room) => (
                    <Card key={room._id} room={room} />
                  ))}
                </div>
              )}

              {/* Hiển thị phòng trọ thường */}
              <h3 className="text-2xl font-bold mb-6">Phòng Trọ Mới Nhất</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {regularRooms.slice(0, 6).map((room) => (
                  <Card key={room._id} room={room} />
                ))}
              </div>
            </>
          )}
        </div>
      </section>

      {/* Khu vực phổ biến */}
      <section className="py-16">
        <div className="container-custom">
          <h2 className="text-3xl font-bold mb-10 text-center">Khu Vực Phổ Biến</h2>
          <p className="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
            Khám phá các thành phố lớn với nhiều lựa chọn phòng trọ chất lượng
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {popularAreas.map((area) => (
              <Link
                key={area.name}
                to={`/search?city=${encodeURIComponent(area.name)}`}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="h-48 bg-gray-300 relative overflow-hidden">
                  <img
                    src={area.image}
                    alt={area.name}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                    onError={(e) => {
                      e.target.src = `https://via.placeholder.com/400x300/3B82F6/FFFFFF?text=${area.name.replace(' ', '+')}`;
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <h3 className="text-white text-2xl font-bold mb-1">{area.name}</h3>
                    <p className="text-white/90 text-sm">{area.description}</p>
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="bg-primary/10 p-3 rounded-full">
                      <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <span className="text-primary font-medium text-sm flex items-center">
                      Xem tất cả phòng trọ
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                      </svg>
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* Thống kê tổng quan */}
          <div className="mt-16 bg-gradient-to-r from-primary/5 to-primary-dark/5 rounded-2xl p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-center">
              <div>
                <h4 className="text-3xl font-bold text-primary mb-2">3</h4>
                <p className="text-gray-600">Thành phố lớn</p>
              </div>
              <div>
                <h4 className="text-3xl font-bold text-primary mb-2">24/7</h4>
                <p className="text-gray-600">Hỗ trợ khách hàng</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Tại sao chọn chúng tôi */}
      <section className="py-16 bg-gray-50">
        <div className="container-custom">
          <h2 className="text-3xl font-bold mb-12 text-center">Tại Sao Chọn Chúng Tôi</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">Tìm Kiếm Dễ Dàng</h3>
              <p className="text-gray-600">Công cụ tìm kiếm thông minh giúp bạn nhanh chóng tìm được phòng trọ phù hợp với nhu cầu.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">Thông Tin Đáng Tin Cậy</h3>
              <p className="text-gray-600">Thông tin phòng trọ được kiểm duyệt kỹ càng, đảm bảo tính chính xác và đáng tin cậy.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">Giá Cả Hợp Lý</h3>
              <p className="text-gray-600">Đa dạng mức giá phù hợp với ngân sách của mọi đối tượng người thuê.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 bg-primary text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl font-bold mb-4">Bạn có phòng trọ cần cho thuê?</h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Đăng tin ngay hôm nay để tiếp cận hàng ngàn người đang tìm kiếm phòng trọ mỗi ngày.
          </p>
        </div>
      </section>
    </div>
  )
}

export default HomePage
