<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Real-time Highlight Expiry</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        input, select { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
        .room-list { margin-top: 20px; }
        .room-item { padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        .room-item.highlighted { background: #fff8e1; border-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Real-time Highlight Expiry</h1>
        <p>Test tính năng tự động chuyển tin nổi bật thành tin thường</p>
        
        <div class="status info">
            <strong>Instructions:</strong><br>
            1. Chọn một phòng từ danh sách<br>
            2. Đặt thời gian hết hạn (phút)<br>
            3. Nhấn "Tạo tin nổi bật test"<br>
            4. Mở trang chủ trong tab khác<br>
            5. Quan sát phòng tự động chuyển từ nổi bật → thường
        </div>

        <div>
            <label>Chọn phòng:</label>
            <select id="roomSelect">
                <option value="">Đang tải...</option>
            </select>
            
            <label>Thời gian hết hạn (phút):</label>
            <input type="number" id="minutesInput" value="1" min="1" max="10">
            
            <button class="btn-primary" onclick="createTestHighlight()">Tạo tin nổi bật test</button>
            <button class="btn-success" onclick="loadRooms()">Reload danh sách phòng</button>
            <button class="btn-danger" onclick="runExpiryJob()">Chạy job hạ cấp</button>
        </div>

        <div id="roomsList" class="room-list"></div>
        
        <div id="logs" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-top: 20px;">
            <strong>Logs:</strong><br>
        </div>
    </div>

    <script>
        let rooms = [];

        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            logsDiv.appendChild(div);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }

        async function loadRooms() {
            try {
                log('📡 Loading rooms...', 'info');
                
                const response = await fetch('http://localhost:5000/api/rooms?limit=20');
                const data = await response.json();
                
                if (data.success) {
                    rooms = data.data.rooms;
                    
                    // Update select options
                    const select = document.getElementById('roomSelect');
                    select.innerHTML = '<option value="">Chọn phòng...</option>';
                    
                    rooms.forEach(room => {
                        const option = document.createElement('option');
                        option.value = room._id;
                        option.textContent = `${room.title} (${room.isHighlighted ? 'Nổi bật' : 'Thường'})`;
                        select.appendChild(option);
                    });
                    
                    // Update rooms list
                    displayRooms();
                    
                    log(`✅ Loaded ${rooms.length} rooms`, 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log(`❌ Error loading rooms: ${error.message}`, 'error');
            }
        }

        function displayRooms() {
            const container = document.getElementById('roomsList');
            container.innerHTML = '<h3>Danh sách phòng:</h3>';
            
            rooms.forEach(room => {
                const div = document.createElement('div');
                div.className = `room-item ${room.isHighlighted ? 'highlighted' : ''}`;
                
                let expiryInfo = '';
                if (room.isHighlighted && room.highlightExpiry) {
                    const expiry = new Date(room.highlightExpiry);
                    const now = new Date();
                    const timeLeft = expiry - now;
                    
                    if (timeLeft > 0) {
                        const minutes = Math.floor(timeLeft / (1000 * 60));
                        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                        expiryInfo = ` - Còn lại: ${minutes}:${seconds.toString().padStart(2, '0')}`;
                    } else {
                        expiryInfo = ' - Đã hết hạn';
                    }
                }
                
                div.innerHTML = `
                    <strong>${room.title}</strong><br>
                    Trạng thái: <span style="color: ${room.isHighlighted ? '#ffc107' : '#6c757d'}">
                        ${room.isHighlighted ? 'Tin nổi bật' : 'Tin thường'}${expiryInfo}
                    </span><br>
                    Giá: ${room.price?.toLocaleString() || 0} VNĐ/tháng<br>
                    ID: ${room._id}
                `;
                
                container.appendChild(div);
            });
        }

        async function createTestHighlight() {
            const roomId = document.getElementById('roomSelect').value;
            const minutes = parseInt(document.getElementById('minutesInput').value);
            
            if (!roomId) {
                log('❌ Vui lòng chọn phòng', 'error');
                return;
            }
            
            if (!minutes || minutes < 1) {
                log('❌ Vui lòng nhập thời gian hợp lệ', 'error');
                return;
            }
            
            try {
                log(`🚀 Creating test highlight for room ${roomId} (${minutes} minutes)...`, 'info');
                
                const response = await fetch('http://localhost:5000/api/test/create-test-highlight', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        roomId: roomId,
                        minutes: minutes
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Test highlight created successfully!`, 'success');
                    log(`⏰ Will expire in ${minutes} minutes`, 'info');
                    log(`🔗 Open homepage in another tab to see real-time update`, 'info');
                    
                    // Reload rooms to show updated status
                    setTimeout(loadRooms, 1000);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log(`❌ Error creating test highlight: ${error.message}`, 'error');
            }
        }

        async function runExpiryJob() {
            try {
                log('🔄 Running expiry job manually...', 'info');
                
                const response = await fetch('http://localhost:5000/api/test/run-highlight-expiry-job', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Expiry job completed: ${data.data.processed} rooms processed`, 'success');
                    setTimeout(loadRooms, 1000);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                log(`❌ Error running expiry job: ${error.message}`, 'error');
            }
        }

        // Auto-load rooms on page load
        window.onload = () => {
            loadRooms();
            
            // Auto-refresh rooms every 30 seconds
            setInterval(loadRooms, 30000);
        };
    </script>
</body>
</html>
