import apiClient from './apiClient';

// Service xử lý các API liên quan đến tiện nghi
const amenityService = {
  // L<PERSON><PERSON> danh s<PERSON>ch tất cả các tiện nghi
  getAmenities: async () => {
    try {
      const response = await apiClient.get('/amenities');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Lấy thông tin chi tiết của một tiện nghi
  getAmenityById: async (id) => {
    try {
      const response = await apiClient.get(`/amenities/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Tạo tiện nghi mới (chỉ admin)
  createAmenity: async (amenityData) => {
    try {
      const response = await apiClient.post('/amenities', amenityData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Cập nhật thông tin tiện nghi (chỉ admin)
  updateAmenity: async (id, amenityData) => {
    try {
      const response = await apiClient.put(`/amenities/${id}`, amenityData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Xóa tiện nghi (chỉ admin)
  deleteAmenity: async (id) => {
    try {
      const response = await apiClient.delete(`/amenities/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default amenityService;
