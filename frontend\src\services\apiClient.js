import axios from 'axios';
import { toast } from 'react-toastify';
// Lưu ý: Không import authService trực tiếp để tránh circular dependency

// Simple flag để tránh toast errors trong lúc app khởi động
let isAppStarting = true;

console.log('🚀 App đang khởi động - Toast errors tạm thời bị tắt trong 12 giây...');

// Tắt flag sau 12 giây - đủ thời gian cho app load xong
setTimeout(() => {
  isAppStarting = false;
  localStorage.removeItem('suppressToastErrors');
  console.log('✅ App đã khởi động xong - Toast errors được bật');
}, 12000);

// Export functions để control toast errors
export const suppressToastErrors = () => {
  localStorage.setItem('suppressToastErrors', 'true');
  console.log('🔇 Toast errors đã bị tắt');
};

export const enableToastErrors = () => {
  localStorage.removeItem('suppressToastErrors');
  isAppStarting = false;
  console.log('🔊 Toast errors đã được bật');
};

// Backup solution - override toast.error temporarily
const originalToastError = toast.error;
toast.error = (...args) => {
  if (isAppStarting || localStorage.getItem('suppressToastErrors') === 'true') {
    console.log('🔇 Toast.error bị override - không hiển thị:', args[0]);
    return;
  }
  return originalToastError(...args);
};

// Restore original toast.error after startup
setTimeout(() => {
  toast.error = originalToastError;
  console.log('🔄 Toast.error đã được restore');
}, 12000);

// Kiểm tra môi trường và sử dụng URL phù hợp
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Tạo instance Axios với cấu hình mặc định
const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 15000, // Timeout sau 15 giây
  withCredentials: true, // Gửi cookies với request
});

// Thêm interceptor để xử lý token xác thực
apiClient.interceptors.request.use(
  (config) => {
    // Không ghi đè Content-Type nếu đã được thiết lập (cho FormData)
    if (config.headers['Content-Type'] === 'multipart/form-data') {
      delete config.headers['Content-Type'];
    }

    // Lấy token từ localStorage
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;

      // Kiểm tra xem token có hợp lệ không (ít nhất phải có định dạng đúng)
      try {
        // Token JWT thường có 3 phần được phân tách bởi dấu chấm
        const parts = token.split('.');
        if (parts.length !== 3) {
          console.warn('Token không hợp lệ, đang xóa...');
          localStorage.removeItem('token');
          localStorage.removeItem('user');
        }
      } catch (err) {
        console.error('Lỗi khi kiểm tra token:', err);
      }
    }

    // Log request để debug
    console.log(`[API Request] ${config.method.toUpperCase()} ${config.url}`, config);

    return config;
  },
  (error) => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// Thêm interceptor để xử lý lỗi từ response
apiClient.interceptors.response.use(
  (response) => {
    // Log response để debug
    console.log(`[API Response] ${response.config.method.toUpperCase()} ${response.config.url}`, response.data);
    return response;
  },
  async (error) => {
    console.error('[API Response Error]', error);

    // Danh sách URLs không hiển thị toast error
    const suppressedUrls = ['/auth/me', '/rooms', '/categories', '/amenities'];
    const isUrlSuppressed = suppressedUrls.some(url => error.config?.url?.includes(url));

    // Không hiển thị toast error trong các trường hợp sau:
    // 1. App đang khởi động
    // 2. Toast bị suppress thủ công
    // 3. URL trong danh sách suppress
    if (isAppStarting || localStorage.getItem('suppressToastErrors') === 'true' || isUrlSuppressed) {
      console.log('🔇 Toast error bị suppress:', {
        url: error.config?.url,
        reason: isAppStarting ? 'App starting' : isUrlSuppressed ? 'URL suppressed' : 'Manual suppress'
      });
      return Promise.reject(error);
    }

    // Xử lý các trường hợp lỗi khác nhau
    if (error.code === 'ECONNABORTED') {
      console.warn('⏰ Request timeout:', error.config?.url);
      toast.error('Kết nối đến máy chủ quá thời gian. Vui lòng thử lại sau.');
    }
    // Xử lý lỗi CORS hoặc Network Error
    else if (error.message && error.message.includes('Network Error')) {
      console.error('🌐 Network Error:', error.config?.url);
      toast.error('Mất kết nối với server. Vui lòng refresh trang.');
    }
    else if (!error.response) {
      console.warn('📡 No response received:', error.config?.url);
      toast.error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.');
    }
    else {
      const originalRequest = error.config;

      // Xử lý lỗi 401 (Unauthorized) - Token hết hạn hoặc không hợp lệ
      if (error.response.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        // Kiểm tra xem có phải là request đến API admin không
        const isAdminRequest = originalRequest.url.includes('/admin/');

        // Kiểm tra xem có phải là request đăng nhập không
        const isLoginRequest = originalRequest.url.includes('/auth/login');

        // Nếu là request đến API admin, không tự động đăng xuất
        if (isAdminRequest) {
          console.error('Lỗi xác thực khi gọi API admin:', error.response.data);
          toast.error('Không có quyền truy cập API admin. Vui lòng kiểm tra quyền của bạn.');
          return Promise.reject(error);
        }

        // Nếu là request đăng nhập, không tự động đăng xuất và chuyển hướng
        // Để LoginPage tự xử lý error message
        if (isLoginRequest) {
          console.log('Lỗi đăng nhập:', error.response.data);
          return Promise.reject(error);
        }

        // Xóa token và chuyển hướng đến trang đăng nhập cho các request khác
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // Nếu đang ở môi trường trình duyệt, chuyển hướng đến trang đăng nhập
        if (typeof window !== 'undefined') {
          toast.error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
          window.location.href = '/login';
        }
      }
      // Xử lý lỗi 500 (Internal Server Error)
      else if (error.response.status === 500) {
        console.error('Lỗi 500 từ server:', error.response.data);

        // Kiểm tra xem có thông báo lỗi cụ thể từ server không
        let errorMessage = 'Đã xảy ra lỗi từ máy chủ. Vui lòng thử lại sau hoặc liên hệ quản trị viên.';

        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data && error.response.data.error) {
          errorMessage = error.response.data.error;
        }

        toast.error(errorMessage);
      }
      // Xử lý lỗi CORS từ response
      else if (error.response.status === 0) {
        console.error('Lỗi CORS từ response:', error);
        toast.error('Lỗi CORS: Server không cho phép truy cập từ origin này.');
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
