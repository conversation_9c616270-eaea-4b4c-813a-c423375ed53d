const express = require('express');
const router = express.Router();
const Room = require('../models/Room');

// Test route để tạo tin nổi bật với thời gian ngắn (để test)
router.post('/create-test-highlight', async (req, res) => {
  try {
    const { roomId, minutes = 1 } = req.body;

    if (!roomId) {
      return res.status(400).json({
        success: false,
        message: 'roomId là bắt buộc'
      });
    }

    // Tìm phòng
    const room = await Room.findById(roomId);
    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy phòng trọ'
      });
    }

    // Cập nhật thành tin nổi bật với thời gian ngắn
    const highlightExpiry = new Date(Date.now() + minutes * 60 * 1000);
    
    await Room.findByIdAndUpdate(roomId, {
      isHighlighted: true,
      highlightExpiry: highlightExpiry
    });

    console.log(`[TestRoute] Đã tạo tin nổi bật test cho phòng ${roomId}, hết hạn sau ${minutes} phút`);

    // Emit socket event để frontend cập nhật ngay lập tức
    const socketService = require('../services/socketService');
    socketService.emitHighlightActivated({
      roomId: roomId,
      title: room.title,
      userId: room.user.toString(),
      highlightExpiry: highlightExpiry
    });

    res.json({
      success: true,
      message: `Đã tạo tin nổi bật test, hết hạn sau ${minutes} phút`,
      data: {
        roomId,
        highlightExpiry,
        minutesLeft: minutes
      }
    });

  } catch (error) {
    console.error('[TestRoute] Lỗi khi tạo tin nổi bật test:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi tạo tin nổi bật test',
      error: error.message
    });
  }
});

// Test route để chạy thủ công job hạ cấp tin nổi bật
router.post('/run-highlight-expiry-job', async (req, res) => {
  try {
    const scheduledService = require('../services/scheduledService');
    const result = await scheduledService.runHighlightExpiryJobManually();
    
    res.json({
      success: true,
      message: 'Đã chạy thủ công job hạ cấp tin nổi bật',
      data: result
    });
  } catch (error) {
    console.error('[TestRoute] Lỗi khi chạy job hạ cấp tin nổi bật:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi chạy job hạ cấp tin nổi bật',
      error: error.message
    });
  }
});

// Test route để lấy danh sách tin nổi bật sắp hết hạn
router.get('/expiring-highlights', async (req, res) => {
  try {
    const now = new Date();
    const in5Minutes = new Date(now.getTime() + 5 * 60 * 1000);
    
    const expiringRooms = await Room.find({
      isHighlighted: true,
      highlightExpiry: { 
        $gte: now,
        $lte: in5Minutes 
      }
    }).select('_id title highlightExpiry user').populate('user', 'fullName email');
    
    res.json({
      success: true,
      message: `Tìm thấy ${expiringRooms.length} tin nổi bật sắp hết hạn trong 5 phút`,
      data: expiringRooms.map(room => ({
        roomId: room._id,
        title: room.title,
        highlightExpiry: room.highlightExpiry,
        minutesLeft: Math.ceil((new Date(room.highlightExpiry) - now) / (1000 * 60)),
        owner: room.user?.fullName || 'Unknown'
      }))
    });
  } catch (error) {
    console.error('[TestRoute] Lỗi khi lấy danh sách tin sắp hết hạn:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy danh sách tin sắp hết hạn',
      error: error.message
    });
  }
});

module.exports = router;
