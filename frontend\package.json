{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.7", "framer-motion": "^12.12.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-icons": "^5.0.1", "react-router-dom": "^6.22.1", "react-toastify": "^10.0.4", "socket.io-client": "^4.8.1", "yup": "^1.3.3"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^5.1.0"}}