// Middleware xử lý lỗi 404 (Not Found)
const notFound = (req, res, next) => {
  const error = new Error(`Không tìm thấy - ${req.originalUrl}`);
  res.status(404);
  next(error);
};

// Middleware xử lý lỗi chung
const errorHandler = (err, req, res, next) => {
  const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
  
  // Xử lý lỗi MongoDB
  let message = err.message;
  let errors = {};
  
  if (err.name === 'CastError' && err.kind === 'ObjectId') {
    message = 'ID không hợp lệ';
    statusCode = 400;
  }
  
  // Xử lý lỗi validation của <PERSON>ose
  if (err.name === 'ValidationError') {
    message = 'Lỗi xác thực dữ liệu';
    statusCode = 400;
    
    Object.keys(err.errors).forEach(key => {
      errors[key] = err.errors[key].message;
    });
  }
  
  // X<PERSON> lý lỗi trùng lặp (duplicate key)
  if (err.code === 11000) {
    message = 'Dữ liệu đã tồn tại';
    statusCode = 400;
    
    const field = Object.keys(err.keyValue)[0];
    errors[field] = `${field} đã tồn tại`;
  }
  
  res.status(statusCode).json({
    success: false,
    message,
    errors: Object.keys(errors).length > 0 ? errors : undefined,
    stack: process.env.NODE_ENV === 'production' ? null : err.stack
  });
};

module.exports = { notFound, errorHandler };
