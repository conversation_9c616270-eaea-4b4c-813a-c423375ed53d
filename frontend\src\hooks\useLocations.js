import { useState, useCallback } from 'react';
import { locationService } from '../services';

// Hook quản lý các thao tác với địa chỉ hành chính
const useLocations = () => {
  const [provinces, setProvinces] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [wards, setWards] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Lấy danh sách tỉnh/thành phố
  const fetchProvinces = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await locationService.getProvinces();
      setProvinces(data);
      return data;
    } catch (err) {
      setError(err.message || 'Không thể lấy danh sách tỉnh/thành phố');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // L<PERSON>y danh sách quận/huyện theo tỉnh/thành phố
  const fetchDistrictsByProvince = useCallback(async (provinceCode) => {
    if (!provinceCode) {
      setDistricts([]);
      return [];
    }

    setIsLoading(true);
    setError(null);

    try {
      const data = await locationService.getDistrictsByProvince(provinceCode);
      setDistricts(data);
      return data;
    } catch (err) {
      setError(err.message || 'Không thể lấy danh sách quận/huyện');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Lấy danh sách phường/xã theo quận/huyện
  const fetchWardsByDistrict = useCallback(async (provinceCode, districtCode) => {
    if (!provinceCode || !districtCode) {
      setWards([]);
      return [];
    }

    setIsLoading(true);
    setError(null);

    try {
      const data = await locationService.getWardsByDistrict(provinceCode, districtCode);
      setWards(data);
      return data;
    } catch (err) {
      setError(err.message || 'Không thể lấy danh sách phường/xã');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    provinces,
    districts,
    wards,
    isLoading,
    error,
    fetchProvinces,
    fetchDistrictsByProvince,
    fetchWardsByDistrict,
  };
};

export default useLocations;
