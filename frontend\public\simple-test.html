<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 15px 30px; margin: 10px; font-size: 16px; cursor: pointer; }
        .success { background: #28a745; color: white; border: none; border-radius: 4px; }
        .primary { background: #007bff; color: white; border: none; border-radius: 4px; }
        .result { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🧪 Simple Real-time Test</h1>
    
    <div>
        <button class="success" onclick="createTestHighlight()">Tạo Test Highlight (1 phút)</button>
        <button class="primary" onclick="runExpiryJob()">Chạy Expiry Job <PERSON></button>
    </div>

    <div id="result" class="result">
        <strong>Kết quả:</strong><br>
        <span id="status">Chưa test...</span>
    </div>

    <div>
        <strong>Hướng dẫn:</strong><br>
        1. Nhấn "Tạo Test Highlight" để tạo tin nổi bật hết hạn sau 1 phút<br>
        2. Mở trang chủ trong tab khác<br>
        3. Chờ 1 phút hoặc nhấn "Chạy Expiry Job Ngay"<br>
        4. Quan sát tin tự động chuyển từ nổi bật xuống thường
    </div>

    <script>
        function updateStatus(message, color = 'black') {
            document.getElementById('status').innerHTML = message;
            document.getElementById('status').style.color = color;
            console.log(message);
        }

        async function createTestHighlight() {
            try {
                updateStatus('🔄 Đang tạo test highlight...', 'blue');
                
                // Get first room
                const roomsResponse = await fetch('http://localhost:5000/api/rooms?limit=1');
                const roomsData = await roomsResponse.json();
                
                if (!roomsData.success || roomsData.data.rooms.length === 0) {
                    throw new Error('Không tìm thấy phòng nào');
                }
                
                const roomId = roomsData.data.rooms[0]._id;
                const roomTitle = roomsData.data.rooms[0].title;
                
                // Create test highlight
                const response = await fetch('http://localhost:5000/api/test/create-test-highlight', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ roomId: roomId, minutes: 1 })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus(`✅ Đã tạo test highlight cho "${roomTitle}"<br>⏰ Sẽ hết hạn sau 1 phút<br>🔗 Mở trang chủ để xem real-time update`, 'green');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                updateStatus(`❌ Lỗi: ${error.message}`, 'red');
            }
        }

        async function runExpiryJob() {
            try {
                updateStatus('🔄 Đang chạy expiry job...', 'blue');
                
                const response = await fetch('http://localhost:5000/api/test/run-highlight-expiry-job', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus(`✅ Job hoàn thành: ${data.data.processed} phòng được xử lý<br>⏱️ Thời gian: ${data.data.duration}ms<br>🔗 Kiểm tra trang chủ để xem kết quả`, 'green');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                updateStatus(`❌ Lỗi: ${error.message}`, 'red');
            }
        }
    </script>
</body>
</html>
