import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { toast } from 'react-toastify'
import Card from '../components/Card'
import Button from '../components/Button'
import useRooms from '../hooks/useRooms'
import useSocket from '../hooks/useSocket'

const HomePageRealTime = () => {
  const { rooms, isLoading, error, fetchRooms } = useRooms();
  const socketHook = useSocket();
  const { addEventListener } = socketHook || {};
  const [allRooms, setAllRooms] = useState([]); // Unified list for sorting
  const [displayRooms, setDisplayRooms] = useState([]); // For display (limited)
  const [currentTime, setCurrentTime] = useState(new Date()); // For countdown

  // State cho image slider
  const [currentSlide, setCurrentSlide] = useState(0);

  // Slider images
  const sliderImages = [
    'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2080&q=80',
    'https://images.unsplash.com/photo-1484154218962-a197022b5858?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80'
  ];

  // Hàm sắp xếp rooms theo priority
  const sortRooms = (rooms) => {
    const rank = { 
      special: 3, 
      highlight: 2, 
      normal: 1 
    };

    return rooms.sort((a, b) => {
      // Determine package type based on isHighlighted
      const aPackage = a.isHighlighted ? 'highlight' : 'normal';
      const bPackage = b.isHighlighted ? 'highlight' : 'normal';
      
      const diff = rank[bPackage] - rank[aPackage];
      if (diff !== 0) return diff;
      
      // If same package type, sort by creation date (newest first)
      return new Date(b.createdAt) - new Date(a.createdAt);
    });
  };

  // Debug socket connection status
  useEffect(() => {
    const checkSocketStatus = () => {
      console.log('[HomePageRealTime] 🔍 Socket Status Check:', {
        socketService: !!window.socketService,
        isConnected: window.socketService?.isConnected(),
        socketId: window.socketService?.socket?.id,
        addEventListener: !!addEventListener,
        socketHook: !!socketHook
      });
    };

    checkSocketStatus();
    
    // Check again after 2 seconds
    const timer = setTimeout(checkSocketStatus, 2000);
    return () => clearTimeout(timer);
  }, [addEventListener, socketHook]);

  // Load rooms khi component mount
  useEffect(() => {
    const loadRooms = async () => {
      try {
        console.log('[HomePageRealTime] Loading all rooms...');
        
        // Load all rooms (both highlighted and regular)
        const allRoomsResponse = await fetchRooms({
          status: 'available',
          limit: 50, // Load more rooms for sorting
          sort: '-createdAt'
        });
        
        if (allRoomsResponse.success) {
          const rooms = allRoomsResponse.data.rooms;
          console.log('[HomePageRealTime] Loaded all rooms:', rooms.length);
          
          // Sort rooms by priority
          const sortedRooms = sortRooms([...rooms]);
          setAllRooms(sortedRooms);
          
          // Set display rooms (limit to 12 for homepage)
          setDisplayRooms(sortedRooms.slice(0, 12));
          
          console.log('[HomePageRealTime] Sorted and set display rooms:', sortedRooms.slice(0, 12).length);
        }
      } catch (error) {
        console.error('[HomePageRealTime] Error loading rooms:', error);
      }
    };

    loadRooms();
  }, [fetchRooms]);

  // Update current time for countdown display
  useEffect(() => {
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timeInterval);
  }, []);

  // Auto-check for expired highlights every second
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      
      setAllRooms(prevRooms => {
        let updated = false;
        
        const newRooms = prevRooms.map(room => {
          // Check if highlighted room has expired
          if (
            room.isHighlighted &&
            room.highlightExpiry &&
            new Date(room.highlightExpiry) < now
          ) {
            updated = true;
            console.log('[HomePageRealTime] ⏰ Room expired locally:', room.title);
            
            // Show toast notification
            toast.info(`Tin "${room.title}" đã hết hạn gói nổi bật và được sắp xếp lại`, {
              position: 'top-right',
              autoClose: 5000
            });
            
            return {
              ...room,
              isHighlighted: false,
              highlightExpiry: null
            };
          }
          return room;
        });
        
        // If there were updates, sort the list again
        if (updated) {
          console.log('[HomePageRealTime] 🔄 Sorting rooms after expiry...');
          const sortedRooms = sortRooms(newRooms);
          
          // Update display rooms
          setDisplayRooms(sortedRooms.slice(0, 12));
          
          return sortedRooms;
        }
        
        return prevRooms;
      });
    }, 1000); // Check every second
    
    return () => clearInterval(interval);
  }, []);

  // Logic xử lý khi tin nổi bật hết hạn từ socket
  const handleRoomHighlightUpdatedLogic = (data) => {
    console.log('[HomePageRealTime] Processing room highlight update from socket:', data);
    
    setAllRooms(prevRooms => {
      const updatedRooms = prevRooms.map(room => {
        if (room._id === data.roomId) {
          console.log('[HomePageRealTime] ✅ Updating room from socket:', room.title);
          return {
            ...room,
            isHighlighted: data.isHighlighted,
            highlightExpiry: data.highlightExpiry
          };
        }
        return room;
      });
      
      // Sort rooms after update
      const sortedRooms = sortRooms(updatedRooms);
      
      // Update display rooms
      setDisplayRooms(sortedRooms.slice(0, 12));
      
      // Show toast notification
      const updatedRoom = sortedRooms.find(room => room._id === data.roomId);
      if (updatedRoom && !data.isHighlighted) {
        toast.info(`Tin "${updatedRoom.title}" đã hết hạn gói nổi bật và được sắp xếp lại`, {
          position: 'top-right',
          autoClose: 5000
        });
      }
      
      console.log('[HomePageRealTime] 🎉 Room list sorted and updated!');
      return sortedRooms;
    });
  };

  const handleHighlightExpiredLogic = (data) => {
    console.log('[HomePageRealTime] Processing highlight expired from socket:', data);
    // Trigger room highlight updated với isHighlighted: false
    handleRoomHighlightUpdatedLogic({
      roomId: data.roomId,
      isHighlighted: false,
      highlightExpiry: null,
      timestamp: data.timestamp
    });
  };

  // Lắng nghe WebSocket events cho real-time updates
  useEffect(() => {
    console.log('[HomePageRealTime] Setting up socket listeners...', { 
      addEventListener: !!addEventListener,
      socketHook: !!socketHook,
      socketService: !!window.socketService,
      isConnected: window.socketService?.isConnected()
    });
    
    // Sử dụng addEventListener nếu có, nếu không thì fallback to direct socketService
    const setupListeners = () => {
      const handleRoomHighlightUpdated = (data) => {
        console.log('[HomePageRealTime] 🔄 Room highlight updated event received:', data);
        handleRoomHighlightUpdatedLogic(data);
      };

      const handleHighlightExpired = (data) => {
        console.log('[HomePageRealTime] 💥 Highlight expired event received:', data);
        handleHighlightExpiredLogic(data);
      };

      if (addEventListener) {
        console.log('[HomePageRealTime] Using addEventListener from useSocket hook');
        const unsubscribeUpdated = addEventListener('room_highlight_updated', handleRoomHighlightUpdated);
        const unsubscribeExpired = addEventListener('highlight_expired', handleHighlightExpired);
        
        return () => {
          if (unsubscribeUpdated && typeof unsubscribeUpdated === 'function') {
            unsubscribeUpdated();
          }
          if (unsubscribeExpired && typeof unsubscribeExpired === 'function') {
            unsubscribeExpired();
          }
        };
      } else if (window.socketService && window.socketService.isConnected()) {
        console.log('[HomePageRealTime] 🔄 Using direct socketService fallback');
        
        window.socketService.addEventListener('room_highlight_updated', handleRoomHighlightUpdated);
        window.socketService.addEventListener('highlight_expired', handleHighlightExpired);
        
        return () => {
          window.socketService?.removeEventListener('room_highlight_updated', handleRoomHighlightUpdated);
          window.socketService?.removeEventListener('highlight_expired', handleHighlightExpired);
        };
      }
      
      return () => {};
    };

    const cleanup = setupListeners();
    return cleanup;
  }, [addEventListener, socketHook, fetchRooms]);

  // Auto slider
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % sliderImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [sliderImages.length]);

  // Manual test function để trigger events
  const testRealTimeUpdate = () => {
    console.log('[HomePageRealTime] 🧪 Testing real-time update manually...');
    
    const highlightedRoom = displayRooms.find(room => room.isHighlighted);
    if (highlightedRoom) {
      console.log('[HomePageRealTime] Using test room:', highlightedRoom.title);
      
      // Simulate room highlight updated event
      handleRoomHighlightUpdatedLogic({
        roomId: highlightedRoom._id,
        isHighlighted: false,
        highlightExpiry: null,
        timestamp: new Date().toISOString()
      });
    } else {
      console.log('[HomePageRealTime] No highlighted rooms to test with');
      toast.info('Không có tin nổi bật để test. Hãy tạo tin nổi bật trước.', {
        position: 'top-right',
        autoClose: 3000
      });
    }
  };

  // Separate rooms for display
  const highlightedDisplayRooms = displayRooms.filter(room => room.isHighlighted);
  const regularDisplayRooms = displayRooms.filter(room => !room.isHighlighted);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 text-xl">Có lỗi xảy ra: {error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Development Test Button */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ position: 'fixed', top: '10px', right: '10px', zIndex: 9999 }}>
          <button 
            onClick={testRealTimeUpdate}
            style={{
              padding: '8px 16px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            🧪 Test Real-time
          </button>
        </div>
      )}
      
      {/* Hero Slider Section */}
      <section className="relative h-96 md:h-[500px] overflow-hidden">
        {sliderImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={image}
              alt={`Slide ${index + 1}`}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          </div>
        ))}
        
        <div className="absolute inset-0 flex items-center justify-center text-white text-center">
          <div className="max-w-4xl mx-auto px-4">
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              Tìm Phòng Trọ Lý Tưởng
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Hàng ngàn phòng trọ chất lượng, giá cả phải chăng đang chờ bạn
            </p>
            <Link to="/search">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg">
                Bắt Đầu Tìm Kiếm
              </Button>
            </Link>
          </div>
        </div>

        {/* Slider indicators */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {sliderImages.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentSlide ? 'bg-white' : 'bg-white bg-opacity-50'
              }`}
            />
          ))}
        </div>
      </section>

      {/* Unified Rooms Section with Auto-Sorting */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Phòng Trọ Nổi Bật & Mới Nhất
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Danh sách được sắp xếp tự động: Tin nổi bật trước, tin thường sau
            </p>
            <div className="mt-4 text-sm text-gray-500">
              Hiển thị {displayRooms.length} phòng • 
              {highlightedDisplayRooms.length} tin nổi bật • 
              {regularDisplayRooms.length} tin thường
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {displayRooms.map((room, index) => (
              <div
                key={room._id}
                className={`
                  transition-all duration-700 ease-in-out transform
                  ${room.isHighlighted ? 'scale-105 shadow-lg' : 'scale-100 shadow-md'}
                  hover:scale-105 hover:shadow-xl
                `}
                style={{
                  animationDelay: `${index * 50}ms`,
                  transform: `translateY(${room.isHighlighted ? '-5px' : '0px'})`,
                  transition: 'all 0.7s cubic-bezier(0.4, 0, 0.2, 1)'
                }}
              >
                <div className={`
                  relative overflow-hidden rounded-lg
                  ${room.isHighlighted ? 'ring-2 ring-yellow-400 ring-opacity-50' : ''}
                `}>
                  {room.isHighlighted && (
                    <div className="absolute top-2 right-2 z-10">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 animate-pulse">
                        ⭐ Nổi bật
                      </span>
                    </div>
                  )}
                  <Card room={room} />
                </div>

                {/* Debug info with countdown */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="text-xs text-gray-400 mt-1 text-center">
                    #{index + 1} • {room.isHighlighted ? '⭐ Nổi bật' : '📝 Thường'}
                    {room.isHighlighted && room.highlightExpiry && (() => {
                      const expiry = new Date(room.highlightExpiry);
                      const timeLeft = expiry - currentTime;

                      if (timeLeft > 0) {
                        const minutes = Math.floor(timeLeft / (1000 * 60));
                        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                        return (
                          <span className="text-red-500 font-semibold">
                            • ⏰ {minutes}:{seconds.toString().padStart(2, '0')}
                          </span>
                        );
                      } else {
                        return <span className="text-red-600 font-bold">• ⏰ EXPIRED</span>;
                      }
                    })()}
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <div className="text-center mt-8">
            <Link to="/search">
              <Button variant="outline" size="lg">
                Xem Tất Cả Phòng Trọ
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Bạn có phòng trọ cần cho thuê?</h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Đăng tin ngay hôm nay để tiếp cận hàng ngàn người đang tìm kiếm phòng trọ mỗi ngày.
          </p>
          <Link to="/post-room">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 text-lg">
              Đăng Tin Miễn Phí
            </Button>
          </Link>
        </div>
      </section>
    </div>
  )
}

export default HomePageRealTime
