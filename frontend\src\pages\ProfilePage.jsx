import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaUser, FaKey, FaHistory, FaSignOutAlt } from 'react-icons/fa';
import { useAuthContext } from '../contexts';
import ProfileInfo from '../components/profile/ProfileInfo';
import PasswordChange from '../components/profile/PasswordChange';
import TransactionHistory from '../components/profile/TransactionHistory';

const ProfilePage = () => {
  const { user, isAuthenticated, isLoading, logout } = useAuthContext();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('profile');

  // Chuyển hướng nếu chưa đăng nhập
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast.error('Vui lòng đăng nhập để xem trang này');
      navigate('/login');
    }
  }, [isLoading, isAuthenticated, navigate]);

  // Xử lý đăng xuất
  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Đăng xuất thành công');
      navigate('/');
    } catch (error) {
      toast.error('Có lỗi xảy ra khi đăng xuất');
    }
  };

  // Hiển thị loading khi đang kiểm tra trạng thái đăng nhập
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  // Hiển thị nội dung trang khi đã đăng nhập
  return (
    <div className="bg-gray-50 py-8">
      <div className="container-custom">
        <h1 className="text-3xl font-bold mb-8 text-center">Quản lý tài khoản</h1>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="md:col-span-1">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              {/* Thông tin người dùng */}
              <div className="p-6 bg-primary text-white">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center">
                    <FaUser className="text-primary text-2xl" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold">{user?.fullName}</h2>
                    <p className="text-sm opacity-80">{user?.email}</p>
                  </div>
                </div>
              </div>

              {/* Menu */}
              <nav className="p-4">
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => setActiveTab('profile')}
                      className={`w-full text-left px-4 py-3 rounded-lg flex items-center space-x-3 ${
                        activeTab === 'profile'
                          ? 'bg-primary text-white'
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      <FaUser />
                      <span>Thông tin cá nhân</span>
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => setActiveTab('password')}
                      className={`w-full text-left px-4 py-3 rounded-lg flex items-center space-x-3 ${
                        activeTab === 'password'
                          ? 'bg-primary text-white'
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      <FaKey />
                      <span>Đổi mật khẩu</span>
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => setActiveTab('transactions')}
                      className={`w-full text-left px-4 py-3 rounded-lg flex items-center space-x-3 ${
                        activeTab === 'transactions'
                          ? 'bg-primary text-white'
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      <FaHistory />
                      <span>Lịch sử giao dịch</span>
                    </button>
                  </li>
                  <li className="border-t pt-2 mt-4">
                    <button
                      onClick={handleLogout}
                      className="w-full text-left px-4 py-3 rounded-lg flex items-center space-x-3 text-red-500 hover:bg-red-50"
                    >
                      <FaSignOutAlt />
                      <span>Đăng xuất</span>
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>

          {/* Nội dung chính */}
          <div className="md:col-span-3">
            <div className="bg-white rounded-lg shadow-md p-6">
              {activeTab === 'profile' && <ProfileInfo user={user} />}
              {activeTab === 'password' && <PasswordChange />}
              {activeTab === 'transactions' && <TransactionHistory />}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
