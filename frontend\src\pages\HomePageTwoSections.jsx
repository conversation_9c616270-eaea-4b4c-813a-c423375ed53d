import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { toast } from 'react-toastify'
import Card from '../components/Card'
import Button from '../components/Button'
import useRooms from '../hooks/useRooms'
import useSocket from '../hooks/useSocket'

const HomePageTwoSections = () => {
  const { rooms, isLoading, error, fetchRooms } = useRooms();
  const socketHook = useSocket();
  const { addEventListener } = socketHook || {};
  
  // Separate states for two sections
  const [highlightedRooms, setHighlightedRooms] = useState([]); // Max 3 items
  const [regularRooms, setRegularRooms] = useState([]); // Max 6 items
  const [currentTime, setCurrentTime] = useState(new Date()); // For countdown

  // State cho image slider
  const [currentSlide, setCurrentSlide] = useState(0);

  // Slider images
  const sliderImages = [
    'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2080&q=80',
    'https://images.unsplash.com/photo-1484154218962-a197022b5858?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80'
  ];

  // Debug socket connection status
  useEffect(() => {
    const checkSocketStatus = () => {
      console.log('[HomePageTwoSections] 🔍 Socket Status Check:', {
        socketService: !!window.socketService,
        isConnected: window.socketService?.isConnected(),
        socketId: window.socketService?.socket?.id,
        addEventListener: !!addEventListener,
        socketHook: !!socketHook
      });
    };

    checkSocketStatus();
    
    // Check again after 2 seconds
    const timer = setTimeout(checkSocketStatus, 2000);
    return () => clearTimeout(timer);
  }, [addEventListener, socketHook]);

  // Update current time for countdown display
  useEffect(() => {
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    
    return () => clearInterval(timeInterval);
  }, []);

  // Load rooms khi component mount
  useEffect(() => {
    const loadRooms = async () => {
      try {
        console.log('[HomePageTwoSections] Loading all rooms for two sections...');

        // Load all rooms in one API call
        const allRoomsResponse = await fetchRooms({
          status: 'available',
          limit: 50, // Load enough rooms for both sections
          sort: '-createdAt'
        });

        if (allRoomsResponse.success) {
          const allRooms = allRoomsResponse.data.rooms;
          console.log('[HomePageTwoSections] Loaded all rooms:', allRooms.length);

          // Separate highlighted and regular rooms
          const highlighted = allRooms.filter(room => room.isHighlighted).slice(0, 3); // Max 3
          const regular = allRooms.filter(room => !room.isHighlighted).slice(0, 6); // Max 6

          setHighlightedRooms(highlighted);
          setRegularRooms(regular);

          console.log('[HomePageTwoSections] Separated rooms - Highlighted:', highlighted.length, 'Regular:', regular.length);
        }
      } catch (error) {
        console.error('[HomePageTwoSections] Error loading rooms:', error);
      }
    };

    loadRooms();
  }, [fetchRooms]);

  // Auto-check for expired highlights every second (local timer)
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      
      setHighlightedRooms(prevHighlighted => {
        let hasExpired = false;
        const expiredRooms = [];
        
        const stillHighlighted = prevHighlighted.filter(room => {
          if (room.highlightExpiry && new Date(room.highlightExpiry) < now) {
            hasExpired = true;
            expiredRooms.push({
              ...room,
              isHighlighted: false,
              highlightExpiry: null
            });
            return false; // Remove from highlighted
          }
          return true; // Keep in highlighted
        });
        
        // If rooms expired, move them to regular section
        if (hasExpired) {
          console.log('[HomePageTwoSections] ⏰ Local timer detected expired rooms:', expiredRooms.length);
          
          expiredRooms.forEach(expiredRoom => {
            // Move to regular section at position 0
            setRegularRooms(prevRegular => {
              const newRegular = [expiredRoom, ...prevRegular].slice(0, 6); // Keep max 6
              console.log('[HomePageTwoSections] ✅ Moved expired room to regular at position 0:', expiredRoom.title);
              return newRegular;
            });
            
            // Show toast notification
            toast.info(`Tin "${expiredRoom.title}" đã hết hạn gói nổi bật và được chuyển xuống danh sách tin thường`, {
              position: 'top-right',
              autoClose: 5000
            });
          });
          
          // Try to refill highlighted section
          refillHighlightedSection();
        }
        
        return stillHighlighted;
      });
    }, 1000); // Check every second
    
    return () => clearInterval(interval);
  }, [fetchRooms]);

  // Function to refill highlighted section when rooms expire
  const refillHighlightedSection = async () => {
    try {
      console.log('[HomePageTwoSections] 🔄 Refilling highlighted section...');

      // Load all rooms and filter highlighted ones
      const response = await fetchRooms({
        status: 'available',
        limit: 20,
        sort: '-createdAt'
      });

      if (response.success) {
        const allRooms = response.data.rooms;
        const availableHighlighted = allRooms.filter(room => room.isHighlighted).slice(0, 3);
        setHighlightedRooms(availableHighlighted);
        console.log('[HomePageTwoSections] ✅ Refilled highlighted section with', availableHighlighted.length, 'rooms');
      }
    } catch (error) {
      console.error('[HomePageTwoSections] Error refilling highlighted section:', error);
    }
  };

  // Logic xử lý khi nhận socket events từ backend
  const handleRoomHighlightUpdatedLogic = (data) => {
    console.log('[HomePageTwoSections] 🔄 Processing socket event - room highlight update:', data);
    
    if (data.isHighlighted) {
      // Room được nâng cấp lên nổi bật
      console.log('[HomePageTwoSections] Room upgraded to highlighted:', data.roomId);
      
      // Remove from regular section if exists
      setRegularRooms(prev => prev.filter(room => room._id !== data.roomId));
      
      // Refill highlighted section
      refillHighlightedSection();
      
    } else {
      // Room hết hạn nổi bật -> chuyển thành thường
      console.log('[HomePageTwoSections] 💥 Room expired from highlighted:', data.roomId);
      
      // Find expired room in highlighted section
      const expiredRoom = highlightedRooms.find(room => room._id === data.roomId);
      
      if (expiredRoom) {
        console.log('[HomePageTwoSections] ✅ Found expired room in highlighted section:', expiredRoom.title);
        
        // Create updated room object
        const updatedRoom = {
          ...expiredRoom,
          isHighlighted: false,
          highlightExpiry: null
        };
        
        // Remove from highlighted section
        setHighlightedRooms(prev => {
          const newHighlighted = prev.filter(room => room._id !== data.roomId);
          console.log('[HomePageTwoSections] ✅ Removed from highlighted section, remaining:', newHighlighted.length);
          return newHighlighted;
        });
        
        // Add to regular section at position 0
        setRegularRooms(prevRegular => {
          const newRegular = [updatedRoom, ...prevRegular].slice(0, 6); // Keep max 6
          console.log('[HomePageTwoSections] ✅ Added expired room to regular section at position 0');
          return newRegular;
        });
        
        // Show toast notification
        toast.info(`Tin "${expiredRoom.title}" đã hết hạn gói nổi bật và được chuyển xuống danh sách tin thường`, {
          position: 'top-right',
          autoClose: 5000
        });
        
        // Try to refill highlighted section
        refillHighlightedSection();
        
        console.log('[HomePageTwoSections] 🎉 Real-time transition completed successfully!');
      } else {
        console.warn('[HomePageTwoSections] ⚠️ Expired room not found in highlighted section:', data.roomId);
      }
    }
  };

  const handleHighlightExpiredLogic = (data) => {
    console.log('[HomePageTwoSections] Processing highlight expired from socket:', data);
    // Trigger room highlight updated với isHighlighted: false
    handleRoomHighlightUpdatedLogic({
      roomId: data.roomId,
      isHighlighted: false,
      highlightExpiry: null,
      timestamp: data.timestamp
    });
  };

  // Lắng nghe WebSocket events cho real-time updates
  useEffect(() => {
    console.log('[HomePageTwoSections] Setting up socket listeners...', { 
      addEventListener: !!addEventListener,
      socketHook: !!socketHook,
      socketService: !!window.socketService,
      isConnected: window.socketService?.isConnected()
    });
    
    // Setup socket listeners
    const setupListeners = () => {
      const handleRoomHighlightUpdated = (data) => {
        console.log('[HomePageTwoSections] 🔄 Socket event - Room highlight updated:', data);
        handleRoomHighlightUpdatedLogic(data);
      };

      const handleHighlightExpired = (data) => {
        console.log('[HomePageTwoSections] 💥 Socket event - Highlight expired:', data);
        handleHighlightExpiredLogic(data);
      };

      if (addEventListener) {
        console.log('[HomePageTwoSections] Using addEventListener from useSocket hook');
        const unsubscribeUpdated = addEventListener('room_highlight_updated', handleRoomHighlightUpdated);
        const unsubscribeExpired = addEventListener('highlight_expired', handleHighlightExpired);
        
        return () => {
          if (unsubscribeUpdated && typeof unsubscribeUpdated === 'function') {
            unsubscribeUpdated();
          }
          if (unsubscribeExpired && typeof unsubscribeExpired === 'function') {
            unsubscribeExpired();
          }
        };
      } else if (window.socketService && window.socketService.isConnected()) {
        console.log('[HomePageTwoSections] 🔄 Using direct socketService fallback');
        
        window.socketService.addEventListener('room_highlight_updated', handleRoomHighlightUpdated);
        window.socketService.addEventListener('highlight_expired', handleHighlightExpired);
        
        return () => {
          window.socketService?.removeEventListener('room_highlight_updated', handleRoomHighlightUpdated);
          window.socketService?.removeEventListener('highlight_expired', handleHighlightExpired);
        };
      }
      
      return () => {};
    };

    const cleanup = setupListeners();
    return cleanup;
  }, [addEventListener, socketHook, fetchRooms, highlightedRooms]);

  // Auto slider
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % sliderImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [sliderImages.length]);

  // Manual test function để trigger events
  const testRealTimeTransition = () => {
    console.log('[HomePageTwoSections] 🧪 Testing real-time transition manually...');
    
    if (highlightedRooms.length > 0) {
      const testRoom = highlightedRooms[0];
      console.log('[HomePageTwoSections] Using test room:', testRoom.title);
      
      // Simulate room highlight updated event
      handleRoomHighlightUpdatedLogic({
        roomId: testRoom._id,
        isHighlighted: false,
        highlightExpiry: null,
        timestamp: new Date().toISOString()
      });
    } else {
      console.log('[HomePageTwoSections] No highlighted rooms to test with');
      toast.info('Không có tin nổi bật để test. Hãy tạo tin nổi bật trước.', {
        position: 'top-right',
        autoClose: 3000
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 text-xl">Có lỗi xảy ra: {error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Development Test Button */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ position: 'fixed', top: '10px', right: '10px', zIndex: 9999 }}>
          <button 
            onClick={testRealTimeTransition}
            style={{
              padding: '8px 16px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            🧪 Test Transition
          </button>
        </div>
      )}
      
      {/* Hero Slider Section */}
      <section className="relative h-96 md:h-[500px] overflow-hidden">
        {sliderImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={image}
              alt={`Slide ${index + 1}`}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          </div>
        ))}
        
        <div className="absolute inset-0 flex items-center justify-center text-white text-center">
          <div className="max-w-4xl mx-auto px-4">
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              Tìm Phòng Trọ Lý Tưởng
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Hàng ngàn phòng trọ chất lượng, giá cả phải chăng đang chờ bạn
            </p>
            <Link to="/search">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg">
                Bắt Đầu Tìm Kiếm
              </Button>
            </Link>
          </div>
        </div>

        {/* Slider indicators */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {sliderImages.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentSlide ? 'bg-white' : 'bg-white bg-opacity-50'
              }`}
            />
          ))}
        </div>
      </section>

      {/* Section 1: Highlighted Rooms (Max 3) */}
      {highlightedRooms.length > 0 && (
        <section className="py-16 bg-gradient-to-r from-yellow-50 to-orange-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                🌟 Phòng Trọ Nổi Bật
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Những phòng trọ được đánh giá cao và có nhiều tiện ích
              </p>
              <div className="mt-4 text-sm text-gray-500">
                Hiển thị {highlightedRooms.length}/3 tin nổi bật
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
              {highlightedRooms.map((room, index) => (
                <div 
                  key={room._id} 
                  className="transition-all duration-700 ease-in-out transform hover:scale-105"
                  style={{
                    animationDelay: `${index * 100}ms`,
                    transform: 'translateY(-5px)',
                    transition: 'all 0.7s cubic-bezier(0.4, 0, 0.2, 1)'
                  }}
                >
                  <div className="relative overflow-hidden rounded-lg ring-2 ring-yellow-400 ring-opacity-50 shadow-lg">
                    <div className="absolute top-2 right-2 z-10">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 animate-pulse">
                        ⭐ Nổi bật
                      </span>
                    </div>
                    <Card room={room} />
                  </div>
                  
                  {/* Debug info with countdown */}
                  {process.env.NODE_ENV === 'development' && (
                    <div className="text-xs text-gray-400 mt-1 text-center">
                      #{index + 1} • ⭐ Nổi bật
                      {room.highlightExpiry && (() => {
                        const expiry = new Date(room.highlightExpiry);
                        const timeLeft = expiry - currentTime;
                        
                        if (timeLeft > 0) {
                          const minutes = Math.floor(timeLeft / (1000 * 60));
                          const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                          return (
                            <span className="text-red-500 font-semibold">
                              • ⏰ {minutes}:{seconds.toString().padStart(2, '0')}
                            </span>
                          );
                        } else {
                          return <span className="text-red-600 font-bold">• ⏰ EXPIRED</span>;
                        }
                      })()}
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            <div className="text-center mt-8">
              <Link to="/search?highlighted=true">
                <Button variant="outline" size="lg">
                  Xem Tất Cả Phòng Nổi Bật
                </Button>
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* Section 2: Regular Rooms (Max 6) */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Phòng Trọ Mới Nhất
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Cập nhật liên tục những phòng trọ mới nhất
            </p>
            <div className="mt-4 text-sm text-gray-500">
              Hiển thị {regularRooms.length}/6 tin thường
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {regularRooms.map((room, index) => (
              <div 
                key={room._id} 
                className="transition-all duration-500 ease-in-out transform hover:scale-105"
                style={{
                  animationDelay: `${index * 50}ms`,
                  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                }}
              >
                <div className="relative overflow-hidden rounded-lg shadow-md">
                  <Card room={room} />
                </div>
                
                {/* Debug info */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="text-xs text-gray-400 mt-1 text-center">
                    #{index + 1} • 📝 Thường
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <div className="text-center mt-8">
            <Link to="/search">
              <Button variant="outline" size="lg">
                Xem Tất Cả Phòng Trọ
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Bạn có phòng trọ cần cho thuê?</h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Đăng tin ngay hôm nay để tiếp cận hàng ngàn người đang tìm kiếm phòng trọ mỗi ngày.
          </p>
          <Link to="/post-room">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 text-lg">
              Đăng Tin Miễn Phí
            </Button>
          </Link>
        </div>
      </section>
    </div>
  )
}

export default HomePageTwoSections
