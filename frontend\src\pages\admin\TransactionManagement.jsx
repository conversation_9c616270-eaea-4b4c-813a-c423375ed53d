import { useState, useEffect } from 'react';
import { Fa<PERSON><PERSON>ch, <PERSON>a<PERSON>ye, FaExclamationTriangle } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useAuthContext } from '../../contexts';
import { formatCurrency, formatDate } from '../../utils/format';
import Pagination from '../../components/Pagination';
import Modal from '../../components/Modal';

const TransactionManagement = () => {
  const { token } = useAuthContext();
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Modals
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState(null);

  // Fetch transactions
  const fetchTransactions = async () => {
    try {
      setLoading(true);

      // Build query params
      const params = new URLSearchParams();
      params.append('page', pagination.page);
      params.append('limit', pagination.limit);
      if (search) params.append('search', search);
      if (status) params.append('status', status);
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/transactions?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Không thể lấy danh sách giao dịch');
      }

      setTransactions(data.data.transactions);
      setPagination(data.data.pagination);
    } catch (error) {
      console.error('Lỗi khi lấy danh sách giao dịch:', error);
      setError(error.message);
      toast.error(`Lỗi: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, [token, pagination.page, pagination.limit]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchTransactions();
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // View transaction details
  const viewTransactionDetails = (transaction) => {
    setSelectedTransaction(transaction);
    setShowDetailModal(true);
  };

  // Reset filters
  const resetFilters = () => {
    setSearch('');
    setStatus('');
    setStartDate('');
    setEndDate('');
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchTransactions();
  };

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <FaExclamationTriangle className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Đã xảy ra lỗi khi tải dữ liệu
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Get package type display name
  const getPackageTypeName = (type) => {
    switch (type) {
      case '1_week': return '1 tuần';
      case '2_weeks': return '2 tuần';
      case '1_month': return '1 tháng';
      default: return type;
    }
  };

  // Get status display name and color
  const getStatusInfo = (status) => {
    switch (status) {
      case 'completed':
        return { name: 'Thành công', color: 'bg-green-100 text-green-800' };
      case 'pending':
        return { name: 'Đang xử lý', color: 'bg-yellow-100 text-yellow-800' };
      case 'failed':
        return { name: 'Thất bại', color: 'bg-red-100 text-red-800' };
      default:
        return { name: status, color: 'bg-gray-100 text-gray-800' };
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Quản lý giao dịch thanh toán</h1>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <form onSubmit={handleSearch} className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
          <div className="relative">
            <input
              type="text"
              placeholder="Tìm kiếm theo mã giao dịch..."
              className="input input-bordered w-full pr-10"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <button type="submit" className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <FaSearch className="text-gray-400" />
            </button>
          </div>

          <div>
            <select
              className="select select-bordered w-full"
              value={status}
              onChange={(e) => setStatus(e.target.value)}
            >
              <option value="">Tất cả trạng thái</option>
              <option value="completed">Thành công</option>
              <option value="pending">Đang xử lý</option>
              <option value="failed">Thất bại</option>
            </select>
          </div>

          <div>
            <input
              type="date"
              className="input input-bordered w-full"
              placeholder="Từ ngày"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
            />
          </div>

          <div>
            <input
              type="date"
              className="input input-bordered w-full"
              placeholder="Đến ngày"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
            />
          </div>

          <div className="flex space-x-2">
            <button type="submit" className="btn btn-primary flex-1">
              Lọc
            </button>
            <button type="button" onClick={resetFilters} className="btn btn-outline">
              Đặt lại
            </button>
          </div>
        </form>
      </div>

      {/* Transactions table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Mã giao dịch
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Người dùng
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Phòng trọ
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Số tiền
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ngày tạo
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thao tác
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="7" className="px-6 py-4 text-center">
                    <div className="flex justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
                    </div>
                  </td>
                </tr>
              ) : transactions.length === 0 ? (
                <tr>
                  <td colSpan="7" className="px-6 py-4 text-center text-sm text-gray-500">
                    Không tìm thấy giao dịch nào
                  </td>
                </tr>
              ) : (
                transactions.map((transaction) => {
                  const statusInfo = getStatusInfo(transaction.status);
                  return (
                    <tr key={transaction._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {transaction.vnpayTxnRef || transaction._id.substring(0, 10)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {transaction.user?.fullName || 'Không có thông tin'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {transaction.user?.email || ''}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 line-clamp-1">
                          {transaction.room?.title || 'Không có thông tin'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(transaction.amount)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {getPackageTypeName(transaction.packageType)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusInfo.color}`}>
                          {statusInfo.name}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(transaction.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => viewTransactionDetails(transaction)}
                          className="text-indigo-600 hover:text-indigo-900"
                          title="Xem chi tiết"
                        >
                          <FaEye />
                        </button>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {!loading && transactions.length > 0 && (
          <div className="px-6 py-4 bg-white border-t border-gray-200">
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.pages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* Transaction Detail Modal */}
      <Modal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        title="Chi tiết giao dịch"
      >
        {selectedTransaction && (
          <div className="p-6">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Mã giao dịch</p>
                  <p className="mt-1 text-sm text-gray-900">{selectedTransaction.vnpayTxnRef || selectedTransaction._id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Mã giao dịch VNPay</p>
                  <p className="mt-1 text-sm text-gray-900">{selectedTransaction.vnpayTxnNo || 'N/A'}</p>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-500">Người dùng</p>
                <p className="mt-1 text-sm text-gray-900">
                  {selectedTransaction.user?.fullName || 'Không có thông tin'} ({selectedTransaction.user?.email || ''})
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-500">Phòng trọ</p>
                <p className="mt-1 text-sm text-gray-900">{selectedTransaction.room?.title || 'Không có thông tin'}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Số tiền</p>
                  <p className="mt-1 text-sm text-gray-900 font-medium">{formatCurrency(selectedTransaction.amount)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Loại gói</p>
                  <p className="mt-1 text-sm text-gray-900">{getPackageTypeName(selectedTransaction.packageType)}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Trạng thái</p>
                  <p className="mt-1">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusInfo(selectedTransaction.status).color}`}>
                      {getStatusInfo(selectedTransaction.status).name}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Mã phản hồi VNPay</p>
                  <p className="mt-1 text-sm text-gray-900">{selectedTransaction.vnpayResponseCode || 'N/A'}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Ngày tạo</p>
                  <p className="mt-1 text-sm text-gray-900">{formatDate(selectedTransaction.createdAt, true)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Ngày hoàn thành</p>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedTransaction.completedAt ? formatDate(selectedTransaction.completedAt, true) : 'N/A'}
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => setShowDetailModal(false)}
              >
                Đóng
              </button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TransactionManagement;
