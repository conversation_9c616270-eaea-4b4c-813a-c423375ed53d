import { useState } from 'react';
import { Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { FaEnvelope, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaCheckCircle } from 'react-icons/fa';
import { authService } from '../services';

const ResendVerificationPage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [sentEmail, setSentEmail] = useState('');

  const { register, handleSubmit, formState: { errors } } = useForm();

  const onSubmit = async (data) => {
    setIsLoading(true);
    try {
      const response = await authService.resendVerificationEmail(data.email);
      if (response.success) {
        setEmailSent(true);
        setSentEmail(data.email);
        toast.success('Email xác nhận đã được gửi lại thành công!');
      }
    } catch (error) {
      toast.error(error.message || '<PERSON>hông thể gửi lại email xác nhận. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  if (emailSent) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
              <FaCheckCircle className="h-8 w-8 text-green-600" />
            </div>
            
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Email đã được gửi!
            </h2>
            
            <p className="mt-2 text-base text-gray-600">
              Chúng tôi đã gửi lại email xác nhận đến địa chỉ của bạn
            </p>
          </div>

          <div className="mt-8">
            <div className="bg-white py-8 px-6 shadow rounded-lg">
              <div className="text-center space-y-6">
                <div className="flex items-center justify-center">
                  <FaEnvelope className="h-12 w-12 text-blue-500" />
                </div>
                
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Kiểm tra email của bạn
                  </h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Email đã được gửi đến: <span className="font-medium text-gray-900">{sentEmail}</span>
                  </p>
                  <p className="text-sm text-gray-600">
                    Vui lòng kiểm tra hộp thư đến (và cả thư mục spam) để tìm email xác nhận từ chúng tôi.
                    Nhấp vào liên kết trong email để kích hoạt tài khoản của bạn.
                  </p>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <FaEnvelope className="h-5 w-5 text-blue-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-blue-800">
                        Lưu ý
                      </h3>
                      <div className="mt-2 text-sm text-blue-700">
                        <ul className="list-disc list-inside space-y-1">
                          <li>Email có thể mất vài phút để đến</li>
                          <li>Kiểm tra cả thư mục spam/junk mail</li>
                          <li>Liên kết xác nhận sẽ hết hạn sau 24 giờ</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="text-center">
                    <Link
                      to="/login"
                      className="font-medium text-primary hover:text-primary-dark"
                    >
                      Đã xác nhận email? Đăng nhập ngay
                    </Link>
                  </div>

                  <div className="text-center">
                    <button
                      onClick={() => {
                        setEmailSent(false);
                        setSentEmail('');
                      }}
                      className="text-sm text-gray-500 hover:text-gray-700"
                    >
                      Gửi đến email khác
                    </button>
                  </div>

                  <div className="text-center">
                    <Link
                      to="/"
                      className="text-sm text-gray-500 hover:text-gray-700"
                    >
                      ← Quay về trang chủ
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-6">
            <FaEnvelope className="h-8 w-8 text-blue-600" />
          </div>
          
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Gửi lại email xác nhận
          </h2>
          
          <p className="mt-2 text-base text-gray-600">
            Nhập địa chỉ email để nhận lại email xác nhận tài khoản
          </p>
        </div>

        <div className="mt-8">
          <div className="bg-white py-8 px-6 shadow rounded-lg">
            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              <div className="form-group">
                <label htmlFor="email" className="form-label">
                  Địa chỉ email
                </label>
                <input
                  id="email"
                  type="email"
                  autoComplete="email"
                  placeholder="Nhập địa chỉ email đã đăng ký"
                  className="form-input"
                  {...register('email', {
                    required: 'Email là bắt buộc',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Email không hợp lệ',
                    },
                  })}
                />
                {errors.email && (
                  <p className="form-error">{errors.email.message}</p>
                )}
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <FaEnvelope className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Lưu ý quan trọng
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <ul className="list-disc list-inside space-y-1">
                        <li>Chỉ nhập email đã được sử dụng để đăng ký tài khoản</li>
                        <li>Nếu email không tồn tại trong hệ thống, bạn sẽ nhận được thông báo lỗi</li>
                        <li>Email xác nhận mới sẽ thay thế email cũ (nếu có)</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full btn btn-primary"
                >
                  {isLoading ? (
                    <>
                      <FaSpinner className="animate-spin h-4 w-4 mr-2" />
                      Đang gửi...
                    </>
                  ) : (
                    <>
                      <FaEnvelope className="h-4 w-4 mr-2" />
                      Gửi email xác nhận
                    </>
                  )}
                </button>
              </div>

              <div className="text-center space-y-2">
                <div>
                  <Link
                    to="/login"
                    className="font-medium text-primary hover:text-primary-dark"
                  >
                    Đã có tài khoản? Đăng nhập
                  </Link>
                </div>
                
                <div>
                  <Link
                    to="/register"
                    className="font-medium text-primary hover:text-primary-dark"
                  >
                    Chưa có tài khoản? Đăng ký ngay
                  </Link>
                </div>

                <div>
                  <Link
                    to="/"
                    className="text-sm text-gray-500 hover:text-gray-700"
                  >
                    ← Quay về trang chủ
                  </Link>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResendVerificationPage;
