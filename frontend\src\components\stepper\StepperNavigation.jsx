import PropTypes from 'prop-types';
import { FaArrowLeft, FaArrowRight, FaCheck } from 'react-icons/fa';

const StepperNavigation = ({
  currentStep,
  totalSteps,
  onNext,
  onPrev,
  onSubmit,
  isNextDisabled = false,
  isSubmitDisabled = false,
  isLoading = false,
  validateStep = null,
}) => {
  // Xử lý khi nhấn nút Next
  const handleNext = async () => {
    // Nếu có hàm validateStep, gọi hàm này trước khi chuyển bước
    if (validateStep) {
      const isValid = await validateStep(currentStep);
      if (!isValid) return;
    }
    onNext();
  };

  // Xử lý khi nhấn nút Submit
  const handleSubmit = async (e) => {
    // Ngăn chặn hành vi mặc định của form
    e.preventDefault();

    // Nếu có hàm validateStep, gọi hàm này trướ<PERSON> khi submit
    if (validateStep) {
      const isValid = await validateStep(currentStep);
      if (!isValid) return;
    }

    // Gọi hàm onSubmit được truyền từ component cha
    onSubmit();
  };

  return (
    <div className="flex justify-between mt-8">
      {/* Nút Quay lại */}
      {currentStep > 0 ? (
        <button
          type="button"
          onClick={onPrev}
          className="px-4 py-2 flex items-center text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          disabled={isLoading}
        >
          <FaArrowLeft className="mr-2" />
          Quay lại
        </button>
      ) : (
        <div></div> // Placeholder để giữ layout
      )}

      {/* Nút Tiếp theo hoặc Hoàn thành */}
      {currentStep < totalSteps - 1 ? (
        <button
          type="button"
          onClick={handleNext}
          className="px-4 py-2 flex items-center text-white bg-primary rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={isNextDisabled || isLoading}
        >
          Tiếp theo
          <FaArrowRight className="ml-2" />
        </button>
      ) : (
        <button
          type="button"
          onClick={handleSubmit}
          className="px-4 py-2 flex items-center text-white bg-primary rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={isSubmitDisabled || isLoading}
        >
          {isLoading ? (
            <>
              <span className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span>
              Đang xử lý...
            </>
          ) : (
            <>
              Hoàn thành
              <FaCheck className="ml-2" />
            </>
          )}
        </button>
      )}
    </div>
  );
};

StepperNavigation.propTypes = {
  currentStep: PropTypes.number.isRequired,
  totalSteps: PropTypes.number.isRequired,
  onNext: PropTypes.func.isRequired,
  onPrev: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  isNextDisabled: PropTypes.bool,
  isSubmitDisabled: PropTypes.bool,
  isLoading: PropTypes.bool,
  validateStep: PropTypes.func,
};

export default StepperNavigation;
