import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { FaCheck, FaInfoCircle, FaMapMarkerAlt, FaList, FaImage, FaEye } from 'react-icons/fa';

const Stepper = ({ steps, currentStep, onStepClick }) => {
  const [stepStates, setStepStates] = useState([]);

  // Cập nhật trạng thái của các bước dựa trên bước hiện tại
  useEffect(() => {
    const updatedStepStates = steps.map((step, index) => {
      if (index < currentStep) return 'completed';
      if (index === currentStep) return 'current';
      return 'pending';
    });
    setStepStates(updatedStepStates);
  }, [currentStep, steps]);

  // Lấy icon cho từng bước
  const getStepIcon = (step) => {
    switch (step.toLowerCase()) {
      case 'thông tin cơ bản':
        return <FaInfoCircle />;
      case 'địa chỉ':
        return <FaMapMarkerAlt />;
      case 'tiện nghi':
        return <FaList />;
      case 'hình ảnh':
        return <FaImage />;
      case 'xem trước':
        return <FaEye />;
      default:
        return null;
    }
  };

  // Lấy class cho từng bước dựa trên trạng thái
  const getStepClasses = (state) => {
    const baseClasses = 'flex items-center justify-center w-8 h-8 rounded-full transition-colors';
    
    switch (state) {
      case 'completed':
        return `${baseClasses} bg-primary text-white`;
      case 'current':
        return `${baseClasses} bg-primary text-white`;
      default:
        return `${baseClasses} bg-gray-200 text-gray-500`;
    }
  };

  // Lấy class cho đường nối giữa các bước
  const getConnectorClasses = (state) => {
    const baseClasses = 'flex-auto border-t-2 transition-colors';
    
    switch (state) {
      case 'completed':
        return `${baseClasses} border-primary`;
      default:
        return `${baseClasses} border-gray-200`;
    }
  };

  return (
    <div className="mb-8">
      <div className="flex items-center">
        {steps.map((step, index) => (
          <div key={step} className="flex items-center flex-1">
            {/* Bước */}
            <button
              onClick={() => onStepClick && onStepClick(index)}
              disabled={stepStates[index] === 'pending'}
              className={`
                ${getStepClasses(stepStates[index])}
                ${onStepClick ? 'cursor-pointer hover:opacity-90' : ''}
                ${stepStates[index] === 'pending' ? 'cursor-not-allowed' : ''}
              `}
              aria-current={stepStates[index] === 'current' ? 'step' : undefined}
            >
              {stepStates[index] === 'completed' ? (
                <FaCheck className="w-4 h-4" />
              ) : (
                getStepIcon(step) || (index + 1)
              )}
            </button>

            {/* Tiêu đề bước */}
            <div className="ml-2 hidden sm:block">
              <div
                className={`text-sm font-medium ${
                  stepStates[index] === 'current' ? 'text-primary' : 
                  stepStates[index] === 'completed' ? 'text-gray-900' : 'text-gray-500'
                }`}
              >
                {step}
              </div>
            </div>

            {/* Đường nối giữa các bước */}
            {index < steps.length - 1 && (
              <div className={`${getConnectorClasses(stepStates[index])} mx-4`}></div>
            )}
          </div>
        ))}
      </div>
      
      {/* Hiển thị tiêu đề bước hiện tại trên mobile */}
      <div className="mt-2 text-center sm:hidden">
        <div className="text-sm font-medium text-primary">
          {steps[currentStep]}
        </div>
      </div>
    </div>
  );
};

Stepper.propTypes = {
  steps: PropTypes.arrayOf(PropTypes.string).isRequired,
  currentStep: PropTypes.number.isRequired,
  onStepClick: PropTypes.func,
};

export default Stepper;
