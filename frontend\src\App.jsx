import { Routes, Route, Navigate, Outlet } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useAuthContext } from './contexts';
import { setupVNPayErrorFiltering } from './utils/vnpay-patch';
import { useEffect } from 'react';
import {
  HomePage,
  AboutPage,
  LoginPage,
  RegisterPage,
  RegistrationSuccessPage,
  EmailVerificationPage,
  ResendVerificationPage,
  ForgotPasswordPage,
  ResetPasswordPage,
  PasswordResetSuccessPage,
  SearchPage,
  RoomDetailPage,
  CreateRoomPage,
  EditRoomPage,
  ProfilePage,
  MyRoomsPage,
  RoomStatsPage,
  UpgradeRoomPage,
  PaymentResultPage,
  NotFound404,
} from './pages';
import {
  AdminLayout,
  Dashboard,
  UserManagement,
  RoomManagement,
  TransactionManagement,
  CategoryManagement,
  AmenityManagement
} from './pages/admin';
import { Header, Footer } from './components';

// Protected Route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthContext();

  // Nếu đang tải, hiển thị loading
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  // Nếu chưa đăng nhập, chuyển hướng đến trang đăng nhập
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Nếu đã đăng nhập, hiển thị component con
  return children;
};

// Admin Route component
const AdminRoute = ({ children }) => {
  const { user, isAuthenticated, isLoading } = useAuthContext();

  // Nếu đang tải, hiển thị loading
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  // Nếu chưa đăng nhập hoặc không phải admin, chuyển hướng đến trang đăng nhập
  if (!isAuthenticated || user?.role !== 'admin') {
    return <Navigate to="/login" replace />;
  }

  // Nếu đã đăng nhập và là admin, hiển thị component con
  return children;
};

// Layout component for main routes
const MainLayout = () => {
  return (
    <>
      <Header />
      <main className="flex-grow">
        <Outlet />
      </main>
      <Footer />
    </>
  );
};

function App() {
  // Setup VNPay error filtering on app initialization
  useEffect(() => {
    setupVNPayErrorFiltering();
  }, []);

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 text-gray-900">
      <Routes>
        {/* Admin routes */}
        <Route
          path="/admin"
          element={
            <AdminRoute>
              <AdminLayout />
            </AdminRoute>
          }
        >
          <Route index element={<Dashboard />} />
          <Route path="users" element={<UserManagement />} />
          <Route path="rooms" element={<RoomManagement />} />
          <Route path="transactions" element={<TransactionManagement />} />
          <Route path="categories" element={<CategoryManagement />} />
          <Route path="amenities" element={<AmenityManagement />} />
          <Route path="*" element={<NotFound404 />} />
        </Route>

        {/* Main layout with header and footer */}
        <Route element={<MainLayout />}>
          {/* Public routes */}
          <Route path="/" element={<HomePage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route path="/registration-success" element={<RegistrationSuccessPage />} />
          <Route path="/verify-email/:token" element={<EmailVerificationPage />} />
          <Route path="/resend-verification" element={<ResendVerificationPage />} />
          <Route path="/forgot-password" element={<ForgotPasswordPage />} />
          <Route path="/reset-password/:token" element={<ResetPasswordPage />} />
          <Route path="/reset-password-success" element={<PasswordResetSuccessPage />} />
          <Route path="/search" element={<SearchPage />} />
          <Route path="/rooms/:id" element={<RoomDetailPage />} />


          {/* Protected routes */}
          <Route
            path="/rooms/create"
            element={
              <ProtectedRoute>
                <CreateRoomPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/rooms/edit/:id"
            element={
              <ProtectedRoute>
                <EditRoomPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <ProfilePage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/my-rooms"
            element={
              <ProtectedRoute>
                <MyRoomsPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/rooms/stats/:id"
            element={
              <ProtectedRoute>
                <RoomStatsPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/rooms/upgrade/:id"
            element={
              <ProtectedRoute>
                <UpgradeRoomPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/payment-result"
            element={
              <ProtectedRoute>
                <PaymentResultPage />
              </ProtectedRoute>
            }
          />

          {/* 404 Not Found route */}
          <Route path="*" element={<NotFound404 />} />
        </Route>
      </Routes>

      {/* Toast notifications */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />
    </div>
  );
}

export default App;
